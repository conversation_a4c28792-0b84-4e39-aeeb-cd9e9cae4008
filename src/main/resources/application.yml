info:
  name: ${spring.application.name}
  os: ${os.name}
  version: "@project.version@"
  instance: ${eureka.instance.instance-id}
logging:
  file:
    path: "${user.home}/logs/${spring.application.name}/${spring.application.name}.log"
server:
  port: 8080
feign:
  circuitbreaker:
    enabled: true
management:
  security:
    enabled: true
  opentelemetry:
    resource-attributes:
      service.name: ${spring.application.name} # 服务名称
      service.version: 1.0.0
      service.namespace: caifa
      deployment.environment: ${spring.profiles.active} # 部署环境，dev、test、pre、real，用于区分不同环境的服务