<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <property name="LOG_PATH" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}"/>
  <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(traceId=%X{trace_id:-}){yellow} %clr(spanId=%X{span_id:-}){yellow} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

  <!--console彩色日志-->
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

  <!--dian-logger日志，下面的DIAN_MONITOR_SURPLUS和DIAN_MONITOR-->
  <include resource="included-config-otel.xml"/>

  <!--去掉Logback 解析配置文件和初始化过程中产生的多余日志No-operation Status Listener-->
  <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

  <!--注入traceId和spanId，输出到CONSOLE和DIAN_MONITOR_SURPLUS日志监控-->
  <appender name="OpenTelemetry"
            class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">
    <appender-ref ref="CONSOLE"/>
  </appender>

  <root level="INFO">
    <appender-ref ref="OpenTelemetry"/>
    <appender-ref ref="DIAN_MONITOR_SURPLUS"/>
  </root>
</configuration>
