package so.dian.fis.settle.common.util.biz;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import so.dian.agent.api.constant.AgentBaseEnum.AgentTypeEnum;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * MainBizTypeUtils
 *
 * <AUTHOR>
 */
public class MainBizTypeUtils {

    private static final List<Integer> MERCHANT_TYPE_LIST = Lists.newArrayList(MainBizTypeEnum.NORMAL_MERCHANT.getCode(),
            MainBizTypeEnum.BRAND_MERCHANT.getCode(), MainBizTypeEnum.JOIN_MERCHANT.getCode());

    private static final List<Integer> AGENT_TYPE_LIST = Lists.newArrayList(MainBizTypeEnum.AGENT.getCode(),
            MainBizTypeEnum.OPEN_SERVICE.getCode(), MainBizTypeEnum.OPERATION_SERVICE.getCode(),
            MainBizTypeEnum.RESOURCE_SERVICE.getCode(),
            MainBizTypeEnum.JV_CORP.getCode()
            );

    public static final Map<Integer, Integer> MAIN_BIZ_TYPE_TO_AGENT_TYPE = ImmutableMap.of(
            MainBizTypeEnum.AGENT.getCode(),AgentTypeEnum.AGENT_TYPE.getId(),
            MainBizTypeEnum.OPEN_SERVICE.getCode(), AgentTypeEnum.BD_AGENT_TYPE.getId(),
            MainBizTypeEnum.RESOURCE_SERVICE.getCode(),AgentTypeEnum.RP_AGENT_TYPE.getId(),
            MainBizTypeEnum.OPERATION_SERVICE.getCode(), AgentTypeEnum.OP_AGENT_TYPE.getId()
    );

    private static final List<Integer> IOT_MERCHANT_TYPE_LIST = Lists.newArrayList(MainBizTypeEnum.IOT_MERCHANT.getCode());

    public static boolean isMerchant(Integer mainBizType) {
        return MERCHANT_TYPE_LIST.contains(mainBizType);
    }

    public static boolean isAgent(Integer mainBizType) {
        return AGENT_TYPE_LIST.contains(mainBizType);
    }

    public static boolean isIotMerchant(Integer mainBizType) {
        return IOT_MERCHANT_TYPE_LIST.contains(mainBizType);
    }

    public static Integer getAgentTypeByMainBizType(Integer mainBizType) {
        if(Objects.isNull(mainBizType)) {
            return null;
        }
        return MAIN_BIZ_TYPE_TO_AGENT_TYPE.get(mainBizType);
    }

}
