package so.dian.fis.settle.common.util;

import jakarta.servlet.http.HttpServletRequest;
import so.dian.zuul.client.model.constant.RequestHeaderConstants;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * @Author: ahuang
 * @CreateTime: 2024-10-31 16:19
 * @Description:
 */
public class ZHeaderUtils {
    public static Long getUserId(HttpServletRequest request) {
        String userId = request.getHeader(RequestHeaderConstants.X_Z_USER_ID);
        if (userId == null || userId.isEmpty()) {
            return null;
        }
        return Long.valueOf(userId);
    }

    /**
     * 获取当前角色Code
     */
    public static String getRoleCode(HttpServletRequest request) {
        String roleCode = request.getHeader(RequestHeaderConstants.X_Z_ROLE_CODE);
        if (roleCode == null || roleCode.isEmpty()) {
            return null;
        }
        return roleCode;
    }

    /**
     * 获取当前角色ID
     */
    public static Long getRoleId(HttpServletRequest request) {
        String roleId = request.getHeader(RequestHeaderConstants.X_Z_ROLE_ID);
        if (roleId == null || roleId.isEmpty()) {
            return null;
        }
        return Long.valueOf(roleId);
    }

    /**
     * 获取用户昵称
     *
     * @apiNote 解码失败直接返回null
     */
    public static String getNickName(HttpServletRequest request) {
        String nickName = request.getHeader(RequestHeaderConstants.X_Z_NICK_NAME);
        if (nickName == null || nickName.isEmpty()) {
            return null;
        }
        String decoded = null;
        try {
            decoded = URLDecoder.decode(nickName, "utf-8");
        } catch (UnsupportedEncodingException e) {
            //ignored
        }
        return decoded;
    }
}
