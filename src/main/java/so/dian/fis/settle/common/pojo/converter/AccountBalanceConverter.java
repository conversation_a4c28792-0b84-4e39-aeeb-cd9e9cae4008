package so.dian.fis.settle.common.pojo.converter;

import so.dian.common.logger.util.StringUtils;
import so.dian.fis.settle.controller.account.response.AccountBalancePcRsp;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.lvy.pojo.dto.AccountBalanceDTO;
import so.dian.rick.merchant.client.enums.AccountAmountOpTypeEnum;
import so.dian.rick.merchant.client.merchant.resp.MerchantBalanceResp;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * AccountBalanceConverter
 *
 * <AUTHOR>
 */
public class AccountBalanceConverter {

    public static List<AccountBalancePcRsp> buildPcRsp(List<AccountBalanceDTO> balanceDTOList) {
        return LocalListUtils.transferList(balanceDTOList, AccountBalanceConverter::buildPcRsp);
    }

    public static AccountBalancePcRsp buildPcRsp(AccountBalanceDTO balanceDTO) {
        AccountBalancePcRsp result = new AccountBalancePcRsp();
        result.setId(balanceDTO.getId());
        result.setBalanceTime(balanceDTO.getBalanceTime());
        result.setBalanceType(balanceDTO.getBalanceType());
        result.setBalanceTypeName(balanceDTO.getBalanceTypeName());
        result.setBalanceAmount(balanceDTO.getBalanceAmount());
        result.setBizDocType(balanceDTO.getBizDocType());
        result.setBizDocTypeName(balanceDTO.getBizDocTypeName());
        result.setBizDocNo(balanceDTO.getBizDocNo());
        result.setBalanceNo(balanceDTO.getBalanceNo());
        result.setSettleSubjectId(balanceDTO.getSettleSubjectId());
        result.setSettleSubjectType(balanceDTO.getSettleSubjectType());
        return result;
    }

    public static List<AccountBalancePcRsp> buildPcNewRsp(List<MerchantBalanceResp> list) {
        return LocalListUtils.transferList(list, AccountBalanceConverter::buildPcNewRsp);
    }

    public static AccountBalancePcRsp buildPcNewRsp(MerchantBalanceResp merchantBalanceResp) {
        AccountBalancePcRsp result = new AccountBalancePcRsp();
        result.setBalanceTime(merchantBalanceResp.getBizDate() == null ? new Date() : new Date(merchantBalanceResp.getBizDate()));
        result.setBalanceType(merchantBalanceResp.getAmountOpType());
        result.setBalanceTypeName(Optional.ofNullable(AccountAmountOpTypeEnum.ofCode(merchantBalanceResp.getAmountOpType()))
                .map(AccountAmountOpTypeEnum::getDesc)
                .orElse("未知类型"));
        result.setBalanceAmount(Math.abs(merchantBalanceResp.getChangeAmount()));
        result.setBizDocType(merchantBalanceResp.getBizOpType());
        result.setBizDocTypeName(merchantBalanceResp.getBizOpTypeDesc());
        result.setBizDocNo(merchantBalanceResp.getOrderNo());
        result.setBalanceNo(merchantBalanceResp.getId().toString());
        result.setSettleSubjectId(StringUtils.isEmpty(merchantBalanceResp.getAccountDimensionFactor3()) ? null : Long.valueOf(merchantBalanceResp.getAccountDimensionFactor3()));
        result.setSettleSubjectType(merchantBalanceResp.getAccountDimensionFactor1());
        return result;
    }
}
