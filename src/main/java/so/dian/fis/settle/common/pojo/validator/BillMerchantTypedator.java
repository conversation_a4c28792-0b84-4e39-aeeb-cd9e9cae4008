package so.dian.fis.settle.common.pojo.validator;

import com.google.common.collect.Lists;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;

import java.util.List;

/**
 * BillValidator
 *
 * <AUTHOR>
 */
public class BillMerchantTypedator {

    private final static List<Integer> VALID_MANUAL_BILL_BIZ_TYPE = Lists
            .newArrayList(MainBizTypeEnum.AGENT.getCode(), MainBizTypeEnum.OPERATION_SERVICE.getCode(), MainBizTypeEnum.JV_CORP.getCode());

    public static boolean isValidManualBillBizType(Integer bizType) {
        return VALID_MANUAL_BILL_BIZ_TYPE.contains(bizType);
    }
}
