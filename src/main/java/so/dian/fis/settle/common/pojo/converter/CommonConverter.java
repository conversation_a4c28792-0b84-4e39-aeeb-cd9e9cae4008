package so.dian.fis.settle.common.pojo.converter;

import so.dian.commons.eden.entity.PageResult;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;

import java.util.List;
import java.util.Objects;

/**
 * CommonConverter
 *
 * <AUTHOR>
 */
public class CommonConverter {

    public static <T, V> BizResult<PageData<T>> convertPageResultToBizResult(PageResult<V> pageResult, List<T> data) {
        if (pageResult.isSuccess()) {
            Long total = null;
            if (Objects.nonNull(pageResult.getTotalCount())) {
                total = pageResult.getTotalCount().longValue();
            }
            return BizResult.create(PageData.create(data, total));
        }
        return BizResult.error(pageResult.getCode().toString(), pageResult.getMsg());
    }

}
