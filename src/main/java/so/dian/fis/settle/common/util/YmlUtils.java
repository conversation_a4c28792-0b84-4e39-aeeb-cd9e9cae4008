package so.dian.fis.settle.common.util;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.yaml.snakeyaml.Yaml;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.Map;
import java.util.Objects;

/**
 * YmlUtil
 *
 * <AUTHOR>
 * @desc yml获取工具类
 * @date 2019-05-20
 */
@Slf4j
public class YmlUtils {

    public final static String APPLICATION_FILE_NAME = "application.yml";
    public final static String BOOTSTRAP_FILE_NAME = "bootstrap.yml";
    public final static String APPLICATION_ENV_FILE_NAME = "application_{0}.yml";

    private static Map<String, Map<String, String>> ymlMaps = Maps.newHashMap();

    static {
        loadYml(APPLICATION_FILE_NAME);
        loadYml(BOOTSTRAP_FILE_NAME);
    }

    /**
     * 根据文件名获取yml的文件内容
     */
    synchronized static private void loadYml(String fileName) {
        log.debug("开始加载yml文件内容.......");
        Map<String, String> yml = MapUtil.isNotEmpty(ymlMaps) ? ymlMaps.get(fileName) : Maps.newHashMap();
        if (Objects.isNull(yml)) {
            yml = Maps.newHashMap();
        }
        InputStream in = null;
        try {
            in = YmlUtils.class.getClassLoader().getResourceAsStream(fileName);
            Yaml props = new Yaml();
            Object obj = props.loadAs(in, Map.class);
            Map<String, Object> param = (Map<String, Object>) obj;
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                String key = entry.getKey();
                Object val = entry.getValue();
                if (val instanceof Map) {
                    forEachYaml(yml, key, (Map<String, Object>) val);
                } else {
                    yml.put(key, val.toString());
                }
            }
            ymlMaps.put(fileName, yml);
        } finally {
            try {
                if (null != in) {
                    in.close();
                }
            } catch (IOException e) {
                log.error("{}文件流关闭出现异常", fileName);
            }
        }
        log.debug("加载yml文件内容完成...........");
        log.debug("properties文件内容：" + yml);
    }

    public static String getValue(String key) {
        if (StringUtils.isBlank(key)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        if (MapUtils.isEmpty(ymlMaps) || Objects.isNull(ymlMaps.get(APPLICATION_FILE_NAME))) {
            loadYml(APPLICATION_FILE_NAME);
        }
        return ymlMaps.get(APPLICATION_FILE_NAME).get(key);
    }

    public static String getValue(String key, String defaultValue) {
        if (StringUtils.isBlank(key)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        if (MapUtils.isEmpty(ymlMaps) || Objects.isNull(ymlMaps.get(APPLICATION_FILE_NAME))) {
            loadYml(APPLICATION_FILE_NAME);
        }
        return ymlMaps.get(APPLICATION_FILE_NAME).getOrDefault(key, defaultValue);
    }

    public static String getEnvValue(String env, String key) {
        return getFileValue(key, MessageFormat.format(APPLICATION_ENV_FILE_NAME, env));
    }

    public static String getEnvValue(String env, String key, String defaultValue) {
        return getFileValue(key, MessageFormat.format(APPLICATION_ENV_FILE_NAME, env), defaultValue);
    }

    public static String getFileValue(String key, String fileName) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(fileName)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        if (MapUtils.isEmpty(ymlMaps) || Objects.isNull(ymlMaps.get(fileName))) {
            loadYml(fileName);
        }
        return ymlMaps.get(fileName).get(key);
    }

    public static String getFileValue(String key, String fileName, String defaultValue) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(fileName)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        if (MapUtils.isEmpty(ymlMaps) || Objects.isNull(ymlMaps.get(fileName))) {
            loadYml(fileName);
        }
        return ymlMaps.get(fileName).getOrDefault(key, defaultValue);
    }

    /**
     * 遍历yml文件，获取map集合
     */
    private static void forEachYaml(Map<String, String> yml, String keyStr, Map<String, Object> obj) {
        for (Map.Entry<String, Object> entry : obj.entrySet()) {
            String key = entry.getKey();
            Object val = entry.getValue();
            String strNew;
            if (StringUtils.isNotBlank(keyStr)) {
                strNew = keyStr + "." + key;
            } else {
                strNew = key;
            }
            if (val instanceof Map) {
                forEachYaml(yml, strNew, (Map<String, Object>) val);
            } else {
                yml.put(strNew, val.toString());
            }
        }
    }
}