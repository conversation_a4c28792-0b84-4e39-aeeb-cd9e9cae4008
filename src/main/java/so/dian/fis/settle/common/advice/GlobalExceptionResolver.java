package so.dian.fis.settle.common.advice;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.util.ContentCachingRequestWrapper;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.enums.error.HttpCodeEnum;
import so.dian.himalaya.common.exception.BizException;

import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一异常处理
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionResolver {

    private static final Logger bizLog = LoggerFactory.getLogger("biz");

    private static final String FORMAT_PARAM_INVALID = "[{0}:{1}]";
    private static final String FORMAT_PARAMS_ERROR_MSG = "参数转换错误：{0}={1} 值不存在或格式不正确！";
    private static final int MAX_BODY_LOG_LENGTH = 1000;

    /**
     * 拼接参数验证 @Valid 错误信息
     *
     * @param bindingResult 绑定结果
     */
    private static StringBuilder joinErrorMsg(BindingResult bindingResult) {
        StringBuilder msg = new StringBuilder();
        bindingResult.getFieldErrors().forEach(err -> msg.append(
                MessageFormat.format(FORMAT_PARAM_INVALID, err.getField(), err.getDefaultMessage())));
        return msg;
    }

    /**
     * 获取请求体内容，已缓存，避免多次读取引发的异常
     *
     * @param request HttpServletRequest 请求对象
     * @return 请求体内容（适当截断，避免内容过长）
     */
    private static String getRequestBody(HttpServletRequest request) {
        if (request instanceof ContentCachingRequestWrapper wrapper) {
            byte[] content = wrapper.getContentAsByteArray();
            if (content.length == 0) return "No request body content";

            String requestBody = new String(content, StandardCharsets.UTF_8).replaceAll("\\s+", " ");
            // 截断请求体内容，防止日志过长
            return requestBody.length() > MAX_BODY_LOG_LENGTH ? requestBody.substring(0, MAX_BODY_LOG_LENGTH) + "...[truncated]" : requestBody;
        }
        return "Request body not available";
    }

    /**
     * 获取请求参数
     *
     * @param request HttpServletRequest 请求对象
     * @return JSON 格式的请求参数
     */
    private static String getRequestParams(HttpServletRequest request) {
        try {
            Map<String, String[]> parameterMap = request.getParameterMap();
            Map<String, String> params = new HashMap<>();
            parameterMap.forEach((key, value) -> params.put(key, String.join(",", value)));
            return JSON.toJSONString(params);
        } catch (Exception e) {
            log.error("Failed to get request parameters", e);
            return "Error reading request parameters";
        }
    }

    /**
     * 通用日志记录方法，包含空值检查和日志内容截断
     */
    private void logError(String message, HttpServletRequest request, Exception ex) {
        String url = request.getRequestURL() != null ? request.getRequestURL().toString() : "URL not available";
        String params = getRequestParams(request);
        String body = getRequestBody(request);

        log.error("{}, url: {}, params: {}, body: {}, exception: {}",
                message, url, params, body, ex.getClass().getSimpleName(), ex);
    }

    /**
     * 处理 JSON 格式错误的异常
     */
    @ExceptionHandler(InvalidFormatException.class)
    @ResponseBody
    public BizResult<Integer> resolveException(HttpServletRequest request, InvalidFormatException e) {
        logError("Invalid format exception", request, e);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR);
    }

    /**
     * 全局异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public BizResult defaultErrorHandler(HttpServletRequest request, Exception ex) {
        logError("Unhandled exception", request, ex);
        return BizResult.error(HttpCodeEnum.INTERNAL_SERVER_ERROR);
    }

    /**
     * 业务异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public BizResult bizExceptionHandler(HttpServletRequest request, BizException bizExp) {
        String traceInfo = "No stack trace available";
        StackTraceElement[] stackTrace = bizExp.getStackTrace();
        if (stackTrace.length > 1) {
            traceInfo = stackTrace[0] + " | " + stackTrace[1];
        }
        bizLog.error("Business exception, message: {}, url: {}, params: {}, body: {}, trace: {}",
                bizExp.getMsg(), request.getRequestURL(), getRequestParams(request), getRequestBody(request), traceInfo);
        return BizResult.error(bizExp);
    }

    /**
     * 参数不合法异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = IllegalArgumentException.class)
    public BizResult illegalArgumentExceptionHandler(HttpServletRequest request, IllegalArgumentException illArgExp) {
        logError("Illegal argument exception", request, illArgExp);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR, illArgExp.getMessage());
    }

    /**
     * 参数类型转换异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public BizResult methodArgumentTypeMismatchExceptionHandler(HttpServletRequest request,
                                                                MethodArgumentTypeMismatchException converterExp) {
        String msg = MessageFormat.format(FORMAT_PARAMS_ERROR_MSG, converterExp.getName(), converterExp.getValue());
        logError("Method argument type mismatch", request, converterExp);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR, msg);
    }

    /**
     * 参数绑定异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = BindException.class)
    public BizResult bindExceptionHandler(HttpServletRequest request, BindException bindExp) {
        StringBuilder msg = joinErrorMsg(bindExp.getBindingResult());
        logError("Bind exception", request, bindExp);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR, msg.toString());
    }

    /**
     * 请求体参数验证异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public BizResult methodArgumentNotValidExceptionHandler(HttpServletRequest request,
                                                            MethodArgumentNotValidException notValidExp) {
        StringBuilder msg = joinErrorMsg(notValidExp.getBindingResult());
        logError("Method argument not valid", request, notValidExp);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR, msg.toString());
    }

    /**
     * Servlet 异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = ServletException.class)
    public BizResult servletExceptionHandler(HttpServletRequest request, ServletException servletExp) {
        logError("Servlet exception", request, servletExp);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR, servletExp.getLocalizedMessage());
    }

    /**
     * HTTP 消息不可读异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public BizResult httpMessageNotReadableExceptionHandler(HttpServletRequest request,
                                                            HttpMessageNotReadableException httpExp) {
        logError("HTTP message not readable", request, httpExp);
        return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR, httpExp.getLocalizedMessage());
    }
}