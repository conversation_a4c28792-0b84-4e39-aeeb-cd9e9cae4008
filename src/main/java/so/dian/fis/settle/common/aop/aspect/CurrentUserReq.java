package so.dian.fis.settle.common.aop.aspect;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p> 基础请求体 </p>
 *
 * <AUTHOR>
 * @date 2019-12-06 17:06
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurrentUserReq implements Serializable {

    @ApiModelProperty("当前角色")
    protected String currentRole;

    @ApiModelProperty("操作人ID")
    protected Long userId;

    @ApiModelProperty("操作人名称")
    protected String userName;

    @ApiModelProperty("操作人邮箱")
    protected String userMail;

    @ApiModelProperty("结算方类型")
    protected Integer settleSubjectType;

    @ApiModelProperty("结算方ID")
    protected Long settleSubjectId;

    private Integer agentSubType;

    private Long roleId;

    public void from(CurrentUserReq req) {
        if (Objects.isNull(req)) {
            return;
        }
        this.userId = req.userId;
        this.userName = req.getUserName();
        this.userMail = req.getUserMail();
        this.settleSubjectType = req.getSettleSubjectType();
        this.settleSubjectId = req.getSettleSubjectId();
        this.currentRole = req.getCurrentRole();
    }
}
