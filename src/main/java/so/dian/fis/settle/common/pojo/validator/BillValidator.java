package so.dian.fis.settle.common.pojo.validator;

import com.google.common.collect.Lists;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * BillValidator
 *
 * <AUTHOR>
 */
public class BillValidator {

    private final static List<Integer> VALID_MANUAL_BILL_BIZ_TYPE = Lists
            .newArrayList(AccountBillBizTypeEnum.订单分成.getCode(), AccountBillBizTypeEnum.提现.getCode(),
                    AccountBillBizTypeEnum.商户分成扣款.getCode(), AccountBillBizTypeEnum.设备休眠扣款.getCode(),
                    AccountBillBizTypeEnum.设备流动扣款.getCode(), AccountBillBizTypeEnum.其他进场费用摊销.getCode(),
                    AccountBillBizTypeEnum.分成预付.getCode(), AccountBillBizTypeEnum.测试.getCode(),
                    AccountBillBizTypeEnum.清零.getCode(),
                    AccountBillBizTypeEnum.失效冻结.getCode(),
                    AccountBillBizTypeEnum.授信还款退款.getCode(),
                    AccountBillBizTypeEnum.代运维费用.getCode(),
                    AccountBillBizTypeEnum.代付商户分成.getCode(),
                    AccountBillBizTypeEnum.个代商户分成.getCode(),
                    AccountBillBizTypeEnum.存量进场费结算.getCode(),
                    AccountBillBizTypeEnum.存量押金结算.getCode(),
                    AccountBillBizTypeEnum.存量预付分成结算.getCode(),
                    AccountBillBizTypeEnum.遗失设备扣款.getCode(),
                    AccountBillBizTypeEnum.存量设备结算款.getCode(),
                    AccountBillBizTypeEnum.新设备结算款.getCode(),
                    AccountBillBizTypeEnum.设备退货退款结算.getCode(),
                    AccountBillBizTypeEnum.新签进场费代付.getCode(),
                    AccountBillBizTypeEnum.不规范建店结算.getCode(),
                    AccountBillBizTypeEnum.客服端退款订单结算.getCode(),
                    AccountBillBizTypeEnum.合作伙伴收入_应分未分.getCode(),
                    AccountBillBizTypeEnum.廉政数据.getCode(),
                    AccountBillBizTypeEnum.代付商户分成.getCode(),
                    AccountBillBizTypeEnum.代付代理商商户分成.getCode(),
                    AccountBillBizTypeEnum.一代资源方收入.getCode(),
                    AccountBillBizTypeEnum.一代设备代扣.getCode(),
                    AccountBillBizTypeEnum.激励分成.getCode(),
                    AccountBillBizTypeEnum.二代为一代代付商家分成.getCode(),
                    AccountBillBizTypeEnum.设备返利金额.getCode(),
                    AccountBillBizTypeEnum.多宝摊销扣款.getCode(),
                    AccountBillBizTypeEnum.充电宝遗失补差.getCode()
            );

    private final static List<Integer> OLD_VALID_MANUAL_BILL_BIZ_TYPE = Lists
            .newArrayList(AccountBillBizTypeEnum.订单分成.getCode(), AccountBillBizTypeEnum.提现.getCode(),
                    AccountBillBizTypeEnum.商户分成扣款.getCode(), AccountBillBizTypeEnum.设备休眠扣款.getCode(),
                    AccountBillBizTypeEnum.设备流动扣款.getCode(), AccountBillBizTypeEnum.其他进场费用摊销.getCode(),
                    AccountBillBizTypeEnum.分成预付.getCode(), AccountBillBizTypeEnum.测试.getCode(),
                    AccountBillBizTypeEnum.清零.getCode(),
                    AccountBillBizTypeEnum.失效冻结.getCode()
            );

    public static boolean isValidManualBillBizType(Integer bizType) {
        return VALID_MANUAL_BILL_BIZ_TYPE.contains(bizType);
    }

    public static boolean isOldValidManualBillBizType(Integer bizType) {
        return OLD_VALID_MANUAL_BILL_BIZ_TYPE.contains(bizType);
    }

    public static boolean validAgentDirectBizType(Integer bizType, Integer mainBizType) {


        boolean contains = Arrays.asList(AccountBillBizTypeEnum.一代设备代扣.getCode(), AccountBillBizTypeEnum.一代资源方收入.getCode(),AccountBillBizTypeEnum.激励分成.getCode(),AccountBillBizTypeEnum.二代为一代代付商家分成.getCode(),AccountBillBizTypeEnum.设备返利金额.getCode(),AccountBillBizTypeEnum.多宝摊销扣款.getCode(),AccountBillBizTypeEnum.充电宝遗失补差.getCode()).contains(bizType);
        if (!contains) {
            return true;
        }
        return Objects.equals(mainBizType, MainBizTypeEnum.AGENT.getCode()) || Objects.equals(mainBizType, MainBizTypeEnum.JV_CORP.getCode());
    }
}
