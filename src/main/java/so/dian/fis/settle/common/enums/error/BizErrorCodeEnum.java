package so.dian.fis.settle.common.enums.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.ErrorCodeInterface;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;

/**
 * <p> 异常枚举 </p>
 * <p>业务线编码 + 5位数字 </p>
 * <p>F10000-F10999</p>
 *
 * <AUTHOR>
 * @date 2019-12-05 16:44
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum BizErrorCodeEnum implements ErrorCodeInterface<BaseErrorCodeEnum> {

    //用户相关
    USER_NOT_LOGIN("F10001", "用户未登录"),
    USER_NOT_EXIST("F10002", "用户不存在"),
    USER_UNAUTHORIZED("F10003", "用户无权访问"),
    USER_DATA_ERROR("F10004", "用户数据异常"),
    SETTLE_SUBJECT_NOT_NULL("F10005", "结算方不能为空"),
    USER_MAIL_NOT_NULL("F10006", "收件人邮箱不存在，请更新邮箱后重试"),
    MAX_LIMIT_1000("F10007", "超过1千条限制"),
    ACCOUNT_NEW("F10008", "账户已迁移新账户"),
    ;

    private String code;
    private String desc;
}
