package so.dian.fis.settle.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.EnumInterface;

@Getter
@AllArgsConstructor
public enum ConstEnum implements EnumInterface<ConstEnum> {

    DEFAULT(-1, ""),

    //下拉框
    JV_COMPANY(-1, "合资公司"),
    //下拉框
    PERSONAL_AGENT(-2, "个人代理商"),

    ;
    private Integer code;
    private String desc;

    @Override
    public ConstEnum getDefault() {
        return null;
    }
}
