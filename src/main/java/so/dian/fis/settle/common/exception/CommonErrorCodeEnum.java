package so.dian.fis.settle.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.ErrorCodeInterface;

@Getter
@AllArgsConstructor
public enum CommonErrorCodeEnum implements ErrorCodeInterface<CommonErrorCodeEnum> {
    //自定义异常
    NO_PERMISSION("F15000", "操作无权限"),
    RECORD_NOT_EXISTED("F15001", "记录不存在"),
    ;

    private String code;
    private String desc;
}
