package so.dian.fis.settle.common.constant;

import com.google.common.collect.ImmutableMap;
import java.util.Map;
import so.dian.agent.api.constant.AgentBaseEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;

public class CommonConstants {

    /**
     * 代理商表.代理商类型-结算方类型枚举映射 key:代理商表.代理商类型 value:结算方类型
     */
    public final static Map<Integer, Integer> AGENT_SETTLE_TYPE_MAPPING = ImmutableMap.of(
            AgentBaseEnum.AgentTypeEnum.AGENT_TYPE.getId(), SettleSubjectTypeEnum.AGENT.getCode(),
            AgentBaseEnum.AgentTypeEnum.OP_AGENT_TYPE.getId(), SettleSubjectTypeEnum.OPERATION_SERVICE.getCode(),
            AgentBaseEnum.AgentTypeEnum.JV_COMPANY_TYPE.getId(), SettleSubjectTypeEnum.JV_CORP.getCode()
    );

    public final static String BATCH_ADD_BILL_WHITE_LIST_CACHE_KEY = "fis-settle:batch_add_bill";

    public final static String USER_LOGIN_INFO = "fis-settle:user:login";

    //获取当前账户手动冻结金额总和，操作类型写死
    public final static Integer BIZ_OP_TYPE = 20042;

}
