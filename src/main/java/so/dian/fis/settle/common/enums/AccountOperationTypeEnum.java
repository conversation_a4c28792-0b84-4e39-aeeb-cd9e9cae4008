package so.dian.fis.settle.common.enums;

/**
 * @Author: ahuang
 * @CreateTime: 2024-11-28 11:40
 * @Description:
 */
public enum AccountOperationTypeEnum {

    /**
     * 账户冻结
     */
    ACCOUNT_FREEZE(1, "账户冻结"),

    /**
     * 账户解冻
     */
    ACCOUNT_UNFREEZE(2, "账户解冻"),

    /**
     * 金额冻结
     */
    AMOUNT_FREEZE(3, "金额冻结"),

    /**
     * 金额解冻
     */
    AMOUNT_UNFREEZE(4, "金额解冻");

    private final int code;
    private final String description;

    AccountOperationTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取枚举对应的代码值
     *
     * @return code 枚举的代码值
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取枚举的描述
     *
     * @return description 枚举的描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码值获取对应的枚举实例
     *
     * @param code 枚举的代码值
     * @return 对应的枚举实例，如果未找到，返回 null
     */
    public static AccountOperationTypeEnum ofCode(int code) {
        for (AccountOperationTypeEnum type : AccountOperationTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据代码值获取描述
     *
     * @param code 枚举的代码值
     * @return 对应的描述，如果未找到，返回 "未知类型"
     */
    public static String getDescriptionByCode(int code) {
        AccountOperationTypeEnum type = ofCode(code);
        return type != null ? type.getDescription() : "未知类型";
    }
}
