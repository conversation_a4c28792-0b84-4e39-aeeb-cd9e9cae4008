package so.dian.fis.settle.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.CacheInterface;
import so.dian.himalaya.boot.constant.CommonConstants;

import java.util.concurrent.TimeUnit;

/**
 * redis缓存 <br/>
 *
 * <AUTHOR>
 * @date 2018-12-12 18:18
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum CacheEnum implements CacheInterface {

    STAFF("staff", "职员缓存", 5L, TimeUnit.MINUTES),
    //
    ;

    private String key;
    private String desc;
    private Long expire;
    private TimeUnit unit;

    @Override
    public String getKey() {
        return CommonConstants.APPLICATION_NAME + CommonConstants.REDIS_SEPARATOR + this.key;
    }
}
