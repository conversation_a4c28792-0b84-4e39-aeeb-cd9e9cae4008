package so.dian.fis.settle.common.pojo.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * AccountExportBO
 *
 * <AUTHOR>
 * @desc
 * @date 2023/1/4 16:14
 */
@Data
public class AccountExportBO {

    @ExcelProperty("资金账户ID")
    private String accountNo;

    @ExcelProperty("资金账户类型")
    private String accountType;

    @ExcelProperty("对象类型")
    private String mainBizType;

    @ExcelProperty("对象ID")
    private String mainBizId;

    @ExcelProperty("对象名称")
    private String mainBizName;

    @ExcelProperty("账户余额(元)")
    private String balance;

    @ExcelProperty("可用余额(元)")
    private String availableBalance;

    @ExcelProperty("冻结金额(元)")
    private String frozenBalance;

    @ExcelProperty("账户状态")
    private String status;

    @ExcelProperty("所属公司")
    private String settleSubjectName;

    @ExcelProperty("创建时间")
    private String createTime;

}
