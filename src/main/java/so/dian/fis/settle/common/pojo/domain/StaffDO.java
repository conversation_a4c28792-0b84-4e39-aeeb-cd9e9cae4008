package so.dian.fis.settle.common.pojo.domain;

import lombok.*;
import so.dian.himalaya.common.entity.BaseDO;
import so.dian.kunlun.common.enums.UserGenderTypeEnum;

/**
 * 职员实体 <br/>
 *
 * <AUTHOR>
 * @date 2019-08-12 20:11
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class StaffDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     *
     * @see UserGenderTypeEnum
     */
    private Integer gender;
}
