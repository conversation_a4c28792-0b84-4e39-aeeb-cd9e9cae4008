package so.dian.fis.settle.common.util.biz;

import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;

import java.util.Objects;

/**
 * SettleSubjectValidateUtils
 *
 * <AUTHOR>
 */
public class SettleSubjectValidateUtils {

    public static Boolean validatePermission(Integer settleSubjectType, Long settleSubjectId,
            Integer targetSettleSubjectType, Long targetSettleSubjectId) {

        // 我司结算方可直接访问
        if(Objects.equals(settleSubjectType, SettleSubjectTypeEnum.XIAODIAN.getCode())) {
            return true;
        }
        // 结算方类型不一致
        if(!Objects.equals(settleSubjectType, targetSettleSubjectType)) {
            return false;
        }else {
            //否则结算方id要一致
            return Objects.equals(settleSubjectId, targetSettleSubjectId);
        }
    }

}
