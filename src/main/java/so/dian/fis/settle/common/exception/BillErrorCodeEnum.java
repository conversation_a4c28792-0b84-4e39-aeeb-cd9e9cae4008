package so.dian.fis.settle.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.ErrorCodeInterface;

@Getter
@AllArgsConstructor
public enum BillErrorCodeEnum implements ErrorCodeInterface<BillErrorCodeEnum> {
    //自定义异常
    MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT("F15100", "手工账单不支持该业务类型"),
    NOT_EDIT("F15101", "该账单关联多家门店数据，不支持编辑和克隆"),
    NOT_ALLOW_APPROVE("F15102", "该用户无审核权限"),
    OLD_MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT("F15103", "结算主体类型除代理商、合资公司、运营型服务商，不支持该业务类型"),
    NOT_SUPPORT_SECOND("F15104", "结算主体类型除代理商，不支持该业务类型"),
    AGENT_NOT_SUPPORT_PROXY_PAY("F15105", "不支持操作收入类账单"),
    NOT_AGENT_NOT_SUPPORT("F15106", "非小电直签二级代理不支持该业务类型"),
    NOT_SUPPORT_BIZ_TYPE("F15107", "结算主体类型除代理商和合资公司，不支持该业务类型"),
    SETTLEMENT_EXPORT_ERROR("F15108", "结算账单导出失败"),
    LIMIT_EXCEEDED("F15109","超出限制"),
    NO_RELATED_CONTRACT("F15110","无关联合同，不支持调账"),
    ACCOUNT_NOT_FOUND("F15111","账户信息不存在"),
    PING_AN_NOT_ALLOW("F15112", "平安银行清分关联账单不允许人工%s，请联系技术处理"),
    AGENT_NOT_SUPPORT_OUT_PROXY_PAY("F15113", "不支持操作支出类账单"),
    ;
    private String code;
    private String desc;
}
