package so.dian.fis.settle.common.pojo.converter;

import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.AddBillReq;
import so.dian.lvy.pojo.param.BatchBillManualParam;
import so.dian.lvy.pojo.param.BillManualParam;
import so.dian.lvy.pojo.param.BillParam;
import so.dian.lvy.pojo.param.OperationParam;

import java.util.List;
import java.util.stream.Collectors;

/**
 * BillConverter
 *
 * <AUTHOR>
 */
public class BillConverter {

    public static BillManualParam buildLvyManualParam(CurrentUserReq userReq, AddBillReq addBillReq) {
        BillManualParam billManualParam = new BillManualParam();
        billManualParam.setOperationParam(buildLvyOperationParam(userReq, addBillReq));
        billManualParam.setBillParam(addBillReq);
        return billManualParam;
    }

    private static OperationParam buildLvyOperationParam(CurrentUserReq userReq, AddBillReq addBillReq) {
        OperationParam operationParam = new OperationParam();
        operationParam.setOperatorId(userReq.getUserId());
        operationParam.setOperatorName(userReq.getUserName());
        operationParam.setSettleSubjectId(userReq.getSettleSubjectId());
        operationParam.setSettleSubjectType(userReq.getSettleSubjectType());
        if(addBillReq != null){
            operationParam.setComment(addBillReq.getComment());
        }
        return operationParam;
    }

    public static BatchBillManualParam buildLvyBatchBillManualParam(CurrentUserReq userReq, List<AddBillReq> addBillReqList) {
        BatchBillManualParam batchBillManualParam = new BatchBillManualParam();
        List<BillParam> params = addBillReqList.stream().map(tmp -> (BillParam) tmp).collect(Collectors.toList());
        batchBillManualParam.setBillParamList(params);
        batchBillManualParam.setOperationParam(buildLvyOperationParam(userReq, null));
        return batchBillManualParam;
    }
}
