package so.dian.fis.settle.common.pojo.val;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import so.dian.agent.api.constant.AgentBaseEnum;
import so.dian.agent.api.dto.AgentDTO;

import java.util.Objects;
import java.util.Optional;

/**
 * AgentVal
 *
 * <AUTHOR>
 * @desc
 * @date 2023/5/18 10:27
 */
@Data
@RequiredArgsConstructor(staticName = "of")
public class AgentVal {

    private final AgentDTO agentDTO;


    public boolean isDian() {
        Optional<AgentBaseEnum.AgentHierarchyEnum> agentHierarchyEnum = getAgentHierarchy(agentDTO);
        return Objects.equals(agentHierarchyEnum.orElse(null), AgentBaseEnum.AgentHierarchyEnum.TOP_HIERARCHY);
    }

    public boolean isFirst(){
        Optional<AgentBaseEnum.AgentHierarchyEnum> agentHierarchyEnum = getAgentHierarchy(agentDTO);
        return Objects.equals(agentHierarchyEnum.orElse(null), AgentBaseEnum.AgentHierarchyEnum.ONE_HIERARCHY);
    }

    public boolean isSecond(){
        Optional<AgentBaseEnum.AgentHierarchyEnum> agentHierarchyEnum = getAgentHierarchy(agentDTO);
        return Objects.equals(agentHierarchyEnum.orElse(null), AgentBaseEnum.AgentHierarchyEnum.TWO_HIERARCHY);
    }

    public Optional<AgentBaseEnum.AgentHierarchyEnum> getAgentHierarchy(AgentDTO agentDTO) {
        Integer hierarchy = agentDTO.getHierarchy();
        return AgentBaseEnum.AgentHierarchyEnum.get(hierarchy);
    }
}
