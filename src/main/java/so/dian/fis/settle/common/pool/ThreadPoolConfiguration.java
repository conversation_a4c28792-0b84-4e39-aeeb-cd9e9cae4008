package so.dian.fis.settle.common.pool;

import java.util.concurrent.*;
import java.util.concurrent.ThreadPoolExecutor.DiscardPolicy;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * ThreadPoolConfiguration
 *
 * <AUTHOR>
 * @desc
 * @date 2022/12/21 16:29
 */
@Slf4j
@Configuration
public class ThreadPoolConfiguration {

    @Bean(name = "exportExecutor",destroyMethod = "shutdown")
    public ThreadPoolExecutor exportExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 40, 15L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                new ThreadFactory() {
                    private AtomicInteger atomicInteger = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        return new Thread(r, "THREAD-EXPORT-" + atomicInteger.getAndIncrement());
                    }
                }, new DiscardPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("thread is full. new runnable was discard. now running thread {},now runnable {}",
                        e.getActiveCount(), r);
            }
        });
        return executor;
    }

    @Bean(name = "exportSettlementExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor exportSettlementExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(1, 2, 15L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100), new ThreadFactory() {
            private final AtomicInteger atomicInteger = new AtomicInteger();

            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "SETTLEMENT-EXPORT-THREAD-" + atomicInteger.getAndIncrement());
            }
        }, new DiscardPolicy());
        return executor;
    }

}
