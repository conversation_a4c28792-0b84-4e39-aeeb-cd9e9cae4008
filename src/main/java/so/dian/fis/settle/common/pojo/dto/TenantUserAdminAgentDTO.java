package so.dian.fis.settle.common.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenantUserAdminAgentDTO {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户域
     */
    private Integer domain;
    /**
     * 管理类型
     */
    private List<Integer> adminTypes;
}
