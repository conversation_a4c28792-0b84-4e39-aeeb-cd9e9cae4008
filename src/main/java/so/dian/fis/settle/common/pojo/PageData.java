package so.dian.fis.settle.common.pojo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * PageData
 *
 * <AUTHOR>
 * @desc
 * @date 2023/1/5 10:48
 */
@Data
public class PageData<T> implements Serializable {

    private static final long serialVersionUID = -4257092979237049883L;
    private List<T> list;
    private Long totalCount;
    private Long pageNo;
    private Integer pageSize;

    private String maxId;
    private Map<String, Object> extra;

    public PageData<T> setList(List<T> list) {
        this.list = list;
        return this;
    }

    private static <T> PageData<T> of() {
        return new PageData<>();
    }

    public PageData<T> setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public PageData<T> setPageNo(Long pageNo) {
        this.pageNo = pageNo;
        return this;
    }

    public PageData<T> setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public PageData<T> setExtra(Map<String, Object> extra) {
        this.extra = extra;
        return this;
    }

    public static <T> PageData<T> create(List<T> data, Long totalCount) {
        return create(data, totalCount, (Long)null, (Integer)null);
    }

    public static <T> PageData<T> create(List<T> data, Long totalCount, Long pageNo, Integer pageSize) {
        return create(data, totalCount, pageNo, pageSize, (Map)null);
    }

    public static <T> PageData<T> create(List<T> data, Long totalCount, Long pageNo, Integer pageSize, Map extra) {
        PageData<T> of = of();
        return of.setList(data).setTotalCount(totalCount).setPageNo(pageNo).setPageSize(pageSize).setExtra(extra);
    }


}
