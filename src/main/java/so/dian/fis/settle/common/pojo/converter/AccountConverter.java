package so.dian.fis.settle.common.pojo.converter;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.math.NumberUtils;
import so.dian.common.logger.util.StringUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.fis.settle.controller.account.enums.AccountStatusEnum;
import so.dian.fis.settle.controller.account.response.AccountListNewRsp;
import so.dian.fis.settle.controller.account.response.AccountListRsp;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.lvy.pojo.dto.QueryAccountListDTO;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AccountConverter
 *
 * <AUTHOR>
 */
public class AccountConverter {

    @Resource
    private AgentManager agentManager;
    @Resource
    private CustomerManager customerManager;

    public static List<AccountListRsp> convertQueryAccountListDTO2ListRsp(List<QueryAccountListDTO> listDTOList,
                                                                          Map<Long, String> idNameMap) {
        return LocalListUtils.transferList(listDTOList,
                queryAccountListDTO -> convertQueryAccountListDTO2ListRsp(queryAccountListDTO,
                        idNameMap.get(queryAccountListDTO.getMainBizId())));
    }

    public static AccountListRsp convertQueryAccountListDTO2ListRsp(QueryAccountListDTO accountListDTO,
                                                                    String mainBizName) {
        AccountListRsp accountListRsp = new AccountListRsp();
        accountListRsp.setAccountId(accountListDTO.getAccountId().toString());
        accountListRsp.setAccountAmount(accountListDTO.getAccountAmount());
        accountListRsp.setAvailableAmount(accountListDTO.getAvailableAmount());
        accountListRsp.setFreezeAmount(accountListDTO.getFreezeAmount());
        accountListRsp.setCreateTime(accountListDTO.getCreateTime());
        accountListRsp.setMainBizId(accountListDTO.getMainBizId().toString());
        accountListRsp.setMainBizName(mainBizName);
        accountListRsp.setMainBizType(accountListDTO.getMainBizType());

        MainBizTypeEnum mainBizTypeEnum = LocalEnumUtils.findByCodeWithoutDefault(MainBizTypeEnum.class, accountListDTO.getMainBizType());
        accountListRsp.setMainBizTypeStr(mainBizTypeEnum == null ? "未知商户类型" : mainBizTypeEnum.getDesc());
        accountListRsp.setFreeze(accountListDTO.getFreeze());
        return accountListRsp;
    }

    public static List<AccountListNewRsp> convertQueryAccountListNewListRsp(List<MerchantAccountResp> list) {
        return LocalListUtils.transferList(list,
                queryAccountListDTO -> convertQueryAccountListNewListRsp(queryAccountListDTO));
    }

    public static AccountListNewRsp convertQueryAccountListNewListRsp(MerchantAccountResp merchantAccountResp) {
        AccountListNewRsp accountListRsp = new AccountListNewRsp();
        accountListRsp.setAccountNo(merchantAccountResp.getAccountNo());
        accountListRsp.setAccountType(merchantAccountResp.getAccountType());
        accountListRsp.setAccountAmount(merchantAccountResp.getTotalAmount());
        accountListRsp.setAvailableAmount(merchantAccountResp.getAvailableAmount());
        accountListRsp.setFreezeAmount(merchantAccountResp.getFrozenAmount());
        accountListRsp.setCreateTime(new Date(merchantAccountResp.getGmtCreate()));
        accountListRsp.setMainBizId(merchantAccountResp.getMerchantId());
        accountListRsp.setMainBizName(merchantAccountResp.getMerchantName());
        accountListRsp.setMainBizType(merchantAccountResp.getMerchantType());
        accountListRsp.setId(merchantAccountResp.getId());
        accountListRsp.setBelongCompanyId(StrUtil.isBlank(merchantAccountResp.getAccountDimensionFactor3()) ? null : NumberUtils.createLong(merchantAccountResp.getAccountDimensionFactor3()));

        MainBizTypeEnum mainBizTypeEnum = LocalEnumUtils.findByCodeWithoutDefault(MainBizTypeEnum.class, merchantAccountResp.getMerchantType());
        accountListRsp.setMainBizTypeStr(mainBizTypeEnum == null ? "未知商户类型" : mainBizTypeEnum.getDesc());
        accountListRsp.setStatus(merchantAccountResp.getStatus());
        accountListRsp.setStatusStr(AccountStatusEnum.ofCode(merchantAccountResp.getStatus()).getValue());
        accountListRsp.setAccountId(StringUtils.isEmpty(merchantAccountResp.getExt1()) ? null : Long.valueOf(merchantAccountResp.getExt1()));
        return accountListRsp;
    }
}
