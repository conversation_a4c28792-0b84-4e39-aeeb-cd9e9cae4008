package so.dian.fis.settle.controller.account.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * BatchApprovalBillPcReq
 *
 * <AUTHOR>
 */
@ApiModel("批量审核账单操作")
@Data
public class BatchApprovalBillPcReq {
    @ApiModelProperty("账单号")
    private List<String> billNos;
    /**
     * 审核状态
     * @see so.dian.lvy.pojo.enums.DecisionStatusEnum
     */
    @ApiModelProperty("审核状态")
    private Integer decision;
    @ApiModelProperty("意见")
    private String comment;

}
