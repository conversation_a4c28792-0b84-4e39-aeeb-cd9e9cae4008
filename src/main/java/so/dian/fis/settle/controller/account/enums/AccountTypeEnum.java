package so.dian.fis.settle.controller.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.lvy.pojo.enums.BillRelateAccountTypeEnum;

/**
 * <p> 异常枚举 </p>
 * <p>业务线编码 + 5位数字 </p>
 * <p>F10000-F10999</p>
 *
 * <AUTHOR>
 * @date 2019-12-05 16:44
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备********号
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum{

    //用户相关
    SHARE_ACCOUNT(30, "分成账户"),
    AGENT_ACCOUNT(31, "设备资金账户"),
    PING_AN_SHARE_ACCOUNT(32, "平安银行资金账户"),
    IOT_PING_AN_SHARE_ACCOUNT(33, "IOT平安银行资金账户"),
    CREDIT_ACCOUNT(40, "授信账户"),
    OVERDUE_ACCOUNT(41, "滞纳金账户");

    private Integer key;
    private String value;

    /**
     * 将 BillRelateAccountTypeEnum 的 code 转换为 AccountTypeEnum
     * {@link so.dian.lvy.pojo.enums.BillRelateAccountTypeEnum}
     * @param billTypeCode BillRelateAccountTypeEnum 的 code 值
     * @return 对应的 AccountTypeEnum 枚举值
     */
    public static AccountTypeEnum fromBillRelateAccountTypeCode(Integer billTypeCode) {
        if (billTypeCode == null) {
            return null;
        }

        switch (billTypeCode) {
            case 1:
                return SHARE_ACCOUNT; // 分成账户
            case 2:
                return PING_AN_SHARE_ACCOUNT; // 平安前端分账账户
            case 3:
                return IOT_PING_AN_SHARE_ACCOUNT; // IOT平安前端分账账户
            default:
                throw new IllegalArgumentException("无效的 BillRelateAccountTypeEnum code: " + billTypeCode);
        }
    }

    /**
     * 将 AccountTypeEnum 转换为 BillRelateAccountTypeEnum 的 code
     *
     * @param accountType AccountTypeEnum 枚举值
     * @return 对应的 BillRelateAccountTypeEnum code
     */
    public static Integer toBillRelateAccountTypeCode(Integer accountType) {
        if (accountType == null) {
            throw new IllegalArgumentException("无效的 accountType: " + null);
        }

        return switch (accountType) {
            case 30 -> BillRelateAccountTypeEnum.SPLIT_ACCOUNT.getCode(); // 分成账户 (SHARE_ACCOUNT)
            case 32 -> BillRelateAccountTypeEnum.P_A_FRONT_ACCOUNT.getCode(); // 平安分成账户 (PING_AN_SHARE_ACCOUNT)
            case 33 -> BillRelateAccountTypeEnum.IOT_P_A_FRONT_ACCOUNT.getCode(); // IOT平安分成账户 (IOT_PING_AN_SHARE_ACCOUNT)
            default -> throw new IllegalArgumentException("无效的 accountType: " + accountType);
        };
    }
}
