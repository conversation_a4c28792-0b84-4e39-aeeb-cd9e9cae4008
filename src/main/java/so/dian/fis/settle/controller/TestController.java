package so.dian.fis.settle.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.common.logger.Log;
import so.dian.fis.settle.biz.handler.BillHandler;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.biz.service.AccountBalanceNewService;
import so.dian.fis.settle.biz.service.BillService;
import so.dian.fis.settle.biz.service.BillWhiteListService;
import so.dian.fis.settle.biz.service.CurrentUserService;
import so.dian.fis.settle.client.pojo.response.UserSessionDTO;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.constant.LoggerConstants;
import so.dian.fis.settle.common.exception.BillErrorCodeEnum;
import so.dian.fis.settle.common.pojo.validator.BillMerchantTypedator;
import so.dian.fis.settle.common.pojo.validator.BillValidator;
import so.dian.fis.settle.controller.account.request.AddBillPcReq;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceNewReq;
import so.dian.himalaya.boot.aop.annotation.Operate;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.exception.BizException;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;
import so.dian.yandang.client.pojo.response.PayNotifyMsgRsp;

import java.util.Objects;


@Slf4j
@Api(value = "TestController")
@RestController
public class TestController {

    @Autowired
    private BillService billService;
    @Autowired
    private BillWhiteListService billWhiteListService;

    @Resource
    private AccountBalanceNewService accountBalanceNewService;

    @ApiOperation("测试打款结果通知lvy")
    @PostMapping(value = "/addBill")
    @Operate
    public BizResult<Object> addBill(@RequestBody PayNotifyMsgRsp payNotifyMsgRsp) {
        try {
            billService.addBill(payNotifyMsgRsp.getBillNo());
        } catch (Exception e) {
            //非个人
            Log.info(LoggerConstants.BIZ, e.getMessage(), e);
        }

        return BizResult.create(true);
    }

    @Autowired
    private LvyManager lvyManager;

    @Autowired
    private BillHandler billHandler;

    @Autowired
    private CurrentUserService currentUserService;

    @PostMapping(value = "/test/add/bill")
    public BizResult<String> addBill(@RequestParam("userId") Long userId, AddBillPcReq req){

        UserSessionDTO session = currentUserService.getSession(userId);

        CurrentUserReq userReq = new CurrentUserReq();
        userReq.setSettleSubjectId(session.getSettleSubjectId());
        userReq.setSettleSubjectType(session.getSettleSubjectType());
        userReq.setUserId(session.getUserId());
        userReq.setUserName(session.getNickName());
        userReq.setAgentSubType(session.getAgentSubType());
        userReq.setCurrentRole(session.getUserRole());


        if (Objects.equals(req.getBizType(), AccountBillBizTypeEnum.代付代理商商户分成.getCode()) && !Objects.equals(req.getBillingMainBizType(), MainBizTypeEnum.AGENT.getCode())) {
            throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_SECOND);
        }



        // 判断业务类型是否允许创建
        if (BillMerchantTypedator.isValidManualBillBizType(req.getBillingMainBizType())) {
            if (!BillValidator.isValidManualBillBizType(req.getBizType())) {
                throw BizException.create(BillErrorCodeEnum.MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT);
            }
        } else {
            if (!BillValidator.isOldValidManualBillBizType(req.getBizType())) {
                throw BizException.create(BillErrorCodeEnum.OLD_MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT);
            }
        }


        return BizResult
                .create(lvyManager.addManualBillReturnStr(billHandler.buildAddBillParam(userReq, req)));
    }

    @ApiOperation("获取账单导入白名单列表")
    @GetMapping(value = "/test/batchImportBill/getWhiteList")
    @Operate
    public BizResult<String> getWhiteList() {
        return BizResult.create(billWhiteListService.getWhiteList());
    }

    @ApiOperation("设置账单导入白名单列表")
    @GetMapping(value = "/test/batchImportBill/setWhiteList")
    @Operate
    public BizResult<Boolean> setWhiteList(@RequestParam("whiteList") String whiteList) {
        billWhiteListService.setWhiteList(whiteList);
        return BizResult.create(true);
    }

    @ApiOperation("设置账单导入白名单列表")
    @GetMapping(value = "/test/testAccountUnFreeze")
    @Operate
    public BizResult<Boolean> testAccountUnFreeze() {
        PageAccountBalanceNewReq req = new PageAccountBalanceNewReq();
        req.setPageNo(1);
        req.setPageSize(10);
        req.setAccountNo("2021121401259694113496679000072");
        req.setAccountType("30");
        Long userId = 16102L;
        String userName = "兴霸";
        CurrentUserReq user = new CurrentUserReq();
        user.setUserId(userId);
        user.setUserName(userName);
        user.setSettleSubjectId(1L);
        user.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
        accountBalanceNewService.pageAccountBalanceReq(user, req);
        return BizResult.create(true);
    }

    @PostMapping(value = "/report/log")
    public BizResult<Boolean> testTrue(@RequestBody String body) {
        log.info("get body:{}", body);
        return BizResult.create(Boolean.TRUE);
    }
}
