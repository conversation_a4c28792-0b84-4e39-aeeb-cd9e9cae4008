package so.dian.fis.settle.controller.transfer.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PageTransferRecordDetailRsp
 *
 * <AUTHOR>
 */
@ApiModel("划拨记录明细返回值列表")
@Data
public class PageTransferRecordDetailRsp{
    @ApiModelProperty(value = "抵扣明细id")
    private Long id;

    @ApiModelProperty(value = "支出方名称")
    private String paySubjectName;

    @ApiModelProperty(value = "支出方分成账户金额")
    private BigDecimal payAccountAmount;

    @ApiModelProperty(value = "收入方名称")
    private String incomeSubjectName;

    @ApiModelProperty(value = "收入方分成账户金额")
    private BigDecimal incomeAccountAmount;

    @ApiModelProperty(value = "生成时间")
    private String openTime;

    @ApiModelProperty(value = "本期需要抵扣总金额")
    private BigDecimal needTransferAmount;

    @ApiModelProperty(value = "本期抵扣总金额")
    private BigDecimal transferAmount;

    @ApiModelProperty(value = "支出方流水号")
    private String payBillNo;

    @ApiModelProperty(value = "支出方账单状态")
    private String payBillStatusName;

    @ApiModelProperty(value = "收入方账单流水号")
    private String incomeBillNo;

    @ApiModelProperty(value = "收入方账单状态")
    private String incomeBillStatusName;

    /**
     * 支出方id
     */
    private Long paySubjectId;

    /**
     * 支出方账户类型30-分成 32-平安 33-IOT平安
     */
    private String payAccountTypeStr;

    /**
     * 支出方结算主体名称
     */
    private String paySettleSubjectName;

    /**
     * 收入方ID
     */
    private Long incomeSubjectId;

    /**
     * 收入方账户类型30-分成 32-平安 33-IOT平安
     */
    private String incomeAccountTypeStr;

    /**
     * 收入方结算主体名称
     */
    private String incomeSettleSubjectName;

    /**
     * 抵扣原因
     */
    private String transferReason;
}
