package so.dian.fis.settle.controller.account.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;

import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
@ApiModel("结算列表导出")
@Data
public class SettleListExportPcReq {
    /**
     * 商家/渠道商 名称
     */
    @ApiModelProperty(value = "商家/渠道商 名称")
    private String billingMainBizName;

    /**
     * 账单id
     */
    @ApiModelProperty(value = "账单id")
    private Long id;
    /**
     * 账单流水号
     */
    @ApiModelProperty(value = "账单流水号")
    private String billNo;
    /**
     * 结算主体类型
     *
     * @see so.dian.lvy.pojo.enums.MainBizTypeEnum
     */
    @NotNull(message = "请选择结算主体类型")
    @ApiModelProperty(value = "请选择结算主体类型")
    private Integer billingMainBizType;
    /**
     * 结算主体id
     */
    @ApiModelProperty(value = "结算主体id")
    private List<Long> billingMainBizIds;
    /**
     * 账单名称
     */
    @ApiModelProperty(value = "账单名称")
    private String billName;
    /**
     * 账单周期类型
     *
     * @see so.dian.lvy.pojo.enums.BillPeriodTypeEnum
     */
    @ApiModelProperty(value = "账单周期类型")
    private Integer periodType;
    /**
     * 账单周期-开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "账单周期-开始时间")
    private Date periodStart;
    /**
     * 账单周期-结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "账单周期-结束时间")
    private Date periodEnd;
    /**
     * 生成方式
     *
     * @see so.dian.lvy.pojo.enums.BillGenerateTypeEnum
     */
    @ApiModelProperty(value = "生成方式")
    private Integer genType;
    /**
     * 账单业务类型：
     *
     * @see AccountBillBizTypeEnum
     */
    @ApiModelProperty(value = "账单业务类型")
    private List<String> bizTypeList;
    /**
     * 收支类型
     *
     * @see so.dian.lvy.pojo.enums.BalanceTypeEnum
     */
    @ApiModelProperty(value = "收支类型")
    private Integer balanceType;
    /**
     * 账单状态
     *
     * @see so.dian.lvy.pojo.enums.BillStatusEnum
     */
    @ApiModelProperty(value = "账单状态")
    private List<String> statusList;
    /**
     * 账单最小金额
     */
    @ApiModelProperty(value = "账单最小金额")
    private Integer amountMin;
    /**
     * 账单最大金额
     */
    @ApiModelProperty(value = "账单最大金额")
    private Integer amountMax;
    /**
     * 开始创建时间
     */
    @ApiModelProperty(value = "开始创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createStart;
    /**
     * 结束创建时间
     */
    @ApiModelProperty(value = "结束创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEnd;

    private Integer settleSubjectType = 1;
    private Long settleSubjectId = 1L;
    private Boolean isAgentDirectSign;


    /**
     * 账户类型 支持多选
     */
    private List<Integer> billAccountTypes;
}
