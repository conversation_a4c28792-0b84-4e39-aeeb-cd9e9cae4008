package so.dian.fis.settle.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.fis.settle.biz.service.SelectionOptionService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.response.NameValueRsp;
import so.dian.himalaya.common.entity.BizResult;

import java.util.List;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2022/10/13 18:21
 * @description: 资金账户-所属公司下拉框
 */
@Api(value = "ConstantApi", description = "下拉框API")
@RestController
public class SelectionOptionController {

    @Autowired
    private SelectionOptionService optionService;

    @ApiOperation("所属公司")
    @GetMapping("/constant/belong/company/list")
    public BizResult<List<NameValueRsp<String, String>>> belongCompanys(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(optionService.belongCompanys(userReq));
    }
}
