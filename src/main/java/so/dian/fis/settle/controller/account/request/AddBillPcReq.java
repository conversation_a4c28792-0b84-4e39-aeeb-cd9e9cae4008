package so.dian.fis.settle.controller.account.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;

import java.util.Date;

/**
 * AddBillPcReq
 *
 * <AUTHOR>
 */
@ApiModel("新增账单")
@Data
public class AddBillPcReq {
    @ApiModelProperty("账单名称")
    private String billName;
    /**
     * @see so.dian.lvy.pojo.enums.BillPeriodTypeEnum
     */
    @ApiModelProperty("账单周期类型")
    private Integer periodType;
    @ApiModelProperty("账单周期-开始时间")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodStart;
    @ApiModelProperty("账单周期-结束时间")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodEnd;
    /**
     * @see AccountBillBizTypeEnum
     */
    @ApiModelProperty("账单业务类型")
    private Integer bizType;
    /**
     * @see so.dian.lvy.pojo.enums.BalanceTypeEnum
     */
    @ApiModelProperty("收支类型")
    private Integer balanceType;

    @ApiModelProperty("账单金额(分)")
    private Long amount;

    @ApiModelProperty("结算主体id")
    private Long billingMainBizId;
    /**
     * @see so.dian.lvy.pojo.enums.MainBizTypeEnum
     */
    @ApiModelProperty("结算主体类型")
    private Integer billingMainBizType;

    /**
     * @see so.dian.lvy.pojo.enums.BooleanEnum
     */
    @ApiModelProperty("是否需要审核,默认需要审核")
    private Boolean needCheck;

    @ApiModelProperty("结算方主体id")
    private Long settleSubjectId = 1L;

    @ApiModelProperty("结算方类型")
    private Integer settleSubjectType = 1;

    @ApiModelProperty("备注")
    private String comment;

    @ApiModelProperty("生成方式：1自动，2手动")
    private Integer genType;

    @ApiModelProperty("门店id")
    private Long shopId ;

    @ApiModelProperty("账户类型")
    private Integer accountType;
}
