package so.dian.fis.settle.controller.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * <p> 新迁移账户类型 </p>
 *
 *
 * <AUTHOR>
 * @date 2022-07-14 16:44
 *
 */
@Getter
@AllArgsConstructor
public enum UserRoleExtendEnum{
    OUTSOURCED_CUSTOMER_SERVICE_M("Outsourced_Customer_Service_M", "外包客服");

    private String roleName;
    private String roleLabel;

    public String getRoleName() {
        return this.roleName;
    }

    public String getDesc() {
        return this.roleLabel;
    }
}
