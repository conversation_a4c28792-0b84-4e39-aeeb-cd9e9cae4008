package so.dian.fis.settle.controller.account;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.fis.settle.biz.service.AccountNewService;
import so.dian.fis.settle.biz.service.ConstantService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.pojo.PageData;
import so.dian.fis.settle.controller.account.request.AccountExportReq;
import so.dian.fis.settle.controller.account.request.AccountListNewReq;
import so.dian.fis.settle.controller.account.response.AccountListNewRsp;
import so.dian.fis.settle.controller.account.response.AccountStatisticsRsp;
import so.dian.fis.settle.controller.account.response.AccountStatusRsp;
import so.dian.fis.settle.controller.account.response.AccountTypeRsp;
import so.dian.fis.settle.executors.AccountExportExecutor;
import so.dian.fis.settle.executors.AccountPageExecutor;
import so.dian.fis.settle.executors.AccountStatisticsExecutor;
import so.dian.fis.settle.executors.cmd.AccountExportAppCmd;
import so.dian.fis.settle.executors.cmd.AccountPageAppCmd;
import so.dian.fis.settle.executors.cmd.AccountStatisticsAppCmd;
import so.dian.himalaya.boot.aop.annotation.Operate;
import so.dian.himalaya.common.entity.BizResult;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

@Api(value = "AccountController", description = "新资金账户API")
@RestController
@Slf4j
@RequestMapping("/accountNew")
public class AccountNewController {

    @Resource
    private AccountNewService accountNewService;
    @Resource
    private ConstantService constantService;

    @Autowired
    private AccountExportExecutor accountExportExecutor;
    @Autowired
    private AccountPageExecutor accountPageExecutor;

    @Autowired
    private AccountStatisticsExecutor statisticsExecutor;

    @ApiOperation("新PC查询资金账户列表")
    @PostMapping(value = "/pageAccount")
    public BizResult<so.dian.fis.settle.common.pojo.PageData<AccountListNewRsp>> pageAccount(
            @CurrentUser CurrentUserReq userReq,
            @Valid @RequestBody AccountListNewReq pageAccountNewReq) {
        PageData<AccountListNewRsp> execute = accountPageExecutor.execute(
                AccountPageAppCmd.of(pageAccountNewReq, userReq));
        return BizResult.create(execute);
    }

    @ApiOperation("新PC资金账户统计")
    @PostMapping(value = "/statistics")
    public BizResult<AccountStatisticsRsp> accountStatistics(@CurrentUser CurrentUserReq userReq,
                                                             @Valid @RequestBody AccountListNewReq listNewReq) {

        AccountStatisticsRsp statisticsRsp = statisticsExecutor.execute(AccountStatisticsAppCmd.of(listNewReq, userReq));
        return BizResult.create(statisticsRsp);
    }

    @ApiOperation("新PC冻结资金账户")
    @GetMapping(value = "/freeze")
    @Operate
    public BizResult<Boolean> freeze(@CurrentUser CurrentUserReq userReq,
            @RequestParam("accountNo") String accountNo, @RequestParam("accountType") Integer accountType) {
        return accountNewService.freeze(accountNo, accountType, userReq);
    }

    @ApiOperation("新PC资金账户解冻")
    @GetMapping(value = "/unFreeze")
    @Operate
    public BizResult<Boolean> unFreeze(@CurrentUser CurrentUserReq userReq,
            @RequestParam("accountNo") String accountNo, @RequestParam("accountType") Integer accountType) {
        return accountNewService.unFreeze(accountNo, accountType, userReq);
    }

    @ApiOperation("新PC资金账户-商户类型")
    @GetMapping(value = "/getObjectType")
    @Operate
    public BizResult<List<NameValueDTO>> getObjectNewType(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(constantService.getObjectTypeNew(userReq));
    }

    @ApiOperation("新PC资金账户-账户类型")
    @GetMapping(value = "/getAccountType")
    @Operate
    public BizResult<List<AccountTypeRsp>> getAccountType(@CurrentUser CurrentUserReq req) {
        return BizResult.create(constantService.getAccountType(req));
    }

    @ApiOperation("新PC资金账户-账户状态")
    @GetMapping(value = "/getAccountStatus")
    @Operate
    public BizResult<List<AccountStatusRsp>> getAccountStatus() {
        return BizResult.create(constantService.getAccountStatus());
    }

    @ApiOperation("新PC资金账户-账户导出")
    @PostMapping(value = "/export")
    public BizResult<Boolean> exportAccount(@CurrentUser CurrentUserReq userReq,
            @RequestBody @Valid AccountExportReq req) {
        accountExportExecutor.execute(AccountExportAppCmd.of(req, userReq));
        return BizResult.create(true);
    }
}
