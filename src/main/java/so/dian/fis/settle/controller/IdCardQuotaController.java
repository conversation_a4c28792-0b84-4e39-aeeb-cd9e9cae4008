package so.dian.fis.settle.controller;

import io.swagger.annotations.Api;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.fis.settle.biz.executors.IdCardStaticsAmountExecutor;
import so.dian.fis.settle.client.api.IdCardQuotaApi;
import so.dian.fis.settle.client.pojo.request.IdCardMonthQuotaQueryDTO;
import so.dian.fis.settle.client.pojo.response.IdCardMonthQuotaDTO;
import so.dian.himalaya.common.entity.BizResult;

/**
 * IdCardQuotaController
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:18
 */
@Api(value = "IdCardQuotaApi", description = "身份证月余额")
@RestController
public class IdCardQuotaController implements IdCardQuotaApi {


    @Autowired
    private IdCardStaticsAmountExecutor idCardStaticsAmountExecutor;

    @Override
    public BizResult<IdCardMonthQuotaDTO> monthQuota(@Valid @RequestBody IdCardMonthQuotaQueryDTO query) {
        IdCardMonthQuotaDTO monthQuota = idCardStaticsAmountExecutor.execute(query);
        return BizResult.create(monthQuota);
    }
}
