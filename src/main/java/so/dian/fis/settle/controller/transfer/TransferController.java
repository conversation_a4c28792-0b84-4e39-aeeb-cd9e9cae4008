package so.dian.fis.settle.controller.transfer;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.fis.settle.biz.service.TransferService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.transfer.request.PageTransferRecordDetailReq;
import so.dian.fis.settle.controller.transfer.request.PageTransferRecordReq;
import so.dian.fis.settle.controller.transfer.response.PageTransferRecordDetailRsp;
import so.dian.fis.settle.controller.transfer.response.PageTransferRecordRsp;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;

import jakarta.annotation.Resource;

/**
 * 结算系统（新） / 渠道商分成关联抵扣
 */

@Api(value = "TransferController", description = "关联代理商划拨分成账户API")
@RestController
@Slf4j
public class TransferController {
    @Resource
    private TransferService transferService;

    @ApiOperation("PC关联抵扣按钮操作")
    @PostMapping(value = "/transfer/transferRecordExecute")
    public BizResult<String> transferRecordExecute(@CurrentUser CurrentUserReq userReq) {
        return transferService.transferRecordExecute(userReq.getUserId());
    }

    @ApiOperation("PC查询关联抵扣记录列表")
    @PostMapping(value = "/transfer/pageTransfer")
    public BizResult<PageData<PageTransferRecordRsp>> pageDeduct(@CurrentUser CurrentUserReq userReq,
                                                                 @RequestBody PageTransferRecordReq pageTransferReq) {
        return BizResult.create(transferService.pageDeduct(pageTransferReq));
    }

    @ApiOperation("PC查询抵扣明细记录")
    @PostMapping(value = "/transfer/pageTransferDetail")
    public BizResult<PageData<PageTransferRecordDetailRsp>> pageTransferDetail(@CurrentUser CurrentUserReq userReq,
                                                                               @RequestBody PageTransferRecordDetailReq pageTransferRecordDetailReq) {
        return BizResult.create(transferService.pageTransferDetail(pageTransferRecordDetailReq));
    }

    @ApiOperation("PC抵扣明细记录-导出")
    @PostMapping(value = "/transfer/export")
    public BizResult<Boolean> exportTransferRecordDetail(@CurrentUser CurrentUserReq userReq,
                                            @RequestBody PageTransferRecordDetailReq req) {
        return transferService.transferRecordDetailExport(req,userReq);
    }

    @ApiOperation("PC抵扣按钮白名单")
    @PostMapping(value = "/transfer/checkPermission")
    public BizResult<Boolean> transferCheckPermission(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(transferService.transferCheckPermission(userReq));
    }
}
