package so.dian.fis.settle.controller.account.response;

import lombok.Data;

import java.util.Date;

/**
 * AccountListRsp
 *
 * <AUTHOR>
 */
@Data
public class AccountListRsp {
    private String accountId;
    private Long accountAmount;
    private Long availableAmount;
    private Date createTime;
    private Long freezeAmount;
    private String mainBizId;
    private String mainBizName;
    private Integer mainBizType;
    private String mainBizTypeStr;
    private Integer freeze;
    private Boolean hasMove;

    private Long settleSubjectId;
}
