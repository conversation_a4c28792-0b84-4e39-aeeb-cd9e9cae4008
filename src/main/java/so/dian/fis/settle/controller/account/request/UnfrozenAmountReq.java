package so.dian.fis.settle.controller.account.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: ahuang
 * @CreateTime: 2024-11-26 10:15
 * @Description:
 */
@Data
public class UnfrozenAmountReq {
    @Min(value = 1L, message = "解冻金额必须大于0")
    @NotNull(message = "解冻金额")
    private Long unfrozenAmount;

    @NotBlank
    private String accountNo;

    @NotNull(message = "账户类型")
    private  Integer accountType;

    @NotBlank(message = "钉钉审批编号")
    private String oaApprovalNo;
}
