package so.dian.fis.settle.controller.account.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * PageReq
 *
 * <AUTHOR>
 */
@Data
public class PageReq {
    /**
     * 分页大小
     */
    private Integer pageSize;
    /**
     * 页数
     */
    private Integer pageNo;

    @JsonIgnore
    public Long getStart() {
        return Long.parseLong(this.pageSize.toString()) * (pageNo - 1);
    }
}
