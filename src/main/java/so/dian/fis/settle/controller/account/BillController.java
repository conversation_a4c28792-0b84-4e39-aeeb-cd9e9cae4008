package so.dian.fis.settle.controller.account;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.fis.settle.biz.service.BillService;
import so.dian.fis.settle.biz.service.BillWhiteListService;
import so.dian.fis.settle.biz.service.ConstantService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.AddBillPcReq;
import so.dian.fis.settle.controller.account.request.ApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.BatchAddManualBillReq;
import so.dian.fis.settle.controller.account.request.BatchApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.ConfirmBillPcReq;
import so.dian.fis.settle.controller.account.request.DiscardBillPcReq;
import so.dian.fis.settle.controller.account.request.EditBillPcReq;
import so.dian.fis.settle.controller.account.request.LvyBillShopPcReq;
import so.dian.fis.settle.controller.account.request.PageBillItemReq;
import so.dian.fis.settle.controller.account.request.PcBillListReq;
import so.dian.fis.settle.controller.account.request.SettleBillAgainPcReq;
import so.dian.fis.settle.controller.account.request.SettleListExportPcReq;
import so.dian.fis.settle.controller.account.response.AccountTypeRsp;
import so.dian.fis.settle.controller.account.response.BalanceTypeRsp;
import so.dian.fis.settle.controller.account.response.BillDetailPcRsp;
import so.dian.fis.settle.controller.account.response.BillInfoRsp;
import so.dian.fis.settle.controller.account.response.BillItemNoListRsp;
import so.dian.fis.settle.controller.account.response.BillItemPcRsp;
import so.dian.fis.settle.controller.account.response.BillItemShopRsp;
import so.dian.fis.settle.controller.account.response.PcBillListRsp;
import so.dian.himalaya.boot.aop.annotation.Operate;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 结算系统
 */

@Api(value = "AccountController", description = "资金账户API")
@RestController
@Slf4j
@RequestMapping("/bill")
public class BillController {

    @Resource
    private BillService billService;
    @Resource
    private BillWhiteListService billWhiteListService;
    @Resource
    private ConstantService constantService;

    @ApiOperation("批量新增手工账单")
    @PostMapping(value = "/batchAddManual")
    @Operate
    public BizResult<Integer> addManualByExcel(@CurrentUser CurrentUserReq userReq, @RequestBody
            BatchAddManualBillReq batchAddManualBillReq) {

        return billService.batchAddManualBill(userReq, batchAddManualBillReq);
    }

    @ApiOperation("是否有批量新增手工账单权限")
    @PostMapping(value = "/batchAddManual/hasPermission")
    @Operate
    public BizResult<Boolean> hasAddManualByExcelPermission(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(billWhiteListService.checkHasPermission(userReq.getUserName()));
    }

    @ApiOperation("pc账单列表")
    @PostMapping(value = "/listForPc")
    public BizResult<List<PcBillListRsp>> listBillPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody PcBillListReq listReq) {
        return billService.listBillPc(userReq, listReq);
    }

    @ApiOperation("pc账单筛选条件-账户类型")
    @GetMapping(value = "/accountType")
    public BizResult<List<AccountTypeRsp>> getAccountType(@CurrentUser CurrentUserReq req) {
        return BizResult.create(constantService.getBillAccountType(req));
    }

    @ApiOperation("pc结算列表导出")
    @PostMapping(value = "/settleListExportPc")
    public BizResult<String> settleListExportPc(@CurrentUser CurrentUserReq userReq,
                                                @RequestBody SettleListExportPcReq settleListExportPcReq) {

        return billService.settleListExportPc(userReq, settleListExportPcReq);
    }

    @ApiOperation("pc账单统计")
    @PostMapping(value = "/statisticsForPc")
    public BizResult<Long> statisticsForPc(@CurrentUser CurrentUserReq userReq, @RequestBody PcBillListReq listReq) {
        return billService.statisticsForPc(userReq, listReq);
    }

    @ApiOperation("新增手工账单")
    @PostMapping(value = "/addBillForPc")
    @Operate
    public BizResult<String> addBillForPc(@CurrentUser CurrentUserReq userReq, @RequestBody AddBillPcReq addBillPcReq) {
        return billService.addBillForPc(userReq, addBillPcReq);
    }

    @ApiOperation("修改手工账单")
    @PostMapping(value = "/editBillForPc")
    @Operate
    public BizResult<String> editBillForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody EditBillPcReq editBillPcReq) {
        return billService.editBillForPc(userReq, editBillPcReq);
    }

    @ApiOperation("作废账单")
    @PostMapping(value = "/discardBillForPc")
    @Operate
    public BizResult<String> discardBillForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody DiscardBillPcReq discardBillPcReq) {
        return billService.discardBillForPc(userReq, discardBillPcReq);
    }

    @ApiOperation("批量审核账单")
    @PostMapping(value = "/batchApprovalBillForPc")
    @Operate
    public BizResult<Boolean> batchApprovalBillForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody BatchApprovalBillPcReq batchApprovalBillPcReq) {
        return billService.batchApprovalBillForPc(userReq, batchApprovalBillPcReq);
    }

    @ApiOperation("审核账单")
    @PostMapping(value = "/approvalBillForPc")
    @Operate
    public BizResult<String> approvalBillForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody ApprovalBillPcReq approvalBillPcReq) {
        return billService.approvalBillForPc(userReq, approvalBillPcReq);
    }

    @ApiOperation("确认账单")
    @PostMapping(value = "/confirmBillForPc")
    @Operate
    public BizResult<String> confirmBillForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody ConfirmBillPcReq confirmBillPcReq) {
        return billService.confirmBillForPc(userReq, confirmBillPcReq);
    }

    @ApiOperation("账单重新结算")
    @PostMapping(value = "/settleBillAgainForPc")
    @Operate
    public BizResult<String> settleBillAgainForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody SettleBillAgainPcReq settleBillAgainPcReq) {
        return billService.settleBillAgainForPc(userReq, settleBillAgainPcReq);
    }

    @ApiOperation("获取账单信息")
    @GetMapping(value = "/getBillInfo")
    public BizResult<BillInfoRsp> getBillInfo(@CurrentUser CurrentUserReq userReq,
            @RequestParam("billNo") String billNo) {
        return billService.getBillInfo(userReq, billNo);
    }

    @ApiOperation("获取账单详细信息")
    @GetMapping(value = "/getBillDetail")
    public BizResult<BillDetailPcRsp> getBillDetail(@CurrentUser CurrentUserReq userReq,
            @RequestParam("billNo") String billNo) {
        return billService.getBillDetailForPc(userReq, billNo);
    }

    @ApiOperation("获取账单明细")
    @PostMapping(value = "/pageBillItem")
    public BizResult<PageData<BillItemPcRsp>> pageBillItem(@CurrentUser CurrentUserReq userReq,
            @RequestBody PageBillItemReq pageBillItemReq) {
        return billService.pageBillItem(userReq, pageBillItemReq);
    }

    @ApiOperation("获取账单明细统计")
    @PostMapping(value = "/statisticsBillItems")
    public BizResult<Long> statisticsBillItems(@CurrentUser CurrentUserReq userReq,
            @RequestBody PageBillItemReq pageBillItemReq) {
        return billService.statisticsBillItems(userReq, pageBillItemReq);
    }

    @ApiOperation("获取账单明细单号")
    @PostMapping(value = "/getBillItemNos")
    public BizResult<BillItemNoListRsp> getBillItemNos(@CurrentUser CurrentUserReq userReq,
            @RequestBody PageBillItemReq pageBillItemReq) {
        return billService.getBillItemNos(userReq, pageBillItemReq);
    }

    @ApiOperation("获取账单明细门店列表")
    @PostMapping(value = "/getBillShops")
    public BizResult<PageData<BillItemShopRsp>> getBillShops(@CurrentUser CurrentUserReq userReq,
            @RequestBody LvyBillShopPcReq lvyBillShopPcReq) {
        return billService.getBillShops(userReq, lvyBillShopPcReq);
    }

    @ApiOperation("PC结算系统-对象类型")
    @GetMapping(value = "/getObjectType")
    @Operate
    public BizResult<List<NameValueDTO>> getObjectType(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(constantService.getObjectType(userReq));
    }

    @ApiOperation("PC结算系统-业务类型")
    @GetMapping(value = "/getBalanceBizType")
    @Operate
    public BizResult<List<BalanceTypeRsp>> getBalanceBizType(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(constantService.getBalanceBizType());
    }
}
