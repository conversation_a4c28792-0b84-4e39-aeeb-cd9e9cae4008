package so.dian.fis.settle.controller.account;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.fis.settle.biz.service.AccountBalanceService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceReq;
import so.dian.fis.settle.controller.account.response.AccountBalancePcRsp;
import so.dian.fis.settle.controller.account.response.BalanceTypeRsp;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;

import jakarta.annotation.Resource;
import java.util.List;

@Api(value = "AccountController", description = "资金账户API")
@RestController
@Slf4j
@RequestMapping("/accountBalance")
public class AccountBalanceController {

    @Resource
    private AccountBalanceService accountBalanceService;

    @ApiOperation("PC查询资金账户流水列表")
    @PostMapping(value = "/pageForPc")
    public BizResult<PageData<AccountBalancePcRsp>> pageAccountBalanceForPc(@CurrentUser CurrentUserReq userReq,
            @RequestBody PageAccountBalanceReq pageAccountBalanceReq) {
        return accountBalanceService.pageAccountBalanceReq(userReq, pageAccountBalanceReq);
    }

    @ApiOperation("PC查询老的资金账户流水类型")
    @PostMapping(value = "/accountBalanceType")
    public BizResult<List<BalanceTypeRsp>> accountBalanceType(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(accountBalanceService.accountBalanceType(userReq));
    }

}
