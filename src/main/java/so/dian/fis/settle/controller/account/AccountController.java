package so.dian.fis.settle.controller.account;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.fis.settle.biz.service.AccountService;
import so.dian.fis.settle.biz.service.ConstantService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.PageAccountReq;
import so.dian.fis.settle.controller.account.response.AccountListRsp;
import so.dian.himalaya.boot.aop.annotation.Operate;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.lvy.pojo.dto.AccountStatisticsDTO;

import java.util.List;

@Api(value = "AccountController", description = "资金账户API")
@RestController
@Slf4j
@RequestMapping("/account")
public class AccountController {

    @Resource
    private AccountService accountService;
    @Resource
    private ConstantService constantService;

    @ApiOperation("PC查询资金账户列表")
    @PostMapping(value = "/pageAccount")
    public BizResult<PageData<AccountListRsp>> pageAccount(@CurrentUser CurrentUserReq userReq,
                                                           @RequestBody PageAccountReq pageAccountReq) {
        return accountService.pageAccount(pageAccountReq, userReq);
    }

    @ApiOperation("PC查询资金账户列表统计")
    @PostMapping(value = "/queryAccountAmount")
    public BizResult<AccountStatisticsDTO> queryAccountAmount(@CurrentUser CurrentUserReq userReq,
                                                              @RequestBody PageAccountReq pageAccountReq) {
        return accountService.queryAccountAmount(pageAccountReq, userReq);
    }

    @ApiOperation("PC冻结资金账户")
    @GetMapping(value = "/freeze")
    @Operate
    public BizResult<Boolean> freeze(@CurrentUser CurrentUserReq userReq,
                                     @RequestParam("accountId") Long accountId) {
        return accountService.freeze(accountId, userReq);
    }

    @ApiOperation("PC资金账户解冻")
    @GetMapping(value = "/unFreeze")
    @Operate
    public BizResult<Boolean> unFreeze(@CurrentUser CurrentUserReq userReq,
                                       @RequestParam("accountId") Long accountId) {
        return accountService.unFreeze(accountId, userReq);
    }

    @ApiOperation("PC资金账户-对象类型")
    @GetMapping(value = "/getObjectType")
    @Operate
    public BizResult<List<NameValueDTO>> getObjectType(@CurrentUser CurrentUserReq userReq) {
        return BizResult.create(constantService.getAccountObjectType(userReq));
    }

}
