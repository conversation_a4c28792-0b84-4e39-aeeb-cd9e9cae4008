package so.dian.fis.settle.controller.account.response;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import so.dian.himalaya.able.EnumInterface;

import java.util.List;

@Data
public class NameValueRsp<k, v> {

    private k id;
    private v value;
    /**
     * 子数据
     */
    private List<NameValueRsp<k, v>> subList;
    /**
     * 扩展信息字段
     */
    private JSONArray extList;

    public static NameValueRsp of(EnumInterface enumInterface) {
        return new NameValueRsp<>(enumInterface.getCode().toString(), enumInterface.getDesc());
    }

    public NameValueRsp(k id, v value) {
        this.id = id;
        this.value = value;
    }

    @Data
    public static class ExtDTO {
        private String label;
        private String value;
    }
}
