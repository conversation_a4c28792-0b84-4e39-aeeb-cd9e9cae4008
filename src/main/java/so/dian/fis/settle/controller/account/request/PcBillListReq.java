package so.dian.fis.settle.controller.account.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;

import java.util.Date;
import java.util.List;

/**
 * PcBillListQuery
 *
 * <AUTHOR>
 */
@Data
public class PcBillListReq extends PageReq {

    /**
     * 商家/渠道商 名称
     */
    private String billingMainBizName;

    /**
     * 账单id
     */
    private Long id;
    /**
     * 账单流水号
     */
    private String billNo;
    /**
     * 结算主体类型
     *
     * @see so.dian.lvy.pojo.enums.MainBizTypeEnum
     */
    @NotNull(message = "请选择结算主体类型")
    private Integer billingMainBizType;
    /**
     * 结算主体id
     */
    private List<Long> billingMainBizIds;
    /**
     * 账单名称
     */
    private String billName;
    /**
     * 账单周期类型
     *
     * @see so.dian.lvy.pojo.enums.BillPeriodTypeEnum
     */
    private Integer periodType;
    /**
     * 账单周期-开始时间
     */
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodStart;
    /**
     * 账单周期-结束时间
     */
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodEnd;
    /**
     * 生成方式
     *
     * @see so.dian.lvy.pojo.enums.BillGenerateTypeEnum
     */
    private Integer genType;
    /**
     * 账单业务类型：
     *
     * @see AccountBillBizTypeEnum
     */
    private List<String> bizTypeList;
    /**
     * 收支类型
     *
     * @see so.dian.lvy.pojo.enums.BalanceTypeEnum
     */
    private Integer balanceType;
    /**
     * 账单状态
     *
     * @see so.dian.lvy.pojo.enums.BillStatusEnum
     */
    private List<String> statusList;
    /**
     * 账单最小金额
     */
    private Integer amountMin;
    /**
     * 账单最大金额
     */
    private Integer amountMax;
    /**
     * 开始创建时间
     */
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createStart;
    /**
     * 结束创建时间
     */
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEnd;

    private Integer settleSubjectType = 1;

    private Long settleSubjectId = 1L;

    private Boolean isAgentDirectSign;

    /**
     * 账户类型 支持多选
     */
    private List<Integer> billAccountTypes;
}
