package so.dian.fis.settle.controller.account.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

/**
 * AccountExportReq
 *
 * <AUTHOR>
 * @desc
 * @date 2022/12/21 16:25
 */
@Data
@ToString
public class AccountExportReq implements Serializable {

    @ApiModelProperty(value = "主体id")
    private Long mainBizId;
    @ApiModelProperty(value = "主体名称")
    private String mainBizName;
    @ApiModelProperty(value = "资金账户No")
    private String accountNo;
    @NotNull(message = "账户类型不能为空")
    @ApiModelProperty(value = "资金账户类型")
    private Integer accountType;
    @ApiModelProperty(value = "创建时间结束")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeEnd;
    @ApiModelProperty(value = "创建时间开始")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeStart;
    @ApiModelProperty(value = "主体类型")
    private List<String> mainBizType;
    @ApiModelProperty(value = "结算方id:0友电 1北京伊电园 3杭州伊电园")
    private List<String> settleSubjectId;
    @ApiModelProperty(value = "是否冻结:1正常 2冻结 99已注销")
    private Integer status;


    private String email;
}
