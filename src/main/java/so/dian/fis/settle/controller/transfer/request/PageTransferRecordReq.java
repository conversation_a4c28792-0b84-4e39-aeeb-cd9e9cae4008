package so.dian.fis.settle.controller.transfer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.fis.settle.controller.account.request.PageReq;

/**
 * PageTransferRecordReq
 *
 * <AUTHOR>
 */
@ApiModel("划拨记录请求值")
@Data
public class PageTransferRecordReq extends PageReq {

    @ApiModelProperty(value = "抵扣记录id")
    private Long transferRecordId;
    @ApiModelProperty(value = "操作开始时间")
    private String operateStartTime;
    @ApiModelProperty(value = "操作结束时间")
    private String operateEndTime;

}
