package so.dian.fis.settle.controller.account.response;

import java.util.Date;
import lombok.Data;

/**
 * AccountListNewRsp
 *
 * <AUTHOR>
 */
@Data
public class AccountListNewRsp {

    private String accountNo;
    private Long accountId;
    private Integer accountType;
    private Long accountAmount;
    private Long availableAmount;
    private Date createTime;
    private Long freezeAmount;
    private String mainBizId;
    private String mainBizName;
    private Integer mainBizType;
    private String mainBizTypeStr;
    private Integer status;
    private String statusStr;
    private Long belongCompanyId;
    private String belongCompany;

    private Long id;
}
