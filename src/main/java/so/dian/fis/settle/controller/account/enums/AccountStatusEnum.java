package so.dian.fis.settle.controller.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 异常枚举 </p>
 * <p>业务线编码 + 5位数字 </p>
 * <p>F10000-F10999</p>
 *
 * <AUTHOR>
 * @date 2019-12-05 16:44
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备********号
 */
@Getter
@AllArgsConstructor
public enum AccountStatusEnum {

    //用户相关
    NORMAL(1, "正常"),
    FROZEN(2, "冻结"),
    CLOSED(99, "已注销");

    private Integer key;
    private String value;

    public static AccountStatusEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AccountStatusEnum levelEnum : AccountStatusEnum.values()) {
            if(levelEnum.getKey().equals(code)) {
                return levelEnum;
            }
        }
        return null;
    }
}
