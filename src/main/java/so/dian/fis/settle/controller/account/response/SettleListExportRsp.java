package so.dian.fis.settle.controller.account.response;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


@Data
public class SettleListExportRsp {
    @ExcelProperty("创建时间")
    private String createTime;
    @ExcelProperty("账单名称")
    private String billName;
    @ExcelProperty("周期类型")
    private String periodTypeName;
    @ExcelProperty("账单周期-开始时间")
    private String periodStartDate;
    @ExcelProperty("账单周期-结束时间")
    private String periodEndDate;
    @ExcelProperty("生成方式")
    private String genTypeName;
    @ExcelProperty("业务类型")
    private String bizTypeName;
    @ExcelProperty("收支类型")
    private String balanceTypeName;
    @ExcelProperty("结算金额(元)")
    private String settleAmount;
    @ExcelProperty("账户类型")
    private String billAccountTypeDesc;
    @ExcelProperty("结算主体类型")
    private String billingMainBizTypeName;
    @ExcelProperty("是否小电直签二代")
    private String agentDirectSignName;
    @ExcelProperty("普通商户ID")
    private Long billingMainBizId;
    @ExcelProperty("普通商户名称")
    private String billingMainBizName;
    @ExcelProperty("账单流水号")
    private String billNo;
    @ExcelProperty("状态")
    private String statusName;

}
