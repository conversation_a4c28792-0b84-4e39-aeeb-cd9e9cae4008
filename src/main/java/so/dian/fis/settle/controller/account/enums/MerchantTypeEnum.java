package so.dian.fis.settle.controller.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * <p> 新迁移账户类型 </p>
 *
 *
 * <AUTHOR>
 * @date 2022-07-14 16:44
 *
 */
@Getter
@AllArgsConstructor
public enum MerchantTypeEnum implements EnumInterface<MerchantTypeEnum> {

    NORMAL_MERCHANT(1, "普通商户"),
    BRAND_MERCHANT(2, "品牌商户"),
    JOIN_MERCHANT(3, "加盟商户"),
    AGENT(4, "代理商"),
    OPEN_SERVICE(5, "开户型服务商"),
    RESOURCE_SERVICE(6, "资源型服务商"),
    OPERATION_SERVICE(7, "运营型服务商"),
    DIAN_USERS(8, "消费者用户"),
    KP_SERVICE(9, "KP服务商"),
    IOT_MERCHANT(10, "IoT商户"),
    JV_CORP(11, "合资公司")

    ;

    private Integer code;
    private String desc;

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    @Override
    public MerchantTypeEnum getDefault() {
        return null;
    }

    public static Integer getRickMerchantCode(Integer code) {
        if(code == null) {
            return null;
        }
        for (MerchantTypeEnum mainBizTypeEnum : MerchantTypeEnum.values()) {
            if (mainBizTypeEnum.getCode().equals(code)) {
                return 1;
            }
        }
        return null;
    }
}
