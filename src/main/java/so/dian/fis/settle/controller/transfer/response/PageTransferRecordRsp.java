package so.dian.fis.settle.controller.transfer.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PageTransferRecordRsp
 *
 * <AUTHOR>
 */
@ApiModel("划拨记录返回值列表")
@Data
public class PageTransferRecordRsp{
    @ApiModelProperty(value = "抵扣记录id")
    private Long id;

    @ApiModelProperty(value = "抵扣账期")
    private String billMonth;

    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty(value = "操作人")
    private String operaterName;

    @ApiModelProperty(value = "操作时间")
    private String operaterTime;
}
