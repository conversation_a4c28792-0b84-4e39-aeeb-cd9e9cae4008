package so.dian.fis.settle.controller.account.response;

import lombok.Data;

import java.util.Date;

/**
 * AccountBalancePcRsp
 *
 * <AUTHOR>
 */
@Data
public class AccountBalancePcRsp {

    private Long id;
    /**
     * 交易时间
     */
    private Date balanceTime;
    /**
     * 收入 / 支出
     */
    private String balanceTypeName;
    /**
     * 1:收入 2:支出
     */
    private Integer balanceType;
    /**
     * 交易金额
     */
    private Long balanceAmount;
    /**
     * 费用项名称
     */
    private String bizDocTypeName;
    /**
     * 费用项:
     *
     * @see so.dian.lvy.pojo.enums.BalanceBizDocTypeEnum
     */
    private Integer bizDocType;
    /**
     * 业务单号
     */
    private String bizDocNo;
    /**
     * 交易单号
     */
    private String balanceNo;

    private Long settleSubjectId;

    private Integer settleSubjectType;
}
