package so.dian.fis.settle.controller.account.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * PageAccountQuery
 *
 * <AUTHOR>
 */
@ApiModel("账单列表")
@Data
public class PageAccountReq extends PageReq {
    @ApiModelProperty(value = "主体id")
    private Long mainBizId;
    @ApiModelProperty(value = "主体名称")
    private String mainBizName;
    @ApiModelProperty(value = "账户余额最大（元）")
    private Long accountAmountMax;
    @ApiModelProperty(value = "账户余额最小（元）")
    private Long accountAmountMin;
    @ApiModelProperty(value = "账户可用余额最大（元）")
    private Long availableAmountMax;
    @ApiModelProperty(value = "账户可用余额最小（元）")
    private Long availableAmountMin;
    @ApiModelProperty(value = "账户冻结金额最大（元）")
    private Long freezeAmountMax;
    @ApiModelProperty(value = "账户冻结金额最小（元）")
    private Long freezeAmountMin;
    @ApiModelProperty(value = "资金账户id")
    private Long accountId;
    @ApiModelProperty(value = "创建时间结束")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeEnd;
    @ApiModelProperty(value = "创建时间开始")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeStart;
    @ApiModelProperty(value = "主体类型")
    private Integer mainBizType;
    @ApiModelProperty(value = "主体id列表")
    private List<Long> mainBizIdList;
    @ApiModelProperty(value = "结算方类型:1小电 2代理商 3运营型服务商")
    private Integer settleSubjectType;
    @ApiModelProperty(value = "结算方id:0友电 1北京伊电园 3杭州伊电园")
    private Long settleSubjectId;
    @ApiModelProperty(value = "是否冻结:0正常 1冻结")
    private Integer freeze;
}
