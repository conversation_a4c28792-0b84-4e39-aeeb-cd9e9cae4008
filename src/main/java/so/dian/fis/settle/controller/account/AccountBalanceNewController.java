package so.dian.fis.settle.controller.account;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.fis.settle.biz.service.AccountBalanceNewService;
import so.dian.fis.settle.common.aop.annotation.CurrentUser;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.FrozenAmountReq;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceNewReq;
import so.dian.fis.settle.controller.account.request.UnfrozenAmountReq;
import so.dian.fis.settle.controller.account.response.AccountBalancePcRsp;
import so.dian.fis.settle.controller.account.response.BalanceTypeRsp;
import so.dian.himalaya.boot.aop.annotation.Operate;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;

import java.util.List;

@Api(value = "AccountController", description = "新资金账户API")
@RestController
@Slf4j
@RequestMapping("/accountBalanceNew")
public class AccountBalanceNewController {

    @Resource
    private AccountBalanceNewService accountBalanceNewService;

    @ApiOperation("新PC查询资金账户流水列表")
    @PostMapping(value = "/pageForPc")
    public BizResult<PageData<AccountBalancePcRsp>> pageAccountBalanceForPc(@CurrentUser CurrentUserReq userReq,
                                                                            @RequestBody PageAccountBalanceNewReq pageAccountBalanceReq) {
        return accountBalanceNewService.pageAccountBalanceReq(userReq, pageAccountBalanceReq);
    }

    @ApiOperation("新PC查询资金账户流水类型列表")
    @GetMapping(value = "/newAccountBalanceType")
    public BizResult<List<BalanceTypeRsp>> newAccountBalanceType(@CurrentUser CurrentUserReq userReq,
                                                                 @RequestParam("accountType") Integer accountType) {
        return BizResult.create(accountBalanceNewService.newAccountBalanceType(userReq, accountType));
    }

    @ApiOperation("冻结账户金额")
    @PostMapping(value = "/frozenAmount")
    @Operate
    public BizResult<Boolean> frozenAmount(@CurrentUser CurrentUserReq userReq, @RequestBody FrozenAmountReq req) {
        return BizResult.create(accountBalanceNewService.frozenAmount(userReq, req));
    }

    @ApiOperation("解冻账户金额")
    @PostMapping(value = "/unfrozenAmount")
    @Operate
    public BizResult<Boolean> unfrozenAmount(@CurrentUser CurrentUserReq userReq, @RequestBody UnfrozenAmountReq req) {
        return BizResult.create(accountBalanceNewService.unfrozenAmount(userReq, req));
    }

    @ApiOperation("获取当前账户手动冻结金额总和")
    @GetMapping(value = "/frozenAmount/total")
    @Operate
    public BizResult<Long> getTotalFrozenAmount(@RequestParam("accountNo") String accountNo, @RequestParam("accountType") Integer accountType) {
        return BizResult.create(accountBalanceNewService.getTotalFrozenAmount(accountNo, accountType));
    }

}
