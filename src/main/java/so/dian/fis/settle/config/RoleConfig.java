package so.dian.fis.settle.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * RoleConfig
 *
 * <AUTHOR>
 * @desc
 * @date 2023/12/25 10:11
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "user.role.display")
public class RoleConfig {

    private List<String> accountTypeDisplay;
}
