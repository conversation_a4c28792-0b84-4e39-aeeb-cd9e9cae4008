package so.dian.fis.settle.config;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import so.dian.mofa3.lang.domain.Result;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Collections;
import java.util.List;

/**
 * @Author: ahuang
 * @CreateTime: 2024-11-25 17:40
 * @Description:
 */
@Slf4j
@Component
public class CommonFallbackFactory<T> implements FallbackFactory<T> {

    @Override
    @SuppressWarnings("unchecked")
    public T create(Throwable cause) {
        // 动态代理生成目标对象的降级实现
        return (T) Proxy.newProxyInstance(
                this.getClass().getClassLoader(),
                new Class<?>[]{},
                new ResultFallbackHandler(cause)
        );
    }

    /**
     * 动态代理的核心处理逻辑
     */
    private record ResultFallbackHandler(Throwable cause) implements InvocationHandler {

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) {
            log.error("Feign Client fallback triggered for method: {}. Cause: {}",
                    method.getName(),
                    cause != null ? cause.getMessage() : "Unknown error",
                    cause);

            // 创建并返回降级响应
            return createFallbackResult(method, args);
        }

        /**
         * 创建默认的降级响应 Result<T>
         */
        private <R> Result<R> createFallbackResult(Method method, Object[] args) {
            String methodName = method.getName();
            String inputArgs = args != null ? safeJsonSerialize(args) : "[]";
            log.error("Fallback executed for method: {}, with args: {}", methodName, inputArgs);

            Result<R> result = new Result<>();
            result.setSuccess(false); // 标记失败
            result.setMsg("Fallback executed for method: " + methodName); // 降级描述
            result.setData(createDefaultData(method)); // 数据部分返回空或默认值
            return result;
        }

        /**
         * 安全的 JSON 序列化工具方法
         */
        private String safeJsonSerialize(Object obj) {
            try {
                return JSONObject.toJSONString(obj);
            } catch (Exception e) {
                log.warn("Failed to serialize object to JSON: {}", obj, e);
                return "Serialization error";
            }
        }

        /**
         * 根据方法返回类型创建默认数据
         */
        @SuppressWarnings("unchecked")
        private <R> R createDefaultData(Method method) {
            Class<?> returnType = method.getReturnType();
            if (returnType.equals(String.class)) {
                return (R) "Default fallback response";
            } else if (returnType.equals(Integer.class)) {
                return (R) Integer.valueOf(0);
            } else if (returnType.equals(List.class)) {
                return (R) Collections.emptyList();
            } else if (returnType.equals(Boolean.class)) {
                return (R) Boolean.FALSE;
            }
            // 默认返回 null
            return null;
        }
    }
}