package so.dian.fis.settle.config;

import feign.micrometer.MicrometerObservationCapability;
import io.micrometer.observation.ObservationPredicate;
import io.micrometer.observation.ObservationRegistry;
import io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.actuate.autoconfigure.tracing.otlp.OtlpProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.server.observation.ServerRequestObservationContext;

import java.util.Objects;

/**
 * OpenTelemetryConfig.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Configuration
public class OpenTelemetryConfig {

  @Resource
  private OtlpProperties otlpProperties;

  /**
   * 过滤掉actuator的监控
   *
   * @return ObservationPredicate
   */
  @Bean
  ObservationPredicate noActuatorServerObservations() {
    return (name, context) -> {
      if (name.equals("http.server.requests")
          && context instanceof ServerRequestObservationContext serverContext) {
        return !serverContext.getCarrier().getRequestURI().startsWith("/actuator")
            || !serverContext.getCarrier().getRequestURI().startsWith("/api-docs")
            || !serverContext.getCarrier().getRequestURI().startsWith("/swagger-ui")
            || serverContext.getCarrier().getMethod().equals("OPTIONS");
      } else {
        return Objects.nonNull(context.getParentObservation());
      }
    };
  }

  /**
   * 添加Feign可观测能力
   *
   * @param registry registry
   * @return MicrometerObservationCapability
   */
  @Bean
  public MicrometerObservationCapability micrometerObservationCapability(
      ObservationRegistry registry) {
    return new MicrometerObservationCapability(registry);
  }

  @Bean
  OtlpGrpcSpanExporter otlpGrpcSpanExporter() {
    return OtlpGrpcSpanExporter.builder()
        .setEndpoint(otlpProperties.getEndpoint())
        .setTimeout(otlpProperties.getTimeout())
        .setHeaders(() -> otlpProperties.getHeaders())
        .build();
  }

}
