package so.dian.fis.settle.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * BizConfig
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:29
 */
@RefreshScope
@Data
@Configuration
public class BizConfig {

    @Value("${card.month.quota.total}")
    private Long totalQuota;

}
