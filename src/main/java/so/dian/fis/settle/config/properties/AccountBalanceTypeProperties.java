package so.dian.fis.settle.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "fis.account")
public class AccountBalanceTypeProperties {

    /**
     * 新的账户流水类型
     */
    private Map<Integer,List<Integer>> newAccountBalanceTypeMap;

}