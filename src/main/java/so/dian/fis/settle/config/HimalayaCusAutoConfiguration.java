package so.dian.fis.settle.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import so.dian.himalaya.boot.properties.ApplicationProperties;

/**
 * @Author: ahuang
 * @CreateTime: 2024-11-04 18:57
 * @Description:
 */
@Configuration
public class HimalayaCusAutoConfiguration {

    @Bean
    @Order(1)
    @ConditionalOnMissingBean(ApplicationProperties.class)
    public ApplicationProperties applicationProperties() {
        return new ApplicationProperties();
    }

}
