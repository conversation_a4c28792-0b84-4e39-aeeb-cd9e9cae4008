package so.dian.fis.settle.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: ahuang
 * @CreateTime: 2025-03-04 11:26
 * @Description:
 */
@Data
@Component
@ConfigurationProperties(prefix = "frontend-split")
public class XdBizCommonsFrontendSplitProperties {

    private AccessControl accessControl;

    @Data
    public static class AccessControl {
        private List<Long> allowedAgentId;
    }
}
