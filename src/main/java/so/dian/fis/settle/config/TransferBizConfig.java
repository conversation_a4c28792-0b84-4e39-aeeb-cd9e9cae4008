package so.dian.fis.settle.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * TransferBizConfig
 *
 * <AUTHOR>
 * @desc
 * @date 2024/08/19 16:29
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "transfer")
public class TransferBizConfig {

    private List<Long> userIds;

}
