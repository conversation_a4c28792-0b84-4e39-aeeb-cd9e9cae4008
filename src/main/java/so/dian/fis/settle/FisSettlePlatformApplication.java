package so.dian.fis.settle;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableConfigurationProperties
@EnableFeignClients(basePackages = "so.dian.fis.settle.remote")
public class FisSettlePlatformApplication {


    public static void main(String[] args) {
        SpringApplication.run(FisSettlePlatformApplication.class, args);
    }
}
