package so.dian.fis.settle.facade;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.common.logger.util.JsonUtils;
import so.dian.fis.settle.remote.WutaiClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.wutai.client.pojo.dto.*;

import java.util.List;

/**
 * WutaiIdCardStaticsFacade
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:13
 */
@Slf4j
@Component
public class WutaiFacade {

    @Autowired
    private WutaiClient wutaiClient;

    public void accountOperationLog(List<AccountOperationLogDTO> accountOperationLogDTOList) {
        BizResult<Boolean> result = wutaiClient.accountOperationLog(accountOperationLogDTOList);
        if (!result.isSuccess()) {
            log.error("accountOperationLog失败，req:{}", JSONObject.toJSONString(accountOperationLogDTOList));
            log.error("accountOperationLog失败，bizResult:{}", JSONObject.toJSONString(result));
        }
    }

    public IdCardPaymentAmountDTO staticsMonthAmount(IdCardPaymentAmountQueryDTO query) {
        BizResult<IdCardPaymentAmountDTO> result = wutaiClient.staticsMonthWithdrawAmount(query);
        log.info("WutaiIdCardStaticsFacade.WutaiIdCardStaticsFacade query:{},result:{}",
                JsonUtils.objectToString(query),
                JsonUtils.objectToString(result));
        return result.getData();
    }

    public PageData<TransferRecordPageRspDTO> transferRecordPage(TransferRecordPageDTO req) {
        BizResult<PageData<TransferRecordPageRspDTO>> bizResult = wutaiClient.transferRecordPage(req);
        if (!bizResult.isSuccess()) {
            log.error("分页查询划拨记录页面失败，bizResult:{}", JSONObject.toJSONString(bizResult));
            return PageData.create(Lists.newArrayList(), 0L, (long)req.getPageNo(), req.getPageSize());
        }
        return bizResult.getData();
    }

    public PageData<TransferRecordDetailPageRspDTO> transferRecordDetailPage(TransferRecordDetailPageDTO req) {

        BizResult<PageData<TransferRecordDetailPageRspDTO>> bizResult = wutaiClient.transferRecordDetailPage(req);
        if (!bizResult.isSuccess()) {
            log.error("分页查询划拨记录详情页面失败，bizResult:{}", JSONObject.toJSONString(bizResult));
            return PageData.create(Lists.newArrayList(), 0L, (long)req.getPageNo(), req.getPageSize());
        }
        return bizResult.getData();
    }

    public BizResult<Boolean> transferRecordDetailExport(TransferRecordDetailExportDTO req) {
        BizResult<Boolean> bizResult = wutaiClient.transferRecordDetailExport(req);
        if (!bizResult.isSuccess()) {
            log.error("transferRecordDetailExport，bizResult:{}", JSONObject.toJSONString(bizResult));
            return BizResult.create(false);
        }
        return bizResult;
    }

    public Boolean transferRecordExecute(Long operatorId) {
        BizResult<Boolean> bizResult = wutaiClient.transferRecordExecute(operatorId);
        if (!bizResult.isSuccess()) {
            log.error("transferRecordDetailExport，bizResult:{}", JSONObject.toJSONString(bizResult));
            return false;
        }
        return bizResult.getData();
    }

    public String transferRecordCheck() {
        BizResult<String> bizResult = wutaiClient.transferRecordCheck();
        if (!bizResult.isSuccess()) {
            log.error("transferRecordCheck，bizResult:{}", JSONObject.toJSONString(bizResult));
            return "接口校验系统异常";
        }
        return bizResult.getData();
    }

}
