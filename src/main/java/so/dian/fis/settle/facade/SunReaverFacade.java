package so.dian.fis.settle.facade;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.common.rpc.result.DianResult;
import so.dian.fis.settle.common.pojo.dto.TenantUserAdminAgentDTO;
import so.dian.fis.settle.remote.SunReaverClient;
import so.dian.sunreaver.api.req.TenantUserAdminAgentReq;
import so.dian.sunreaver.api.resp.TenantUserAdminAgentResp;
@Slf4j
@Component
public class SunReaverFacade {
    @Autowired
    private SunReaverClient sunReaverClient;
    /**
     * 查询授权代理
     */
    public TenantUserAdminAgentResp getTenantUserAdminAgent(TenantUserAdminAgentDTO tenantUserAdminAgentDTO) {
        log.info("getTenantUserAdminAgent get tenantUserAdminAgentDTO:{}", JSONObject.toJSON(tenantUserAdminAgentDTO));
        TenantUserAdminAgentReq req = new TenantUserAdminAgentReq();
        BeanUtils.copyProperties(tenantUserAdminAgentDTO,req);
        DianResult<TenantUserAdminAgentResp> result = sunReaverClient.findAuthorizeAgentIdsByUser(req);
        log.info("getTenantUserAdminAgent get result:{}", JSONObject.toJSON(result));
        if (!result.getSuccess()) {
            log.error("getTenantUserAdminAgent tenantUserAdminAgentDTO:{}, result:{}",
                    JSONObject.toJSONString(tenantUserAdminAgentDTO), result);
            return null;
        }
        return result.getData();
    }
}
