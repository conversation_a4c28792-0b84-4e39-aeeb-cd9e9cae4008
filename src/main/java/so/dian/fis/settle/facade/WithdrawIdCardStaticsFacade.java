package so.dian.fis.settle.facade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.common.logger.util.JsonUtils;
import so.dian.fis.settle.remote.WithdrawPlatformClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.withdraw.platform.dto.IdCardWithdrawAmountDTO;
import so.dian.withdraw.platform.query.IdCardWithdrawAmountQueryDTO;

/**
 * WithdrawIdCardStaticsFacade
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:13
 */
@Slf4j
@Component
public class WithdrawIdCardStaticsFacade {

    @Autowired
    private WithdrawPlatformClient withdrawPlatformClient;


    public IdCardWithdrawAmountDTO staticsMonthAmount(IdCardWithdrawAmountQueryDTO query) {
        BizResult<IdCardWithdrawAmountDTO> bizResult = withdrawPlatformClient.staticsMonthWithdrawAmount(query);
        log.info("WithdrawIdCardStaticsFacade.staticsMonthAmount body : {},result:{}", JsonUtils.objectToString(query),
                JsonUtils.objectToString(bizResult));
        return bizResult.getData();
    }

}
