package so.dian.fis.settle.facade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.common.logger.util.JsonUtils;
import so.dian.fis.settle.remote.PapIdCardStaticsClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.payapply.platform.dto.IdCardPayApplyAmountQueryDTO;
import so.dian.payapply.platform.dto.IdCardPayApplyAmountStaticsDTO;

/**
 * PapIdCardStaticsFacade
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:13
 */
@Slf4j
@Component
public class PapIdCardStaticsFacade {

    @Autowired
    private PapIdCardStaticsClient papIdCardStaticsClient;


    public IdCardPayApplyAmountStaticsDTO staticsMonth(IdCardPayApplyAmountQueryDTO queryDTO) {
        BizResult<IdCardPayApplyAmountStaticsDTO> bizResult = papIdCardStaticsClient.staticsMonthPayApplyAmount(
                queryDTO);
        log.info("PapIdCardStaticsFacade.staticsMonth query:{},result:{}", JsonUtils.objectToString(queryDTO),
                JsonUtils.objectToString(bizResult));
        return bizResult.getData();
    }

}
