package so.dian.fis.settle.facade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.contract.explain.client.dto.agent.AgentContractInfoDTO;
import so.dian.contract.explain.client.dto.constant.SecondaryAgentSignEnum;
import so.dian.fis.settle.common.exception.BillErrorCodeEnum;
import so.dian.fis.settle.remote.ContractExplainClient;
import so.dian.himalaya.common.exception.BizException;

import java.util.Objects;

/**
 * ContractExplainClient
 *
 * <AUTHOR>
 * @desc
 * @date 2023/10/13 10:12
 */
@Slf4j
@Component
public class ContractExplainFacade {

    @Autowired
    private ContractExplainClient contractExcplainClient;


    public Boolean isProxyPay(Long agentId) {
        log.info("ContractExplainFacade isProxyPay get  agentId:{}", agentId);
        BizResult<AgentContractInfoDTO> bizResult = contractExcplainClient.getSecondaryAgentContractInfo(agentId);
        log.info("ContractExplainFacade isProxyPay get result:{}", bizResult);
        if (Objects.isNull(bizResult) || Objects.isNull(bizResult.getData()) ||
                // 无合同(生效，失效合同都没有)
                (Objects.isNull(bizResult.getData().getEffectContractSignType()) && Objects.isNull(bizResult.getData().getLastInvalidContractSignType()))) {
            log.error("isProxyPay is failed agentId:{},cause:{}", agentId, BillErrorCodeEnum.NO_RELATED_CONTRACT.getDesc());
            throw BizException.create(BillErrorCodeEnum.NO_RELATED_CONTRACT);
        }
        // 生效的合同
        SecondaryAgentSignEnum secondaryAgentSignEnum = bizResult.getData().getEffectContractSignType();
        if (Objects.nonNull(secondaryAgentSignEnum)) {
            return isProxyContract(secondaryAgentSignEnum);
        }
        // 没有生效合同取最近一份失效合同
        return isProxyContract(bizResult.getData().getLastInvalidContractSignType());
    }

    /**
     * 代付合同
     */
    private Boolean isProxyContract(SecondaryAgentSignEnum secondaryAgentSignEnum){
        return SecondaryAgentSignEnum.SECONDARY_AGENT_DF.equals(secondaryAgentSignEnum);
    }

}
