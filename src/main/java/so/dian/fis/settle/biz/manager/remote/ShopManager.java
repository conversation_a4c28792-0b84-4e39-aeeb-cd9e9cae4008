package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import so.dian.entity.dto.ShopInfoDTO;
import so.dian.fis.settle.remote.ShopClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.util.LocalMapUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * ShopManager
 *
 * <AUTHOR>
 */
@Slf4j(topic = "remote")
@Service
public class ShopManager {

    @Resource
    private ShopClient shopClient;

    public Map<Long, String> getShopIdNameMapByIdList(List<Long> idList) {
        List<ShopInfoDTO> shopInfoDTOList = listByIdList(idList);
        return LocalMapUtils.listAsHashMap(shopInfoDTOList, ShopInfoDTO::getShopId, ShopInfoDTO::getShopName);
    }

    public Map<Long, ShopInfoDTO> getShopIdMapByIdList(List<Long> idList) {
        List<ShopInfoDTO> shopInfoDTOList = listByIdList(idList);
        return LocalMapUtils.listAsHashMap(shopInfoDTOList, ShopInfoDTO::getShopId);
    }

    public List<ShopInfoDTO> listByIdList(List<Long> idList) {
        if(CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        BizResult<List<ShopInfoDTO>> shopResult = shopClient.queryShopByShopIds(idList);
        if(Objects.isNull(shopResult)) {
            log.error("远程根据idList称获取门店列表失败,idList:{}", idList);
            return Lists.newArrayList();
        }
        if(!shopResult.isSuccess()) {
            log.error("远程根据idList称获取商户列表,接口返回错误信息,idList:{},result:{}", idList, shopResult);
            return Lists.newArrayList();
        }
        return shopResult.getData();
    }

}
