package so.dian.fis.settle.biz.manager.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.fis.settle.remote.HrClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;

import jakarta.annotation.Resource;
import java.util.Objects;

@Slf4j(topic = "remote")
@Service
public class HrManager {

    @Resource
    private HrClient hrClient;

    /**
     * 获取员工信息
     */
    public AgentEmployeeDTO getEmployeeById(Long employeeId) {
        if (Objects.isNull(employeeId)) {
            log.error("远程员工信息 | 员工ID不能为空 | employeeId:{}", employeeId);
            return null;
        }
        BizResult<AgentEmployeeDTO> bizResult = hrClient.getById(employeeId);
        if (Objects.isNull(bizResult)) {
            log.error("远程员工信息 | fallback | employeeId:{}", employeeId);
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("远程员工信息 | 接口返回错误信息 | employeeId:{},result:{}", employeeId, bizResult);
            return null;
        }
        AgentEmployeeDTO agentEmployeeDTO = bizResult.getData();
        if (Objects.isNull(agentEmployeeDTO)) {
            log.error("远程员工信息 | 用户不存在 | employeeId:{},result:{}", employeeId, bizResult);
            return null;
        }
        return agentEmployeeDTO;
    }
}
