package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.agent.api.constant.AgentBaseEnum;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.agent.api.dto.PageDTO;
import so.dian.agent.api.dto.param.AgentQueryParamDTO;
import so.dian.agent.api.dto.request.QueryAgentConstant;
import so.dian.agent.api.dto.request.QueryAgentListRequest;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.fis.settle.remote.AgentClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.util.LocalMapUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j(topic = "remote")
@Service
public class AgentManager {

    @Resource
    private AgentClient agentClient;

    /**
     * 获取代理商信息
     */
    public AgentDTO findById(Long agentId) {
        if (Objects.isNull(agentId)) {
            log.error("远程获取代理商信息 | 代理商ID不能为 | agentId:{}", agentId);
            return null;
        }
        BizResult<AgentDTO> bizResult = agentClient.getAgentById(agentId);
        log.info("AgentManager  findById get bizResult:{}",bizResult);
        if (Objects.isNull(bizResult)) {
            log.error("远程获取代理商信息失败,agentId:{}", agentId);
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("远程获取代理商信息,接口返回错误信息,agentId:{},result:{}", agentId, bizResult);
            return null;
        }
        AgentDTO agentDTO = bizResult.getData();
        if (Objects.isNull(agentDTO)) {
            log.error("远程获取代理商信息,用户不存在,agentId:{},result:{}", agentId, bizResult);
            return null;
        }
        return agentDTO;
    }

    public String getNameById(Long id) {
        AgentDTO agentDTO = findById(id);
        if(Objects.isNull(agentDTO)) {
            return null;
        }
        return agentDTO.getAgentName();
    }

    public List<Long> getIdsByName(String name, Integer mainBizType) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        Integer agentType = null;
        // 类型无法对应到agentType，返回空列表
        if (Objects.nonNull(mainBizType)) {
            agentType = MainBizTypeUtils.getAgentTypeByMainBizType(mainBizType);
            if (Objects.isNull(agentType)) {
                return Lists.newArrayList();
            }
        }
        BizResult<List<Long>> agentIdListResult = agentClient.getIdsByName(name, agentType);
        if (Objects.isNull(agentIdListResult)) {
            log.error("远程根据代理商名称获取代理商信息失败,agentName:{},mainBizType:{}", name, mainBizType);
            return null;
        }
        if (!agentIdListResult.isSuccess()) {
            log.error("远程根据代理商名称获取代理商信息失败,agentName:{},mainBizType:{},result:{}", name, mainBizType, agentIdListResult);
            return null;
        }
        if (CollectionUtils.isEmpty(agentIdListResult.getData())) {
            return Lists.newArrayList();
        }
        return agentIdListResult.getData();
    }

    public Map<Long, String> getIdNameMap(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Maps.newHashMap();
        }
        List<AgentDTO> newByIds = findNewByIds(idList);
        if(CollectionUtils.isEmpty(newByIds)){
            return Maps.newHashMap();
        }
        return LocalMapUtils.listAsHashMap(newByIds, AgentDTO::getAgentId, AgentDTO::getAgentName);
    }

    public List<AgentDTO> findNewByIds(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            log.error("远程获取代理商列表信息 | 代理商ID不能为 | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        agentIds = agentIds.stream().distinct().collect(Collectors.toList());
        QueryAgentListRequest request = new QueryAgentListRequest();
        request.setAgentIds(agentIds);
        QueryAgentConstant queryAgentConstant = new QueryAgentConstant();
        queryAgentConstant.setIgnoreStatus(true);
        request.setQueryAgentConstant(queryAgentConstant);
        BizResult<List<AgentDTO>> bizResult = agentClient.getAgentByIdsAndOptions(request);
        log.info("findNewByIds get bizResult:{},agentIds:{}",bizResult,agentIds);
        if (Objects.isNull(bizResult)) {
            log.error("远程获取代理商列表信息 | fallback | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        if (!bizResult.isSuccess()) {
            log.error("远程获取代理商列表信息 | 接口返回错误信息 | agentIds:{},result:{}", agentIds, bizResult);
            return new ArrayList<>();
        }
        List<AgentDTO> agentDTOList = bizResult.getData();
        if (CollectionUtils.isEmpty(agentDTOList)) {
            log.error("远程获取代理商列表信息 | 用户不存在 | agentIds:{},result:{}", agentIds, bizResult);
            return new ArrayList<>();
        }
        return agentDTOList;
    }

    public List<AgentDTO> listAgentByType(@NonNull AgentBaseEnum.AgentTypeEnum type) {

        AgentQueryParamDTO paramDTO = new AgentQueryParamDTO();
        ArrayList<Integer> types = Lists.newArrayList();
        types.add(type.getId());
        int pageNo = 0;
        ArrayList<Integer> subTypes = Lists.newArrayList();
        paramDTO.setTypeList(types);
        paramDTO.setSubTypeList(subTypes);
        paramDTO.setPageNo(pageNo);
        paramDTO.setPageSize(100);
        List<AgentDTO> list = new ArrayList<>();
        BizResult<PageDTO<AgentDTO>> bizResult = agentClient.listChannelByType(paramDTO);
        while (!Objects.isNull(bizResult) && bizResult.isSuccess()&& !CollectionUtils.isEmpty(bizResult.getData().getList()) && bizResult.getData().getList().size() > 0){
            if (Objects.isNull(bizResult)) {
                log.error("远程获取agent信息 | fallback | paramDTO:{}", paramDTO);
                return Lists.newArrayList();
            }
            if (!bizResult.isSuccess()) {
                log.error("远程获取agent信息 | 接口返回错误信息 | paramDTO:{},result:{}", paramDTO, bizResult);
                return Lists.newArrayList();
            }
            PageDTO<AgentDTO> resultData = bizResult.getData();
            if (Objects.isNull(resultData)) {
                log.error("远程获取agent信息 | agent信息不存在 | paramDTO:{},result:{}", paramDTO, bizResult);
                return Lists.newArrayList();
            }
            list.addAll(resultData.getList());
            paramDTO.setPageNo(++ pageNo);
            bizResult = agentClient.listChannelByType(paramDTO);
        }
        return list;
    }
}
