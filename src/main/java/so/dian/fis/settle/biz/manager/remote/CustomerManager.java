package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.customer.dto.MerchantDTO;
import so.dian.customer.dto.request.AccountMerchantQueryDTO;
import so.dian.fis.settle.remote.CustomerClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.himalaya.util.LocalMapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * CustomerManager
 *
 * <AUTHOR>
 */
@Slf4j(topic = "remote")
@Component
public class CustomerManager {

    @Resource
    private CustomerClient customerClient;

    public List<Long> getMerchantIdsByMerchantName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        AccountMerchantQueryDTO queryDTO = new AccountMerchantQueryDTO();
        queryDTO.setName(name);
        queryDTO.setPageSize(1000);
        queryDTO.setOffset(0);
        BizResult<List<MerchantDTO>> result = customerClient.listMerchantByCondition(queryDTO);
        if (Objects.isNull(result)) {
            log.error("远程根据商户名称获取商户列表失败,query:{}", queryDTO);
            return null;
        }
        if (!result.isSuccess()) {
            log.error("远程根据商户名称获取商户列表,接口返回错误信息,name:{},result:{}", name, result);
            return null;
        }
        return LocalListUtils.transferList(result.getData(), MerchantDTO::getReferId);
    }

    public Map<Long, String> getIdNameMap(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Maps.newHashMap();
        }
        BizResult<List<MerchantDTO>> result = customerClient.listMerchantByMerchantIds(idList);
        if (Objects.isNull(result)) {
            log.error("远程根据idList称获取商户列表失败,idList:{}", idList);
            return Maps.newHashMap();
        }
        if (!result.isSuccess()) {
            log.error("远程根据idList称获取商户列表,接口返回错误信息,idList:{},result:{}", idList, result);
            return Maps.newHashMap();
        }
        return LocalMapUtils.listAsHashMap(result.getData(), MerchantDTO::getReferId, MerchantDTO::getName);
    }

    public String getNameById(Long id) {
        if(Objects.isNull(id)) {
            return null;
        }
        BizResult<List<MerchantDTO>> result = customerClient.listMerchantByMerchantIds(Lists.newArrayList(id));
        if (Objects.isNull(result)) {
            log.error("远程根据id称获取商户失败,id:{}", id);
            return null;
        }
        if (!result.isSuccess()) {
            log.error("远程根据id称获取商户,接口返回错误信息,id:{},result:{}", id, result);
            return null;
        }
        List<MerchantDTO> merchantDTOList = result.getData();
        if(CollectionUtils.isEmpty(merchantDTOList)) {
            return null;
        }
        return merchantDTOList.get(0).getName();
    }
}
