package so.dian.fis.settle.biz.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.center.common.util.DateUtil;
import so.dian.fis.settle.biz.manager.MailManager;
import so.dian.fis.settle.biz.manager.MainBizInfoManager;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.response.SettleListExportRsp;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.query.BillListQuery;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ExcelHandler {
    @Autowired
    private LvyManager lvyManager;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private MainBizInfoManager mainBizInfoManager;

    /**
     * 结算账单导出
     */
    public void SettlementExport(BillListQuery billListQuery, CurrentUserReq userReq,int count) {
        if (Objects.isNull(billListQuery)) {
            return;
        }
        int pageNo = 1;
        int pageSize = 200;
        String path = "/var/tmp/结算账单";
        File tempFile;
        try {
            tempFile = File.createTempFile(path, ".xlsx");
        } catch (IOException e) {
            log.error("create temp file error.", e);
            return;
        }
        ExcelWriter writer = EasyExcel.write(tempFile).build();
        // 使用 LinkedHashMap 通过唯一 ID 去重，保持顺序
        Map<String, SettleListExportRsp> resultMap = new LinkedHashMap<>();
        // 循环保护，防止死循环
        int i = 51;
        while (count > 0 && i > 0) {
            billListQuery.setPageNo(pageNo);
            billListQuery.setPageSize(pageSize);
            List<SettleListExportRsp> currentPage = listExportRsp(lvyManager.pageBillList(billListQuery));
            for (SettleListExportRsp item : currentPage) {
                // 假设 getId() 是唯一标识字段
                resultMap.putIfAbsent(item.getBillNo(), item);
            }
            count = count - pageSize;
            pageNo = pageNo+1;
            i = i - 1;
        }
        log.info("SettlementExport get pageNo:{},pageSize:{}",pageNo,pageSize);
        writer.write(new ArrayList<>(resultMap.values()), EasyExcel.writerSheet("结算账单").head(SettleListExportRsp.class).build());
        writer.close();
        try {
            mailManager.sendEmail(userReq.getUserMail(), "结算账单导出",
                    "结算账单已导出,请查收" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"),
                    tempFile);
        } catch (IOException e) {
           log.error("发送邮件失败usermail:{}", userReq.getUserMail(),e);
        }
    }

    /**
     * 批量转化

     */
    private List<SettleListExportRsp> listExportRsp (List<BillDTO> list){
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 获取普通商户名称
        List<Long> mainBizIdList = LocalListUtils.transferList(list, BillDTO::getBillingMainBizId);
        Integer mainBizType =  list.get(0).getBillingMainBizType();
        Map<Long, String> mainBizIdNameMap = mainBizInfoManager.getIdNameMap(mainBizType, mainBizIdList);
        return list.stream().filter(Objects::nonNull).map(k-> getSettleListExportRsp(k,mainBizIdNameMap)).collect(Collectors.toList());
    }

    /**
     * 单个对象转化
     */
    private SettleListExportRsp getSettleListExportRsp(BillDTO billDTO,Map<Long, String> mainBizIdNameMap) {
        SettleListExportRsp settleListExportRsp = new SettleListExportRsp();
        settleListExportRsp.setBillName(billDTO.getBillName());
        settleListExportRsp.setBillNo(billDTO.getBillNo());
        settleListExportRsp.setBalanceTypeName(billDTO.getBalanceTypeName());
        settleListExportRsp.setBillingMainBizId(billDTO.getBillingMainBizId());
        if (Objects.nonNull(billDTO.getBillingMainBizId()) && Objects.nonNull(mainBizIdNameMap)) {
            settleListExportRsp.setBillingMainBizName(mainBizIdNameMap.get(billDTO.getBillingMainBizId()));
        }
        settleListExportRsp.setCreateTime(DateUtil.format(new Date(billDTO.getCreateTime()), "yyyy-MM-dd"));
        settleListExportRsp.setPeriodStartDate(DateUtil.format(new Date(billDTO.getPeriodStart()), "yyyy-MM-dd"));
        settleListExportRsp.setPeriodEndDate(DateUtil.format(new Date(billDTO.getPeriodEnd()), "yyyy-MM-dd"));
        settleListExportRsp.setBizTypeName(billDTO.getBizTypeName());
        settleListExportRsp.setPeriodTypeName(billDTO.getPeriodTypeName());
        settleListExportRsp.setGenTypeName(billDTO.getGenTypeName());
        settleListExportRsp.setBillingMainBizTypeName(billDTO.getBillingMainBizTypeName());
        if (Objects.nonNull(billDTO.getAgentDirectSign())){
            settleListExportRsp.setAgentDirectSignName(billDTO.getAgentDirectSign()==1?"是":"否");
        }
        if (Objects.nonNull(billDTO.getAmount())){
            settleListExportRsp.setSettleAmount(String.valueOf(new BigDecimal(billDTO.getAmount()).divide(new BigDecimal(100),2, RoundingMode.HALF_UP)));
        }
        settleListExportRsp.setStatusName(billDTO.getStatusName());
        settleListExportRsp.setBillAccountTypeDesc(billDTO.getBillAccountTypeDesc());
        return settleListExportRsp;
    }
}


