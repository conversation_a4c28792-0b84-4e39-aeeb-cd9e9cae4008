package so.dian.fis.settle.biz.service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.HrManager;
import so.dian.fis.settle.client.pojo.response.UserSessionDTO;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.constant.CommonConstants;
import so.dian.fis.settle.common.enums.error.BizErrorCodeEnum;
import so.dian.fis.settle.common.util.ZHeaderUtils;
import so.dian.himalaya.boot.util.EnvUtils;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.kunlun.caifa.enums.SettleSubjectIdEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.yandang.client.pojo.enums.BelongSubTypeEnum;

@Slf4j(topic = "user")
@Component
public class CurrentUserService {

    private static final String COOKIE_NAME_EMPLOYEE_ID = "userId";
    private static final String COOKIE_NAME_EMPLOYEE_ROLE = "current_role";
    @Resource
    private HrManager hrManager;
    @Resource
    private AgentManager agentManager;

    @Autowired
    private RedissonClient redissonClient;

    private static String getCookieValue(HttpServletRequest request, String cookieName) {
        if (ArrayUtils.isEmpty(request.getCookies()) || StringUtils.isBlank(cookieName)) {
            return null;
        }
        return Stream.of(request.getCookies()).filter(cookie -> cookieName.equals(cookie.getName())).findFirst()
                .map(Cookie::getValue).orElse(null);
    }

    private static Long getEmployeeId(HttpServletRequest request) {
        String employeeStr = getCookieValue(request, COOKIE_NAME_EMPLOYEE_ID);
        if (StringUtils.isBlank(employeeStr) || !StringUtils.isNumeric(employeeStr)) {
            return null;
        }
        return Long.valueOf(employeeStr);
    }

    private static User getEmployeeRole(HttpServletRequest request) {
        User user = new User();
        Long userId = ZHeaderUtils.getUserId(request);
        if (Objects.isNull(userId)) {
            log.warn("get user id from header error.{}", request.getHeaderNames());
            return null;
        }
        String roleCode = ZHeaderUtils.getRoleCode(request);
        String nickName = ZHeaderUtils.getNickName(request);
        Long roleId = ZHeaderUtils.getRoleId(request);
        user.setUserId(userId);
        user.setCurrentRole(roleCode);
        user.setRoleId(roleId);
        user.setNickName(nickName);
        return user;
    }

    /**
     * 校验登录用户，并获取用户信息
     */
    public CurrentUserReq currentUser(HttpServletRequest request) {
        User user = getLoginUserInfo(request);
        return getSettleSubjectByLogin(user);
    }

    /**
     * 获取登录用户信息
     */
    private User getLoginUserInfo(HttpServletRequest request) {
        User user = getEmployeeRole(request);
        //测试环境
        if (EnvUtils.isLocal()) {
            String userId = request.getParameter("userId");
            String currentRole = request.getParameter("currentRole");
            if (StringUtils.isNotBlank(userId)
                    && StringUtils.isNotBlank(currentRole)) {
                user = new User();
                user.setUserId(Long.parseLong(userId));
                user.setCurrentRole(currentRole);
            }
        }
        log.info("user:{}", user);
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            throw BizException.create(BizErrorCodeEnum.USER_NOT_LOGIN);
        }
        return user;
    }

    /**
     * 获取登录用户结算方信息
     */
    public CurrentUserReq getSettleSubjectByLogin(User user) {
        if (Objects.isNull(user)) {
            throw BizException.create(BizErrorCodeEnum.USER_NOT_LOGIN);
        }
        RBucket<CurrentUserReq> bucket = redissonClient.getBucket(
                CommonConstants.USER_LOGIN_INFO + ":" + user.getUserId());
        CurrentUserReq currentUserReq = bucket.get();
        if (Objects.nonNull(currentUserReq)) {
            currentUserReq.setCurrentRole(user.getCurrentRole());
            currentUserReq.setRoleId(user.getRoleId());
            return currentUserReq;
        }
        currentUserReq = new CurrentUserReq();
        AgentEmployeeDTO employee = hrManager.getEmployeeById(user.getUserId());
        currentUserReq.setUserId(user.getUserId());
        currentUserReq.setUserName(employee.getNickNameOrName());
        currentUserReq.setUserMail(employee.getEmail());
        currentUserReq.setCurrentRole(user.getCurrentRole());
        currentUserReq.setRoleId(user.getRoleId());
        //结算方
        Integer agentId = employee.getAgentId();
        if (Objects.isNull(agentId)) {
            throw BizException.create(BizErrorCodeEnum.USER_DATA_ERROR);
        }
        Long settleSubjectId = agentId.longValue();
        currentUserReq.setSettleSubjectId(settleSubjectId);
        if (SettleSubjectIdEnum.containByXD(settleSubjectId)) {
            currentUserReq.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
        } else {
            AgentDTO agentDTO = agentManager.findById(settleSubjectId);
            if (Objects.isNull(agentDTO)) {
                log.error("远程获取代理商信息异常 | currentUserReq:{}", currentUserReq);
                throw BizException.create(BizErrorCodeEnum.USER_DATA_ERROR);
            }
            if (Objects.nonNull(agentDTO.getSubType())) {
                currentUserReq.setAgentSubType(agentDTO.getSubType());
            } else {
                currentUserReq.setAgentSubType(BelongSubTypeEnum.ENTERPRISE.getCode());
            }
            currentUserReq.setSettleSubjectType(CommonConstants.AGENT_SETTLE_TYPE_MAPPING.get(agentDTO.getType()));
        }
        bucket.set(currentUserReq, 10, TimeUnit.MINUTES);
        return currentUserReq;
    }

    public UserSessionDTO getSession(Long userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        User user = new User();
        user.setUserId(userId);
        CurrentUserReq userReq = getSettleSubjectByLogin(user);
        UserSessionDTO userSessionDTO = new UserSessionDTO();
        userSessionDTO.setUserId(userReq.getUserId());
        userSessionDTO.setUserRole(userReq.getCurrentRole());
        userSessionDTO.setEmail(userReq.getUserMail());
        userSessionDTO.setNickName(userReq.getUserName());
        userSessionDTO.setAgentSubType(userReq.getAgentSubType());
        userSessionDTO.setSettleSubjectId(userReq.getSettleSubjectId());
        userSessionDTO.setSettleSubjectType(userReq.getSettleSubjectType());
        return userSessionDTO;
    }

    @Data
    private static class User {

        private Long userId;
        private String currentRole;
        private AgentEmployeeDTO employeeDTO;
        private String nickName;
        private Long roleId;
    }
}
