package so.dian.fis.settle.biz.manager;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.himalaya.util.LocalObjectUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * MainBizInfoManager
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MainBizInfoManager {

    @Resource
    private CustomerManager customerManager;
    @Resource
    private AgentManager agentManager;

    public List<Long> listMainBizIdByTypeAndName(Integer mainBizType, String mainBizName) {
        if (Objects.isNull(mainBizType) || StringUtils.isBlank(mainBizName)) {
            return Lists.newArrayList();
        }
        List<Long> result = null;
        // 商户
        if (MainBizTypeUtils.isMerchant(mainBizType)) {
            result = customerManager.getMerchantIdsByMerchantName(mainBizName);
        }
        //渠道
        if (MainBizTypeUtils.isAgent(mainBizType)) {
            result = agentManager.getIdsByName(mainBizName, mainBizType);
        }
        if (Objects.isNull(result)) {
            return Lists.newArrayList();
        }
        return result;
    }

    public Map<Long, String> getIdNameMap(Integer mainBizType, List<Long> mainBizIdList) {
        if(Objects.isNull(mainBizType) || CollectionUtils.isEmpty(mainBizIdList)) {
            return Maps.newHashMap();
        }
        if (MainBizTypeUtils.isMerchant(mainBizType)) {
            return customerManager.getIdNameMap(mainBizIdList);
        }
        if(MainBizTypeUtils.isAgent(mainBizType)) {
            return agentManager.getIdNameMap(mainBizIdList);
        }
        return Maps.newHashMap();
    }

    public String getNameByMainBiz(Integer mainBizType, Long mainBizId) {
        if(LocalObjectUtils.anyNull(mainBizId, mainBizType)) {
            return null;
        }
        if (MainBizTypeUtils.isMerchant(mainBizType)) {
            return customerManager.getNameById(mainBizId);
        }
        if(MainBizTypeUtils.isAgent(mainBizType)) {
            return agentManager.getNameById(mainBizId);
        }
        return null;
    }
}
