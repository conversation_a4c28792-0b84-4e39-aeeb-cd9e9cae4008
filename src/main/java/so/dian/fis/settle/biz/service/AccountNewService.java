package so.dian.fis.settle.biz.service;

import cn.hutool.core.util.IdUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.biz.manager.remote.WithdrawPlatformManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.enums.AccountOperationTypeEnum;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.rick.merchant.client.merchant.req.FrozenMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.req.GetMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.req.UnfrozenMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;
import so.dian.withdraw.platform.param.MerchantDrawApplyNewParams;

import java.util.Objects;

/**
 * AccountService
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccountNewService {

    @Resource
    private RickMerchantManager rickMerchantManager;

    @Resource
    private WithdrawPlatformManager withdrawPlatformManager;

    @Resource
    private RecordOperationLogService recordOperationLogService;

    /**
     * 迁移账户的冻结
     *
     * @param accountNo
     * @return
     */
    public BizResult<Boolean> freeze(String accountNo, Integer accountType, CurrentUserReq userReq) {
        if (Objects.isNull(accountNo)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        if (Objects.isNull(accountType)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        try {
            withdrawCancelNew(accountNo, accountType);
        } catch (Exception e) {
            log.error("取消提现单失败：" + e.getMessage());
        }
        FrozenMerchantAccountReq frozenMerchantAccountReq = new FrozenMerchantAccountReq();
        frozenMerchantAccountReq.setAccountNo(accountNo);
        frozenMerchantAccountReq.setOperatorId(userReq.getUserId().toString());
        frozenMerchantAccountReq.setCause("后台冻结账户");
        frozenMerchantAccountReq.setAccountType(accountType);
        Boolean result = rickMerchantManager.frozenAccount(frozenMerchantAccountReq);
        try {
            //由于写库和读库之前存在延迟，冻结完，刷新pg库获取的数据还是之前的状态，要延迟1秒
            Thread.sleep(700);
        } catch (InterruptedException interruptedException){
            Thread.currentThread().interrupt();
            log.error("解冻延迟异常:" + interruptedException.getMessage());
        } catch (Exception e) {
            log.error("解冻延迟异常:" + e.getMessage());
        }

        // wutai异步记录操作日志
        String operationBatchNo = IdUtil.getSnowflake(1, 1).nextIdStr();
        recordOperationLogService.recordOperationLogs(userReq, accountNo, accountType, operationBatchNo,
                AccountOperationTypeEnum.ACCOUNT_FREEZE.getCode(), null, null, null);
        return BizResult.create(result);
    }

    private Boolean withdrawCancelNew(String accountNo, Integer accountType) {
        GetMerchantAccountReq getMerchantAccountReq = new GetMerchantAccountReq();
        getMerchantAccountReq.setAccountType(accountType);
        getMerchantAccountReq.setAccountNo(accountNo);
        MerchantAccountResp merchantAccountResp = rickMerchantManager.getAccount(getMerchantAccountReq);
        if (merchantAccountResp == null) {
            log.error("为查询到对应的新账户数据，accountNo:{},accountType:{}", accountNo, accountType);
            return false;
        }
        MerchantDrawApplyNewParams merchantDrawApplyNewParams = new MerchantDrawApplyNewParams();
        merchantDrawApplyNewParams.setAccountType(accountType);
        merchantDrawApplyNewParams.setMainBizId(StringUtils.isBlank(merchantAccountResp.getMerchantId()) ? null
                : Long.valueOf(merchantAccountResp.getMerchantId()));
        merchantDrawApplyNewParams.setMainBizType(
                merchantAccountResp.getMerchantType() == null ? null : merchantAccountResp.getMerchantType());
        merchantDrawApplyNewParams.setSettleSubjectId(
                StringUtils.isBlank(merchantAccountResp.getAccountDimensionFactor3()) ? null
                        : Long.valueOf(merchantAccountResp.getAccountDimensionFactor3()));
        merchantDrawApplyNewParams.setSettleSubjectType(merchantAccountResp.getAccountDimensionFactor1());
        return withdrawPlatformManager.cancelNew(merchantDrawApplyNewParams);
    }

    /**
     * 迁移账户的解冻
     *
     * @param accountNo
     * @return
     */
    public BizResult<Boolean> unFreeze(String accountNo, Integer accountType, CurrentUserReq userReq) {
        if (Objects.isNull(accountNo)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        UnfrozenMerchantAccountReq unfrozenMerchantAccountReq = new UnfrozenMerchantAccountReq();
        unfrozenMerchantAccountReq.setAccountNo(accountNo);
        unfrozenMerchantAccountReq.setOperatorId(userReq.getUserId().toString());
        unfrozenMerchantAccountReq.setCause("后台解冻账户");
        unfrozenMerchantAccountReq.setAccountType(accountType);
        boolean unFreezeResult = rickMerchantManager.unfrozenAccount(unfrozenMerchantAccountReq);
        try {
            //由于写库和读库之前存在延迟，冻结完，刷新pg库获取的数据还是之前的状态，要延迟1秒
            Thread.sleep(1000);
        } catch (InterruptedException interruptedException){
            Thread.currentThread().interrupt();
            log.error("解冻延迟异常:" + interruptedException.getMessage());
        }catch (Exception e) {
            log.error("解冻延迟异常:" + e.getMessage());
        }

        // wutai异步记录操作日志
        String operationBatchNo = IdUtil.getSnowflake(1, 1).nextIdStr();
        recordOperationLogService.recordOperationLogs(userReq, accountNo, accountType, operationBatchNo,
                AccountOperationTypeEnum.ACCOUNT_UNFREEZE.getCode(), null, null, null);
        return BizResult.create(unFreezeResult);
    }
}
