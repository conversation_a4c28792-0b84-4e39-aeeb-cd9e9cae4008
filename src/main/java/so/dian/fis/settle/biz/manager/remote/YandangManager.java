package so.dian.fis.settle.biz.manager.remote;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.fis.settle.remote.YandangClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

import jakarta.annotation.Resource;

/**
 * LvyManager
 *
 * <AUTHOR>
 */
@Slf4j(topic = "remote")
@Service
public class YandangManager {

    @Resource
    private YandangClient yandangClient;

    public PayBillInfoRsp getPayBillInfo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }
        BizResult<PayBillInfoRsp> addResult = yandangClient.getPayBillInfo(billNo);
        log.info("YandangClient getPayBillInfo->billNo:{},resp:{}", billNo, addResult);
        if (addResult.isSuccess()) {
            return addResult.getData();
        }
        return null;
    }

}
