package so.dian.fis.settle.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.handler.AccountHandler;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.enums.error.BizErrorCodeEnum;
import so.dian.fis.settle.common.pojo.converter.CommonConverter;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.request.PageAccountReq;
import so.dian.fis.settle.controller.account.response.AccountListRsp;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.lvy.pojo.dto.AccountDTO;
import so.dian.lvy.pojo.dto.AccountStatisticsDTO;
import so.dian.lvy.pojo.dto.QueryAccountListDTO;
import so.dian.lvy.pojo.query.AccountListQuery;
import so.dian.rick.merchant.client.merchant.req.BaseMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.req.CheckAccountIsMoveReq;
import so.dian.rick.merchant.client.merchant.req.GetMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * AccountService
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccountService {

    @Resource
    private AccountHandler accountHandler;
    @Resource
    private LvyManager lvyManager;
    @Resource
    private RickMerchantManager rickMerchantManager;

    /**
     * 没有迁移账户的分页查询
     *
     * @param pageAccountReq
     * @param userReq
     * @return
     */
    public BizResult<PageData<AccountListRsp>> pageAccount(PageAccountReq pageAccountReq, CurrentUserReq userReq) {
        AccountListQuery lvyListQuery = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);

        // 为null时，无需去lvy查询
        if (Objects.isNull(lvyListQuery)) {
            return BizResult.create(PageData.create(Lists.newArrayList(), 0L));
        }
        PageResult<QueryAccountListDTO> queryResult = lvyManager.pageAccount(lvyListQuery);
        CheckAccountIsMoveReq checkAccountIsMoveReq = new CheckAccountIsMoveReq();
        List<QueryAccountListDTO> data = queryResult.getData();
        if (CollectionUtils.isEmpty(data)) {
            return BizResult.create(PageData.create(Collections.emptyList()));
        }
        List<BaseMerchantAccountReq> reqs = Lists.newArrayList();
        for (QueryAccountListDTO datum : data) {
            BaseMerchantAccountReq baseMerchantAccountReq = new BaseMerchantAccountReq();
            baseMerchantAccountReq.setAccountType(AccountTypeEnum.SHARE_ACCOUNT.getKey());
            baseMerchantAccountReq.setMerchantId(datum.getMainBizId().toString());
            baseMerchantAccountReq.setMerchantType(datum.getMainBizType());
            baseMerchantAccountReq.setAccountDimensionFactor1(lvyListQuery.getSettleSubjectType());
            baseMerchantAccountReq.setAccountDimensionFactor3(lvyListQuery.getSettleSubjectId().toString());
            reqs.add(baseMerchantAccountReq);
        }
        checkAccountIsMoveReq.setBaseMerchantAccountReqs(reqs);
        List<MerchantAccountResp> merchantAccountResps = rickMerchantManager.checkAccountMove(checkAccountIsMoveReq);
        List<String> merchantIds = merchantAccountResps.stream().map(MerchantAccountResp::getMerchantId)
                .collect(Collectors.toList());
        List<AccountListRsp> accountListRsps = accountHandler.buildListRspFromLvyAccountList(
                pageAccountReq.getMainBizType(), queryResult.getData(),
                merchantIds);
        accountListRsps.forEach(accountListRsp -> accountListRsp.setSettleSubjectId(lvyListQuery.getSettleSubjectId()));
        return CommonConverter.convertPageResultToBizResult(queryResult,
                accountListRsps);
    }

    /**
     * 没有迁移账户的统计
     *
     * @param pageAccountReq
     * @param userReq
     * @return
     */
    public BizResult<AccountStatisticsDTO> queryAccountAmount(PageAccountReq pageAccountReq, CurrentUserReq userReq) {
        AccountListQuery lvyListQuery = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);

        // 为null时，无需去lvy查询
        if (Objects.isNull(lvyListQuery)) {
            return BizResult.create(null);
        }

        AccountStatisticsDTO statisticsDTO = lvyManager.queryAccountAmount(lvyListQuery);
        return BizResult.create(statisticsDTO);
    }

    /**
     * 没有迁移账户的冻结
     *
     * @param accountId
     * @param userReq
     * @return
     */
    public BizResult<Boolean> freeze(Long accountId, CurrentUserReq userReq) {
        if (Objects.isNull(accountId)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        AccountDTO accountDto = lvyManager.getAccountById(accountId);
        GetMerchantAccountReq merchantAccount = new GetMerchantAccountReq();
        merchantAccount.setMerchantId(accountDto.getBillingMainBizId().toString());
        merchantAccount.setMerchantType(accountDto.getBillingMainBizType());
        merchantAccount.setAccountDimensionFactor1(accountDto.getSettleSubjectType());
        merchantAccount.setAccountDimensionFactor3(accountDto.getSettleSubjectId().toString());
        merchantAccount.setAccountType(AccountTypeEnum.SHARE_ACCOUNT.getKey());
        rickMerchantManager.getAccount(merchantAccount);
        log.error("账户已经迁移到新账户，getFlowSwitchReq：{},flowSwitch:{}", JSONObject.toJSONString(merchantAccount),
                JSONObject.toJSONString(accountDto));
        throw BizException.create(BizErrorCodeEnum.ACCOUNT_NEW);
    }

    /**
     * 没有迁移账户的解冻
     *
     * @param accountId
     * @param userReq
     * @return
     */
    public BizResult<Boolean> unFreeze(Long accountId, CurrentUserReq userReq) {
        if (Objects.isNull(accountId)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        AccountDTO accountDto = lvyManager.getAccountById(accountId);
        if (accountDto == null) {
            log.error("账户不存在，accountId：" + accountId);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        GetMerchantAccountReq merchantAccount = new GetMerchantAccountReq();
        merchantAccount.setMerchantId(accountDto.getBillingMainBizId().toString());
        merchantAccount.setMerchantType(accountDto.getBillingMainBizType());
        merchantAccount.setAccountDimensionFactor1(accountDto.getSettleSubjectType());
        merchantAccount.setAccountDimensionFactor3(accountDto.getSettleSubjectId().toString());
        merchantAccount.setAccountType(AccountTypeEnum.SHARE_ACCOUNT.getKey());
        MerchantAccountResp account = rickMerchantManager.getAccount(merchantAccount);
        log.error("账户已经迁移到新账户，getFlowSwitchReq：{},flowSwitch:{}", JSONObject.toJSONString(merchantAccount),
                JSONObject.toJSONString(account));
        throw BizException.create(BizErrorCodeEnum.ACCOUNT_NEW);
    }
}
