package so.dian.fis.settle.biz.manager;

import io.github.notoday.dingtalk.robot.DingRobotHelper;
import io.github.notoday.dingtalk.robot.message.MarkdownMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import so.dian.fis.settle.common.pojo.properties.AppProperties;
import so.dian.fis.settle.remote.DingTalkClient;

@Slf4j
@Service
public class DingTalkManager {

    @Autowired
    private AppProperties appProperties;
    @Autowired
    private DingTalkClient dingTalkClient;

    /**
     * 账单告警消息
     */
    @Async("dingTalkExecutor")
    public void sendWarnMsg(String title, String text) {
        DingRobotHelper dingRobotHelper =
                dingTalkClient.getDingRobotHelper(appProperties.getDTalkWarnToken(), appProperties.getDTalkSecret());
        MarkdownMessage markdownMessage = MarkdownMessage.builder().title(title).text(text).build();
        log.info("DingTalkSend");
        dingRobotHelper.sendMessage(markdownMessage);
    }
}
