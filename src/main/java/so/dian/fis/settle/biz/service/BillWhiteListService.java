package so.dian.fis.settle.biz.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import so.dian.fis.settle.common.constant.CommonConstants;
import so.dian.himalaya.util.LocalStringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * BillWhiteListService
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BillWhiteListService {
    @Resource
    private RedissonClient redissonClient;

    public void setWhiteList(String value) {
        RBucket<String> rBucket = redissonClient.getBucket(CommonConstants.BATCH_ADD_BILL_WHITE_LIST_CACHE_KEY);
        rBucket.set(value);
    }

    public String getWhiteList() {
        RBucket<String> rBucket = redissonClient.getBucket(CommonConstants.BATCH_ADD_BILL_WHITE_LIST_CACHE_KEY);
        return rBucket.get();
    }

    public boolean checkHasPermission(String nickName) {
        if(StringUtils.isBlank(nickName)) {
            return false;
        }
        String whiteListStr = getWhiteList();
        if(StringUtils.isBlank(whiteListStr)) {
            return false;
        }
        List<String> whiteList = LocalStringUtils.spiltStringToList(whiteListStr, ",");
        return whiteList.contains(nickName);
    }

}
