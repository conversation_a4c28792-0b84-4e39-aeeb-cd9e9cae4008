package so.dian.fis.settle.biz.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.enums.error.BizErrorCodeEnum;
import so.dian.fis.settle.common.pojo.converter.AccountBalanceConverter;
import so.dian.fis.settle.common.pojo.converter.CommonConverter;
import so.dian.fis.settle.common.util.biz.SettleSubjectValidateUtils;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceReq;
import so.dian.fis.settle.controller.account.response.AccountBalancePcRsp;
import so.dian.fis.settle.controller.account.response.BalanceTypeRsp;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.exception.BizException;
import so.dian.lvy.pojo.dto.AccountBalanceDTO;
import so.dian.lvy.pojo.dto.AccountDTO;
import so.dian.lvy.pojo.enums.BalanceBizDocTypeEnum;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * AccountBalanceService
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccountBalanceService {

    @Resource
    private LvyManager lvyManager;

    public BizResult<PageData<AccountBalancePcRsp>> pageAccountBalanceReq(CurrentUserReq userReq, PageAccountBalanceReq pageAccountBalanceReq) {
        // 查询账户信息，比对是否有权限查看(根据结算方来)
        AccountDTO accountDTO = lvyManager.getAccountById(pageAccountBalanceReq.getAccountId());
        if(Objects.isNull(accountDTO)) {
            return BizResult.create(PageData.create(Lists.newArrayList(), 0L));
        }
        boolean settleSubjectValid = SettleSubjectValidateUtils.validatePermission(userReq.getSettleSubjectType(), userReq.getSettleSubjectId(), accountDTO.getSettleSubjectType(), accountDTO.getSettleSubjectId());
        if(!settleSubjectValid) {
            return BizResult.create(PageData.create(Lists.newArrayList(), 0L));
        }
        //查询列表结果返回
        PageResult<AccountBalanceDTO> pageResult = lvyManager.pageBalanceList(pageAccountBalanceReq);
        return CommonConverter.convertPageResultToBizResult(pageResult, AccountBalanceConverter.buildPcRsp(pageResult.getData()));
    }

    public List<BalanceTypeRsp> accountBalanceType(CurrentUserReq userReq){
        if(userReq == null){
            throw BizException.create(BizErrorCodeEnum.USER_NOT_LOGIN);
        }
        return Arrays.stream(BalanceBizDocTypeEnum.values()).map(o -> {
            BalanceTypeRsp balanceTypeRsp = new BalanceTypeRsp();
            balanceTypeRsp.setKey(o.getCode());
            balanceTypeRsp.setValue(o.getDesc());
            return balanceTypeRsp;
        }).collect(Collectors.toList());
    }

}
