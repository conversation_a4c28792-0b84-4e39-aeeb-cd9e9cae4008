package so.dian.fis.settle.biz.manager;

import com.alibaba.fastjson.JSON;
import java.io.File;
import java.io.IOException;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.fis.settle.remote.MailClient;
import so.dian.mail.dto.MailInfoParamDto;
import so.dian.mail.model.ResultEntity;

/**
 * <EMAIL>
 * 2021/6/30
 */
@Slf4j
@Service
public class MailManager {

    @Resource
    private MailClient mailClient;

    public boolean sendEmail(String to, String title, String html, File file) throws IOException {
        MailInfoParamDto mailInfoParamDto = new MailInfoParamDto();
        mailInfoParamDto.setTo(to);
        mailInfoParamDto.setTitle(title);
        mailInfoParamDto.setHtml(html);
        mailInfoParamDto.setFileStreams(file);
        ResultEntity resultEntity = mailClient.sendMail(mailInfoParamDto);
        log.info("------ mail send result {}", JSON.toJSONString(resultEntity));
        if (Objects.isNull(resultEntity)) {
            return false;
        }
        return resultEntity.isSuccess();
    }
}
