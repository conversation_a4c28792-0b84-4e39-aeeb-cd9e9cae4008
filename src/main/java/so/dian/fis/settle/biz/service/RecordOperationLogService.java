package so.dian.fis.settle.biz.service;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.fis.settle.facade.WutaiFacade;
import so.dian.rick.merchant.client.entity.BatchUnfrozenMerchantAmountItem;
import so.dian.rick.merchant.client.merchant.req.PageQueryAccountMerchantReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;
import so.dian.wutai.client.pojo.dto.AccountOperationLogDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: ahuang
 * @CreateTime: 2024-11-28 16:57
 * @Description:
 */
@Component
@Slf4j
public class RecordOperationLogService {

    @Resource
    private WutaiFacade wutaiFacade;

    @Autowired
    @Qualifier(value = "exportExecutor")
    private ThreadPoolExecutor exportExecutor;

    @Resource
    private RickMerchantManager rickMerchantManager;

    @Resource
    private AgentManager agentManager;

    @Resource
    private CustomerManager customerManager;

    public void recordOperationLogs(CurrentUserReq userReq, String accountNo, Integer accountType,
                                     String operationBatchNo, Integer operationType, Long totalAmount, String oaApprovalNo,
                                     List<BatchUnfrozenMerchantAmountItem> itemList) {
        exportExecutor.execute(() -> {
            try {
                log.info("Operation logs recorded start");
                // 查询账户信息
                PageQueryAccountMerchantReq queryReq = new PageQueryAccountMerchantReq();
                queryReq.setAccountNo(accountNo);
                queryReq.setAccountType(accountType);
                queryReq.setPageNo(1);
                queryReq.setPageSize(1);

                PageResult<MerchantAccountResp> result = rickMerchantManager.pageQueryAccount(queryReq);
                // 检查返回结果是否为空或没有数据
                if (result.getTotalCount() <= 0) {
                    log.error("No merchant account found for accountNo: {}, accountType: {}", accountNo, accountType);
                    return; // 或者直接结束方法
                }
                MerchantAccountResp account = result.getData().getFirst();

                // 构建日志
                List<AccountOperationLogDTO> logs = new ArrayList<>();
                if (itemList == null || itemList.isEmpty()) {
                    // 冻结金额日志以及解冻冻结账户，只有一条
                    AccountOperationLogDTO log = buildOperationLog(userReq, account, operationBatchNo, operationBatchNo,
                            operationType, totalAmount, oaApprovalNo);
                    logs.add(log);
                } else {
                    // 解冻金额日志，每个 item 一条
                    for (BatchUnfrozenMerchantAmountItem item : itemList) {
                        AccountOperationLogDTO log = buildOperationLog(userReq, account, item.getUnfrozenOrderNo(),
                                operationBatchNo, operationType, item.getUnfrozenAmount(), oaApprovalNo);
                        logs.add(log);
                    }
                }

                // 批量记录日志
                wutaiFacade.accountOperationLog(logs);
                log.info("Operation logs recorded successfully: {}", logs);
            } catch (Exception e) {
                log.error("Failed to record operation logs for batchNo: {}", operationBatchNo, e);
            }
        });
    }

    private AccountOperationLogDTO buildOperationLog(CurrentUserReq userReq, MerchantAccountResp account,
                                                     String operationNo, String operationBatchNo, Integer operationType,
                                                     Long operationAmount, String oaApprovalNo) {
        AccountOperationLogDTO log = new AccountOperationLogDTO();
        log.setOperationNo(operationNo);
        log.setOperationBatchNo(operationBatchNo);
        log.setAccountId(account.getId());
        log.setAccountNo(account.getAccountNo());
        log.setAccountType(account.getAccountType());
        log.setStatus(account.getStatus());
        log.setOperationType(operationType);
        log.setOperationAmount(operationAmount);
        log.setOaApprovalNo(oaApprovalNo);
        log.setBelongCompanyId(StrUtil.isBlank(account.getAccountDimensionFactor3()) ? null : NumberUtils.createLong(account.getAccountDimensionFactor3()));
        log.setMainBizId(account.getMerchantId());
        log.setMainBizName(account.getMerchantName());
        log.setMainBizType(account.getMerchantType());
        if (StringUtils.isBlank(log.getMainBizName())) {
            if (MainBizTypeUtils.isMerchant(log.getMainBizType())) {
                log.setMainBizName(customerManager.getNameById(Long.valueOf(log.getMainBizId())));
            } else if (MainBizTypeUtils.isAgent(log.getMainBizType())) {
                log.setMainBizName(agentManager.getNameById(Long.valueOf(log.getMainBizId())));
            }
        }
        log.setOperatorId(userReq.getUserId());
        log.setOperatorRole(userReq.getCurrentRole());
        return log;
    }
}
