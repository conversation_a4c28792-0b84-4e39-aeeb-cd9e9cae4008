package so.dian.fis.settle.biz.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import so.dian.fis.settle.biz.service.CurrentUserService;
import so.dian.fis.settle.client.api.UserSessionApi;
import so.dian.fis.settle.client.pojo.response.UserSessionDTO;
import so.dian.himalaya.common.entity.BizResult;

/**
 * UserSessionController
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/20 15:31
 */
@RestController
public class UserSessionController implements UserSessionApi {

    @Autowired
    private CurrentUserService currentUserService;

    @Override
    public BizResult<UserSessionDTO> get(Long userId) {
        UserSessionDTO session = currentUserService.getSession(userId);
        return BizResult.create(session);
    }
}
