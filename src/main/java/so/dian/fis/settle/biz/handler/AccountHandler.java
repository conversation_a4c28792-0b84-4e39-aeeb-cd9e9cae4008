package so.dian.fis.settle.biz.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.constant.CommonConstants;
import so.dian.fis.settle.common.pojo.converter.AccountConverter;
import so.dian.fis.settle.common.util.MoneyUtils;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.request.PageAccountNewReq;
import so.dian.fis.settle.controller.account.request.PageAccountReq;
import so.dian.fis.settle.controller.account.response.AccountListNewRsp;
import so.dian.fis.settle.controller.account.response.AccountListRsp;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.kunlun.caifa.enums.SettleSubjectIdEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.dto.QueryAccountListDTO;
import so.dian.lvy.pojo.query.AccountListQuery;
import so.dian.rick.merchant.client.merchant.req.PageQueryAccountMerchantReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * AccountHandler
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccountHandler {

    @Resource
    private AgentManager agentManager;
    @Resource
    private CustomerManager customerManager;

    public List<AccountListRsp> buildListRspFromLvyAccountList(Integer mainBizType,
            List<QueryAccountListDTO> lvyAccountList, List<String> merchantIds) {
        if (CollectionUtils.isEmpty(lvyAccountList)) {
            return Lists.newArrayList();
        }
        List<Long> mainBizIdList = LocalListUtils.transferList(lvyAccountList, QueryAccountListDTO::getMainBizId);
        Map<Long, String> idNameMap = null;
        if (MainBizTypeUtils.isMerchant(mainBizType)) {
            idNameMap = customerManager.getIdNameMap(mainBizIdList);
        }

        if (MainBizTypeUtils.isAgent(mainBizType)) {
            idNameMap = agentManager.getIdNameMap(mainBizIdList);
        }
        if (Objects.isNull(idNameMap)) {
            idNameMap = Maps.newHashMap();
        }
        List<AccountListRsp> accountListRsps = AccountConverter.convertQueryAccountListDTO2ListRsp(lvyAccountList,
                idNameMap);
        for (AccountListRsp accountListRsp : accountListRsps) {
            accountListRsp.setHasMove(merchantIds.contains(accountListRsp.getMainBizId()));
        }
        return accountListRsps;
    }

    public List<AccountListNewRsp> buildListRspFromRickAccountList(Integer mainBizType,
            List<MerchantAccountResp> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<AccountListNewRsp> accountListNewRsps = AccountConverter.convertQueryAccountListNewListRsp(list);
        return accountListNewRsps.stream().map(o -> {
            if (StringUtils.isBlank(o.getMainBizName())) {
                if (MainBizTypeUtils.isMerchant(o.getMainBizType())) {
                    o.setMainBizName(customerManager.getNameById(Long.valueOf(o.getMainBizId())));
                } else if (MainBizTypeUtils.isAgent(o.getMainBizType())) {
                    o.setMainBizName(agentManager.getNameById(Long.valueOf(o.getMainBizId())));
                }
            }
            return o;
        }).collect(Collectors.toList());
    }

    public AccountListQuery buildLvyAccountListQuery(PageAccountReq pageAccountReq, CurrentUserReq userReq) {
        if (Objects.isNull(pageAccountReq)) {
            pageAccountReq = new PageAccountReq();
        }

        AccountListQuery lvyListQuery = new AccountListQuery();
        //结算方类型统一取登陆用户所属类型，结算方id：如果结算方类型为我司，则取查询条件，否则取登陆用户所属的结算方id
        lvyListQuery.setSettleSubjectType(userReq.getSettleSubjectType());
        if (Objects.equals(SettleSubjectTypeEnum.XIAODIAN.getCode(), userReq.getSettleSubjectType())) {
            lvyListQuery.setSettleSubjectId(pageAccountReq.getSettleSubjectId());

            // 如果当前登录的是小电员工，且查询的所属公司是合资公司，需要将结算主体类型设置成合资公司的类型
            if (!SettleSubjectIdEnum.containByXD(pageAccountReq.getSettleSubjectId())) {
                AgentDTO agentDTO = agentManager.findById(pageAccountReq.getSettleSubjectId());
                lvyListQuery.setSettleSubjectType(CommonConstants.AGENT_SETTLE_TYPE_MAPPING.get(agentDTO.getType()));
            }
        } else {
            lvyListQuery.setSettleSubjectId(userReq.getSettleSubjectId());
        }

        if (Objects.nonNull(pageAccountReq.getCreateTimeStart())) {
            lvyListQuery.setCreateStartTime(LocalDateUtils.beginOfDay(pageAccountReq.getCreateTimeStart()));
        }
        if (Objects.nonNull(pageAccountReq.getCreateTimeEnd())) {
            lvyListQuery.setCreateEndTime(LocalDateUtils.endOfDayWithoutMillisecond(pageAccountReq.getCreateTimeEnd()));
        }

        lvyListQuery.setAccountAmountMin(MoneyUtils.yuanToFen(pageAccountReq.getAccountAmountMin()));
        lvyListQuery.setAccountAmountMax(MoneyUtils.yuanToFen(pageAccountReq.getAccountAmountMax()));
        lvyListQuery.setAvailableAmountMin(MoneyUtils.yuanToFen(pageAccountReq.getAvailableAmountMin()));
        lvyListQuery.setAvailableAmountMax(MoneyUtils.yuanToFen(pageAccountReq.getAvailableAmountMax()));
        lvyListQuery.setFreezeAmountMin(MoneyUtils.yuanToFen(pageAccountReq.getFreezeAmountMin()));
        lvyListQuery.setFreezeAmountMax(MoneyUtils.yuanToFen(pageAccountReq.getFreezeAmountMax()));
        lvyListQuery.setAccountId(pageAccountReq.getAccountId());
        // 类型必选
        if (Objects.isNull(pageAccountReq.getMainBizType())) {
            log.info("buildLvyAccountListQuery parameter error, param:{}", pageAccountReq);
            return null;
        }
        lvyListQuery.setMainBizType(pageAccountReq.getMainBizType());
        lvyListQuery.setFreeze(pageAccountReq.getFreeze());
        lvyListQuery.setPageNo(pageAccountReq.getPageNo());
        lvyListQuery.setPageSize(pageAccountReq.getPageSize());

        // 商户类型
        if (MainBizTypeUtils.isMerchant(pageAccountReq.getMainBizType())) {
            if (StringUtils.isNotBlank(pageAccountReq.getMainBizName())) {
                List<Long> merchantIdList = customerManager.getMerchantIdsByMerchantName(
                        pageAccountReq.getMainBizName());
                if (CollectionUtils.isEmpty(merchantIdList)) {
                    log.info("buildLvyAccountListQuery parameter error, param:{}", pageAccountReq);
                    return null;
                }
                if (Objects.nonNull(pageAccountReq.getMainBizId())) {
                    if (!merchantIdList.contains(pageAccountReq.getMainBizId())) {
                        log.info("buildLvyAccountListQuery parameter error, param:{}", pageAccountReq);
                        return null;
                    }
                    lvyListQuery.setMainBizIdList(Lists.newArrayList(pageAccountReq.getMainBizId()));
                } else {
                    lvyListQuery.setMainBizIdList(merchantIdList);
                }
            } else if (Objects.nonNull(pageAccountReq.getMainBizId())) {
                lvyListQuery.setMainBizIdList(Lists.newArrayList(pageAccountReq.getMainBizId()));
            }

        }
        // 渠道类型
        else if (MainBizTypeUtils.isAgent(pageAccountReq.getMainBizType())) {
            if (StringUtils.isNotBlank(pageAccountReq.getMainBizName())) {
                List<Long> agentIdList = agentManager
                        .getIdsByName(pageAccountReq.getMainBizName(), pageAccountReq.getMainBizType());
                if (CollectionUtils.isEmpty(agentIdList)) {
                    log.info("buildLvyAccountListQuery parameter error, param:{}", pageAccountReq);
                    return null;
                }
                if (Objects.nonNull(pageAccountReq.getMainBizId())) {
                    if (!agentIdList.contains(pageAccountReq.getMainBizId())) {
                        log.info("buildLvyAccountListQuery parameter error, param:{}", pageAccountReq);
                        return null;
                    }
                    lvyListQuery.setMainBizIdList(Lists.newArrayList(pageAccountReq.getMainBizId()));
                } else {
                    lvyListQuery.setMainBizIdList(agentIdList);
                }
            } else if (Objects.nonNull(pageAccountReq.getMainBizId())) {
                lvyListQuery.setMainBizIdList(Lists.newArrayList(pageAccountReq.getMainBizId()));
            }
        }
        // iot商户
        else if (MainBizTypeUtils.isIotMerchant(pageAccountReq.getMainBizType())) {
            if (Objects.nonNull(pageAccountReq.getMainBizId())) {
                lvyListQuery.setMainBizIdList(Lists.newArrayList(pageAccountReq.getMainBizId()));
            }
        }
        // 未选择类型
        else if (Objects.nonNull(pageAccountReq.getMainBizId())) {
            lvyListQuery.setMainBizIdList(Lists.newArrayList(pageAccountReq.getMainBizId()));
        }

        return lvyListQuery;
    }

//    public PageQueryAccountMerchantReq buildRickMerchantAccountListQuery(PageAccountNewReq pageAccountReq, CurrentUserReq userReq) {
//        if (Objects.isNull(pageAccountReq)) {
//            pageAccountReq = new PageAccountNewReq();
//        }
//        // 类型必选
//        if (Objects.isNull(pageAccountReq.getAccountType())) {
//            log.info("buildRickMerchantAccountListQuery parameter error, param:{}", pageAccountReq);
//            return null;
//        }
//        //subjectName,status,merchantType,merchantId,accountType,accountNo
//        PageQueryAccountMerchantReq pageQueryAccountMerchantReq = new PageQueryAccountMerchantReq();
//        //结算方类型统一取登陆用户所属类型，结算方id：如果结算方类型为我司，则取查询条件，否则取登陆用户所属的结算方id
//        pageQueryAccountMerchantReq.setMerchantType(pageAccountReq.getMainBizType());
//        // 商户类型，数据不多可以这样查询，后面如果数据量上来需要换方式
//        if (MainBizTypeUtils.isMerchant(pageAccountReq.getMainBizType())) {
//            if (StringUtils.isNotBlank(pageAccountReq.getMainBizName())) {
//                List<Long> merchantIdList = customerManager.getMerchantIdsByMerchantName(pageAccountReq.getMainBizName());
//                if (CollectionUtils.isEmpty(merchantIdList)) {
//                    log.info("buildRickMerchantAccountListQuery parameter error, param:{}", pageAccountReq);
//                    return null;
//                }
//                if (Objects.nonNull(pageAccountReq.getMainBizId())) {
//                    if (!merchantIdList.contains(pageAccountReq.getMainBizId())) {
//                        log.info("buildRickMerchantAccountListQuery parameter error, param:{}", pageAccountReq);
//                        return null;
//                    }
//                    pageQueryAccountMerchantReq.setMerchantIds(Lists.newArrayList(pageAccountReq.getMainBizId().toString()));
//                } else {
//                    pageQueryAccountMerchantReq.setMerchantIds(merchantIdList.stream().map(o -> o.toString()).collect(Collectors.toList()));
//                }
//            } else if (Objects.nonNull(pageAccountReq.getMainBizId())) {
//                pageQueryAccountMerchantReq.setMerchantIds(Lists.newArrayList(pageAccountReq.getMainBizId().toString()));
//            }
//            // 渠道类型
//        } else if (MainBizTypeUtils.isAgent(pageAccountReq.getMainBizType())) {
//            if (StringUtils.isNotBlank(pageAccountReq.getMainBizName())) {
//                List<Long> agentIdList = agentManager
//                        .getIdsByName(pageAccountReq.getMainBizName(), pageAccountReq.getMainBizType());
//                if (CollectionUtils.isEmpty(agentIdList)) {
//                    log.info("buildRickMerchantAccountListQuery parameter error, param:{}", pageAccountReq);
//                    return null;
//                }
//                if (Objects.nonNull(pageAccountReq.getMainBizId())) {
//                    if (!agentIdList.contains(pageAccountReq.getMainBizId())) {
//                        log.info("buildRickMerchantAccountListQuery parameter error, param:{}", pageAccountReq);
//                        return null;
//                    }
//                    pageQueryAccountMerchantReq.setMerchantIds(Lists.newArrayList(pageAccountReq.getMainBizId().toString()));
//                } else {
//                    pageQueryAccountMerchantReq.setMerchantIds(agentIdList.stream().map(o -> o.toString()).collect(Collectors.toList()));
//                }
//            } else if (Objects.nonNull(pageAccountReq.getMainBizId())) {
//                pageQueryAccountMerchantReq.setMerchantIds(Lists.newArrayList(pageAccountReq.getMainBizId().toString()));
//            }
//        } else if (MainBizTypeUtils.isIotMerchant(pageAccountReq.getMainBizType())) {
//            // iot商户
//            if (Objects.nonNull(pageAccountReq.getMainBizId())) {
//                pageQueryAccountMerchantReq.setMerchantIds(Lists.newArrayList(pageAccountReq.getMainBizId().toString()));
//            }
//        } else if (Objects.nonNull(pageAccountReq.getMainBizId())) {
//            // 未选择类型
//            pageQueryAccountMerchantReq.setMerchantIds(Lists.newArrayList(pageAccountReq.getMainBizId().toString()));
//        }
//        if (Objects.nonNull(pageAccountReq.getCreateTimeStart())) {
//            pageQueryAccountMerchantReq.setStartCreateTime(LocalDateUtils.getTime(pageAccountReq.getCreateTimeStart()));
//        }
//        if (Objects.nonNull(pageAccountReq.getCreateTimeEnd())) {
//            pageQueryAccountMerchantReq.setEndCreateTime(LocalDateUtils.getTime(pageAccountReq.getCreateTimeEnd()));
//        }
//
//        pageQueryAccountMerchantReq.setAccountNo(StringUtils.isBlank(pageAccountReq.getAccountNo()) ? null : pageAccountReq.getAccountNo());
//        pageQueryAccountMerchantReq.setAccountType(pageAccountReq.getAccountType());
//
//        pageQueryAccountMerchantReq.setPageNo(pageAccountReq.getPageNo());
//        pageQueryAccountMerchantReq.setPageSize(pageAccountReq.getPageSize());
//        pageQueryAccountMerchantReq.setSubjectName(StringUtils.isBlank(pageAccountReq.getMainBizName()) ? null : pageAccountReq.getMainBizName());
//        pageQueryAccountMerchantReq.setStatus(pageAccountReq.getStatus());
//        if (AccountTypeEnum.SHARE_ACCOUNT.getKey().equals(pageAccountReq.getAccountType())) {
//            pageQueryAccountMerchantReq.setAccountDimensionFactor1(userReq.getSettleSubjectType());
//            if (Objects.equals(SettleSubjectTypeEnum.XIAODIAN.getCode(), userReq.getSettleSubjectType())) {
//                pageQueryAccountMerchantReq.setAccountDimensionFactor3(pageAccountReq.getSettleSubjectId() == null ? null : pageAccountReq.getSettleSubjectId().toString());
//            } else {
//                pageQueryAccountMerchantReq.setAccountDimensionFactor3(userReq.getSettleSubjectId() == null ? null : userReq.getSettleSubjectId().toString());
//            }
//        }
//        return pageQueryAccountMerchantReq;
//    }
}
