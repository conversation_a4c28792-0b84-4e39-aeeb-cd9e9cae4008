package so.dian.fis.settle.biz.manager.remote;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.common.enums.error.BizErrorCodeEnum;
import so.dian.fis.settle.remote.rickMerchant.RickConfigClient;
import so.dian.fis.settle.remote.rickMerchant.RickMerchantClient;
import so.dian.himalaya.common.exception.BizException;
import so.dian.mofa3.lang.domain.Result;
import so.dian.rick.merchant.client.config.req.GetBizOpTypeReq;
import so.dian.rick.merchant.client.config.resp.GetBizOpTypeResp;
import so.dian.rick.merchant.client.merchant.req.BatchUnfrozenMerchantAmountReq;
import so.dian.rick.merchant.client.merchant.req.CheckAccountIsMoveReq;
import so.dian.rick.merchant.client.merchant.req.FrozenMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.req.FrozenMerchantAmountReq;
import so.dian.rick.merchant.client.merchant.req.GetMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgExportReq;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgReq;
import so.dian.rick.merchant.client.merchant.req.PageQueryAccountMerchantReq;
import so.dian.rick.merchant.client.merchant.req.PageQueryBalanceMerchant4CRMReq;
import so.dian.rick.merchant.client.merchant.req.QueryFrozenBillNotAllReverseReq;
import so.dian.rick.merchant.client.merchant.req.UnfrozenMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.resp.AccountStatisticsResp;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;
import so.dian.rick.merchant.client.merchant.resp.MerchantBalanceResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * RickMerchantManager
 *
 * @author: shike
 */
@Slf4j(topic = "remote")
@Service
public class RickMerchantManager {

    @Resource
    private RickMerchantClient rickMerchantClient;

    @Resource
    private RickConfigClient rickConfigClient;

    public AccountStatisticsResp statisticsAccount(ListAccountMerchantPgReq merchantPgReq) {

        log.info("statisticsAccount . 获取账户流水查询 --> merchantPgReq:{}", JSONObject.toJSONString(merchantPgReq));
        Result<AccountStatisticsResp> result = rickMerchantClient.statisticsAccountSync(merchantPgReq);
        if (Objects.isNull(result) || !result.isSuccess()) {
            log.error("statisticsAccount . 获取账户流水查询结束 --> result:{}", JSONObject.toJSONString(result));
            return null;
        }

        return result.getData();
    }

    public PageResult<MerchantBalanceResp> pageQueryBalance4CRM(PageQueryBalanceMerchant4CRMReq pageQueryBalanceMerchantReq) {
        log.info("pageQueryBalance . 获取账户流水查询 --> pageQueryBalanceMerchantReq:{}",
                JSONObject.toJSONString(pageQueryBalanceMerchantReq == null ? "" : pageQueryBalanceMerchantReq));
        Result<PageInfo<MerchantBalanceResp>> result = rickMerchantClient.pageQueryBalance4CRM(pageQueryBalanceMerchantReq);
        if (result == null || !result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(
                result.getData().getList())) {
            log.error("pageQueryBalance . 获取账户流水查询结束 --> result:{}",
                    JSONObject.toJSONString(result == null ? "" : result));
            return PageResult.create(Collections.emptyList(), 0, pageQueryBalanceMerchantReq.getPageNo(),
                    pageQueryBalanceMerchantReq.getPageSize());
        }
        return PageResult.create(result.getData().getList(),
                Integer.parseInt(String.valueOf(result.getData().getTotal())), pageQueryBalanceMerchantReq.getPageNo(),
                pageQueryBalanceMerchantReq.getPageSize());
    }


    public PageResult<MerchantAccountResp> pageQueryAccount(PageQueryAccountMerchantReq pageQueryAccountMerchantReq) {
        log.info("pageQueryAccount . 获取分页账户查询 --> pageQueryAccountMerchantReq:{}",
                JSONObject.toJSONString(pageQueryAccountMerchantReq == null ? "" : pageQueryAccountMerchantReq));
        Result<PageInfo<MerchantAccountResp>> result = rickMerchantClient.pageQueryAccount(pageQueryAccountMerchantReq);
        if (result == null || !result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(
                result.getData().getList())) {
            log.error("pageQueryAccount . 获取分页账户查询结束 --> result:{}",
                    JSONObject.toJSONString(result == null ? "" : result));
            return PageResult.create(Collections.emptyList(), 0, pageQueryAccountMerchantReq.getPageNo(),
                    pageQueryAccountMerchantReq.getPageSize());
        }
        return PageResult.create(result.getData().getList(),
                Integer.parseInt(String.valueOf(result.getData().getTotal())), pageQueryAccountMerchantReq.getPageNo(),
                pageQueryAccountMerchantReq.getPageSize());
    }

    public Boolean unfrozenAccount(UnfrozenMerchantAccountReq unfrozenMerchantAccountReq) {
        log.info("unfrozenAmount . 解冻账户 --> unfrozenMerchantAccountReq:{}",
                JSONObject.toJSONString(unfrozenMerchantAccountReq == null ? "" : unfrozenMerchantAccountReq));
        Result<Boolean> result = rickMerchantClient.unfrozenAccount(unfrozenMerchantAccountReq);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("unfrozenAmount . 解冻账户结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return false;
        }
        return result.getData();
    }


    public Boolean frozenAccount(FrozenMerchantAccountReq frozenMerchantAccountReq) {
        log.info("frozenAmount . 冻结账户 --> frozenMerchantAccountReq:{}",
                JSONObject.toJSONString(frozenMerchantAccountReq == null ? "" : frozenMerchantAccountReq));
        Result<Boolean> result = rickMerchantClient.frozenAccount(frozenMerchantAccountReq);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("frozenAmount . 冻结账户结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return false;
        }
        return result.getData();
    }

    public MerchantAccountResp getAccount(GetMerchantAccountReq getMerchantAccountReq) {
        log.info("getAccount . 单个账户查询 --> getMerchantAccountReq:{}",
                JSONObject.toJSONString(getMerchantAccountReq == null ? "" : getMerchantAccountReq));
        Result<MerchantAccountResp> result = rickMerchantClient.getAccount(getMerchantAccountReq);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("getAccount . 单个账户查询结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return null;
        }
        return result.getData();
    }

    public List<GetBizOpTypeResp> getBizOpTypes(GetBizOpTypeReq getBizOpTypeReq) {
        log.info("getBizOpTypes . 账户获取资金流水类型 --> GetBizOpTypeReq:{}",
                JSONObject.toJSONString(getBizOpTypeReq == null ? "" : getBizOpTypeReq));
        Result<List<GetBizOpTypeResp>> result = rickConfigClient.getBizOpTypes(getBizOpTypeReq);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("getBizOpTypes . 账户获取资金流水类型结束 --> result:{}",
                    JSONObject.toJSONString(result == null ? "" : result));
            return Collections.emptyList();
        }
        return result.getData();
    }

    public List<MerchantAccountResp> checkAccountMove(CheckAccountIsMoveReq req) {
        log.info("checkAccountMove . 资金账户是否迁移 --> GetBizOpTypeReq:{}", JSONObject.toJSONString(req == null ? "" : req));
        Result<List<MerchantAccountResp>> listResult = rickMerchantClient.checkAccountIsMove(req);
        if (listResult == null || !listResult.isSuccess() || listResult.getData() == null) {
            log.error("checkAccountMove . 资金账户是否迁移 --> result:{}",
                    JSONObject.toJSONString(listResult == null ? "" : listResult));
            return Collections.emptyList();
        }
        return listResult.getData();
    }

    public List<MerchantAccountResp> pageAccount(ListAccountMerchantPgExportReq req){
        log.info("pageQueryAccount . 获取分页账户查询 --> pageQueryAccountMerchantReq:{}", JSONObject.toJSONString(req == null ? "" : req));
        Result<List<MerchantAccountResp>> result = rickMerchantClient.listAccountSyncExport(req);
        if(Objects.isNull(result)){
            for(int i=1;i<5;i++){
                result = rickMerchantClient.listAccountSyncExport(req);
                if(Objects.nonNull(result)){
                    break;
                }
            }
            if(Objects.isNull(result)){
                log.error("pageAccount分页获取数据异常,pageQueryAccountMerchantReq={}",JSONObject.toJSONString(req == null ? "" : req));
                throw BizException.create(BizErrorCodeEnum.USER_DATA_ERROR);
            }
        }
        if(!result.isSuccess() || result.getData() == null){
            log.error("pageAccount . 获取分页账户查询结束 --> result:{}", JSONObject.toJSONString(result));
            return Collections.emptyList();
        }
        return result.getData();
    }

    public Integer countAccount(ListAccountMerchantPgExportReq req){
        log.info("countAccount . 统计账户查询 --> pageQueryAccountMerchantReq:{}", JSONObject.toJSONString(req == null ? "" : req));
        Result<Integer> result = rickMerchantClient.listAccountSyncExportCount(req);
        if(result == null || !result.isSuccess() || result.getData() == null){
            log.error("countAccount . 统计账户查询结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return null;
        }
        return result.getData();
    }

    public Boolean frozenAmount(FrozenMerchantAmountReq req) {
        Result<Boolean> result = rickMerchantClient.frozenAmount(req);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("frozenAmount . 冻结账户金额结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return false;
        }
        return result.getData();
    }

    public Boolean batchUnfrozenAmount(BatchUnfrozenMerchantAmountReq req) {
        Result<Boolean> result = rickMerchantClient.batchUnfrozenAmount(req);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("batchUnfrozenAmount . 批量解冻账户金额结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return false;
        }
        return result.getData();
    }

    public List<MerchantBalanceResp> queryFrozenBill(QueryFrozenBillNotAllReverseReq req) {
        Result<List<MerchantBalanceResp>> result = rickMerchantClient.queryFrozenBillNotAllReverse(req);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.error("queryFrozenBill . 获取当前账户手动冻结流水结束 --> result:{}", JSONObject.toJSONString(result == null ? "" : result));
            return new ArrayList<>();
        }
        return result.getData();
    }
}
