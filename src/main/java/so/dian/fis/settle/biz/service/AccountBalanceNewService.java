package so.dian.fis.settle.biz.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.common.logger.util.StringUtils;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.enums.AccountOperationTypeEnum;
import so.dian.fis.settle.common.enums.error.BizErrorCodeEnum;
import so.dian.fis.settle.common.pojo.converter.AccountBalanceConverter;
import so.dian.fis.settle.common.pojo.converter.CommonConverter;
import so.dian.fis.settle.config.properties.AccountBalanceTypeProperties;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.request.FrozenAmountReq;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceNewReq;
import so.dian.fis.settle.controller.account.request.UnfrozenAmountReq;
import so.dian.fis.settle.controller.account.response.AccountBalancePcRsp;
import so.dian.fis.settle.controller.account.response.BalanceTypeRsp;
import so.dian.himalaya.boot.constant.CommonConstants;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.exception.BizException;
import so.dian.rick.merchant.client.config.req.GetBizOpTypeReq;
import so.dian.rick.merchant.client.config.resp.GetBizOpTypeResp;
import so.dian.rick.merchant.client.entity.BatchUnfrozenMerchantAmountItem;
import so.dian.rick.merchant.client.merchant.req.BatchUnfrozenMerchantAmountReq;
import so.dian.rick.merchant.client.merchant.req.FrozenMerchantAmountReq;
import so.dian.rick.merchant.client.merchant.req.PageQueryBalanceMerchant4CRMReq;
import so.dian.rick.merchant.client.merchant.req.QueryFrozenBillNotAllReverseReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantBalanceResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static so.dian.fis.settle.common.constant.CommonConstants.BIZ_OP_TYPE;

/**
 * AccountBalanceService
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccountBalanceNewService {

    @Resource
    private RickMerchantManager rickMerchantManager;

    @Resource
    private AccountBalanceTypeProperties accountBalanceTypeProperties;

    @Resource
    private RecordOperationLogService recordOperationLogService;

    public BizResult<PageData<AccountBalancePcRsp>> pageAccountBalanceReq(CurrentUserReq userReq, PageAccountBalanceNewReq pageAccountBalanceReq) {

        if (pageAccountBalanceReq == null || StringUtils.isEmpty(pageAccountBalanceReq.getAccountNo()) || StringUtils.isEmpty(pageAccountBalanceReq.getAccountType())) {
            log.info("传值为空，无法查询！" + JSONObject.toJSONString(pageAccountBalanceReq == null ? "" : pageAccountBalanceReq));
            return BizResult.create(PageData.create(Lists.newArrayList(), 0L));
        }
        Integer accountType = Integer.parseInt(pageAccountBalanceReq.getAccountType());
        PageQueryBalanceMerchant4CRMReq pageQueryBalanceMerchantReq = new PageQueryBalanceMerchant4CRMReq();
        pageQueryBalanceMerchantReq.setAccountNo(pageAccountBalanceReq.getAccountNo());
        pageQueryBalanceMerchantReq.setAccountType(accountType);
        if(pageAccountBalanceReq.getAccountBalanceType() == null){
            Map<Integer, List<Integer>> newAccountBalanceTypeMap = accountBalanceTypeProperties.getNewAccountBalanceTypeMap();
            if(MapUtil.isNotEmpty(newAccountBalanceTypeMap)){
                List<Integer> list = newAccountBalanceTypeMap.get(accountType);
                if(CollUtil.isNotEmpty(list)){
                    pageQueryBalanceMerchantReq.setIncludeBizOpTypes(list);
                }
            }
            pageQueryBalanceMerchantReq.setIncludeFrozenBizOpTypes(Collections.singletonList(BIZ_OP_TYPE));
        } else{
            if (pageAccountBalanceReq.getAccountBalanceType().equals(BIZ_OP_TYPE)) {
                pageQueryBalanceMerchantReq.setIncludeFrozenBizOpTypes(Collections.singletonList(BIZ_OP_TYPE));
            }
            pageQueryBalanceMerchantReq.setIncludeBizOpTypes(Collections.singletonList(pageAccountBalanceReq.getAccountBalanceType()));
        }
        pageQueryBalanceMerchantReq.setPageNo(pageAccountBalanceReq.getPageNo());
        pageQueryBalanceMerchantReq.setPageSize(pageAccountBalanceReq.getPageSize());
        PageResult<MerchantBalanceResp> result = rickMerchantManager.pageQueryBalance4CRM(pageQueryBalanceMerchantReq);
        if (Objects.isNull(result) || result.getData() == null) {
            return BizResult.create(PageData.create(Lists.newArrayList(), 0L));
        }
        return CommonConverter.convertPageResultToBizResult(result, AccountBalanceConverter.buildPcNewRsp(result.getData()));
    }

    public List<BalanceTypeRsp> newAccountBalanceType(CurrentUserReq userReq,Integer accountType){
        if(userReq == null){
            throw BizException.create(BizErrorCodeEnum.USER_NOT_LOGIN);
        }
        Map<Integer, List<Integer>> newAccountBalanceTypeMap = accountBalanceTypeProperties.getNewAccountBalanceTypeMap();
        if(MapUtil.isEmpty(newAccountBalanceTypeMap)){
            return Collections.emptyList();
        }
        List<Integer> list = newAccountBalanceTypeMap.get(accountType);
        if(CollUtil.isEmpty(list)){
            return Collections.emptyList();
        }
        GetBizOpTypeReq getBizOpTypeReq = new GetBizOpTypeReq();
        getBizOpTypeReq.setIncludeBizOpTypes(list);
        List<GetBizOpTypeResp> bizOpTypes = rickMerchantManager.getBizOpTypes(getBizOpTypeReq);
        if(CollUtil.isEmpty(bizOpTypes)){
            return Collections.emptyList();
        }
        return bizOpTypes.stream().map(o->{
            BalanceTypeRsp balanceTypeRsp = new BalanceTypeRsp();
            balanceTypeRsp.setKey(o.getBizOpType());
            balanceTypeRsp.setValue(o.getBizOpTypeDesc());
            return balanceTypeRsp;
        }).collect(Collectors.toList());
    }


    public Boolean frozenAmount(CurrentUserReq userReq, FrozenAmountReq req) {
        log.info("frozenAmount req: {}", JSONObject.toJSONString(req));
        if (!AccountTypeEnum.SHARE_ACCOUNT.getKey().equals(req.getAccountType())) {
            return false;
        }
        String orderNo = IdUtil.getSnowflake(1, 1).nextIdStr();
        FrozenMerchantAmountReq frozen = new FrozenMerchantAmountReq();
        frozen.setOrderSource(CommonConstants.APPLICATION_NAME);
        frozen.setOrderNo(orderNo);
        frozen.setBizOpType(BIZ_OP_TYPE);
        frozen.setFrozenAmount(req.getFrozenAmount());
        frozen.setAccountNo(req.getAccountNo());
        frozen.setAccountType(req.getAccountType());
        log.info("frozenAmount: {}", JSONObject.toJSONString(frozen));
        rickMerchantManager.frozenAmount(frozen);

        // wutai异步记录操作日志
        recordOperationLogService.recordOperationLogs(userReq, req.getAccountNo(), req.getAccountType(), orderNo,
                AccountOperationTypeEnum.AMOUNT_FREEZE.getCode(), req.getFrozenAmount(), req.getOaApprovalNo(), null);
        return true;
    }

    public Boolean unfrozenAmount(CurrentUserReq userReq, UnfrozenAmountReq req) {
        log.info("unfrozenAmount req: {}", JSONObject.toJSONString(req));
        if (!AccountTypeEnum.SHARE_ACCOUNT.getKey().equals(req.getAccountType())) {
            return false;
        }
        //调用服务获取冻结账单列表
        QueryFrozenBillNotAllReverseReq query = new QueryFrozenBillNotAllReverseReq();
        query.setAccountType(req.getAccountType());
        query.setAccountNo(req.getAccountNo());
        query.setIncludeBizOpTypes(Collections.singletonList(BIZ_OP_TYPE));
        //冻结资金解冻顺序:按照冻结先后顺序解冻,优先解冻最先创建的冻结资金
        List<MerchantBalanceResp> billList = rickMerchantManager.queryFrozenBill(query)
                .stream()
                .sorted(Comparator.comparing(MerchantBalanceResp::getId))
                .toList();
        // 剩余解冻基金额
        long unfrozenAmount = req.getUnfrozenAmount();
        //待解冻的bill
        List<BatchUnfrozenMerchantAmountItem> itemList = new ArrayList<>();
        // 遍历账单列表
        for (MerchantBalanceResp bill : billList) {
            // 获取 changeAmount 和 reverseAmount
            long changeAmount = bill.getChangeAmount() != null ? bill.getChangeAmount() : 0L;
            long reverseAmount = bill.getReverseAmount() != null ? bill.getReverseAmount() : 0L;
            // 如果 changeAmount 是负数，取反使其变为正数
            if (changeAmount < 0) {
                changeAmount = -changeAmount;
            }
            // 计算 changeAmount - reverseAmount
            long diff = changeAmount - reverseAmount;

            // 累减差值
            unfrozenAmount -= diff;
            // 将当前账单添加到结果列表
            if (diff > 0) {
                //可以解冻
                BatchUnfrozenMerchantAmountItem item = new BatchUnfrozenMerchantAmountItem();
                item.setAccountNo(req.getAccountNo());
                item.setAccountType(req.getAccountType());
                item.setBizOpType(BIZ_OP_TYPE);
                item.setFrozenOrderSource(CommonConstants.APPLICATION_NAME);
                item.setFrozenOrderNo(bill.getOrderNo());
                item.setUnfrozenOrderSource(CommonConstants.APPLICATION_NAME);
                item.setUnfrozenOrderNo(IdUtil.getSnowflake(1, 1).nextIdStr());
                if (unfrozenAmount >= 0) {
                    item.setUnfrozenAmount(diff);
                } else {
                    item.setUnfrozenAmount(unfrozenAmount + diff);
                }
                itemList.add(item);
                log.info("unfrozenItem: {}", JSONObject.toJSONString(item));
            }
            if (unfrozenAmount <= 0) {
                break;
            }
        }

        if (CollUtil.isEmpty(itemList)) {
            return false;
        }

        BatchUnfrozenMerchantAmountReq unfrozen = new BatchUnfrozenMerchantAmountReq();
        unfrozen.setBatchUnfrozenAmountItemList(itemList);
        unfrozen.setAccountNo(req.getAccountNo());
        unfrozen.setAccountType(req.getAccountType());
        rickMerchantManager.batchUnfrozenAmount(unfrozen);

        // wutai异步记录操作日志
        String operationBatchNo = IdUtil.getSnowflake(1, 1).nextIdStr();
        recordOperationLogService.recordOperationLogs(userReq, req.getAccountNo(), req.getAccountType(), operationBatchNo,
                AccountOperationTypeEnum.AMOUNT_UNFREEZE.getCode(), null, req.getOaApprovalNo(), itemList);
        return true;
    }

    public Long getTotalFrozenAmount(String accountNo, Integer accountType) {
        // 创建查询请求对象
        QueryFrozenBillNotAllReverseReq query = new QueryFrozenBillNotAllReverseReq();
        query.setAccountType(accountType);
        query.setAccountNo(accountNo);
        query.setIncludeBizOpTypes(Collections.singletonList(BIZ_OP_TYPE));

        // 调用服务获取冻结账单列表
        List<MerchantBalanceResp> billList = rickMerchantManager.queryFrozenBill(query);

        // 计算总和
        return billList.stream()
                .mapToLong(bill -> {
                    long changeAmount = bill.getChangeAmount() != null ? bill.getChangeAmount() : 0L;
                    long reverseAmount = bill.getReverseAmount() != null ? bill.getReverseAmount() : 0L;

                    // 如果 changeAmount 是负数，取反使其变为正数
                    if (changeAmount < 0) {
                        changeAmount = -changeAmount;
                    }

                    // 计算 changeAmount - reverseAmount
                    return changeAmount - reverseAmount;
                })
                .sum();
    }
}
