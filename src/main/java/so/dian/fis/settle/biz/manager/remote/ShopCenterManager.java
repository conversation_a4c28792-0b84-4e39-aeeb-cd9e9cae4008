package so.dian.fis.settle.biz.manager.remote;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.fis.settle.remote.ShopCenterClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.util.LocalMapUtils;
import so.dian.shop.shopcenter.model.mdo.LeoShopDO;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * ShopCenterManager
 * @author: shike
 */
@Slf4j(topic = "remote")
@Service
public class ShopCenterManager {
    @Resource
    private ShopCenterClient shopCenterClient;

    public Map<Long, String> getShopByShopIds(List<Long> shopIds){
        if(CollectionUtils.isEmpty(shopIds)){
            return Maps.newHashMap();
        }
        log.info("shopcenter . 获取门店开始 --> shopIds:{}", shopIds);
        BizResult<List<LeoShopDO>> result = shopCenterClient.getShopByShopIds(shopIds);
        log.info("shopcenter . 获取门店结束 --> result:{}", JSONObject.toJSONString(result));
        if(!result.isSuccess() || CollectionUtils.isEmpty(result.getData())){
            return Maps.newHashMap();
        }
        Map<Long, String> map = LocalMapUtils.listAsHashMap(result.getData(), LeoShopDO::getId, LeoShopDO::getName);
        return map;
    }

}
