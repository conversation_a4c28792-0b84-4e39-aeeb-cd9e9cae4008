package so.dian.fis.settle.biz.manager.remote;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.fis.settle.remote.WithdrawPlatformClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.withdraw.platform.param.MerchantDrawApplyNewParams;

import jakarta.annotation.Resource;

/**
 * WithdrawPlatformManager
 *
 * <AUTHOR>
 */
@Slf4j(topic = "remote")
@Service
public class WithdrawPlatformManager {

    @Resource
    private WithdrawPlatformClient withdrawPlatformClient;

    public Boolean cancelNew(MerchantDrawApplyNewParams params) {
        if (params == null) {
            return null;
        }
        BizResult<Boolean> result = withdrawPlatformClient.cancelNew(params);
        log.info("WithdrawPlatformClient cancelNew->MerchantDrawApplyNewParams:{},resp:{}", JSONObject.toJSONString(params), result);
        if (result.isSuccess()) {
            return result.getData();
        }
        return null;
    }

}
