package so.dian.fis.settle.biz.service;

import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.config.TransferBizConfig;
import so.dian.fis.settle.controller.transfer.request.PageTransferRecordDetailReq;
import so.dian.fis.settle.controller.transfer.request.PageTransferRecordReq;
import so.dian.fis.settle.controller.transfer.response.PageTransferRecordDetailRsp;
import so.dian.fis.settle.controller.transfer.response.PageTransferRecordRsp;
import so.dian.fis.settle.facade.WutaiFacade;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.wutai.client.pojo.dto.TransferRecordDetailExportDTO;
import so.dian.wutai.client.pojo.dto.TransferRecordDetailPageDTO;
import so.dian.wutai.client.pojo.dto.TransferRecordDetailPageRspDTO;
import so.dian.wutai.client.pojo.dto.TransferRecordPageDTO;
import so.dian.wutai.client.pojo.dto.TransferRecordPageRspDTO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TransferService
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferService {

    @Resource
    private WutaiFacade wutaiFacade;

    @Resource
    private TransferBizConfig transferBizConfig;

    public PageData<PageTransferRecordRsp> pageDeduct(PageTransferRecordReq pageTransferReq) {
        PageData<TransferRecordPageRspDTO> transferRecordPageRspDTOPageData = wutaiFacade.transferRecordPage(buildTransferRecordPageDTO(pageTransferReq));
        List<TransferRecordPageRspDTO> list = transferRecordPageRspDTOPageData.getList();
        return PageData.create(buildTransferRecordPageDTO(list),transferRecordPageRspDTOPageData.getTotalCount(),transferRecordPageRspDTOPageData.getPageNo(),transferRecordPageRspDTOPageData.getPageSize());
    }
    private TransferRecordPageDTO buildTransferRecordPageDTO(PageTransferRecordReq pageTransferReq){
        TransferRecordPageDTO transferRecordPageDTO = new TransferRecordPageDTO();
        transferRecordPageDTO.setTransferRecordId(pageTransferReq.getTransferRecordId());
        transferRecordPageDTO.setOperateEndTime(pageTransferReq.getOperateEndTime());
        transferRecordPageDTO.setOperateStartTime(pageTransferReq.getOperateStartTime());
        transferRecordPageDTO.setPageNo(pageTransferReq.getPageNo());
        transferRecordPageDTO.setPageSize(pageTransferReq.getPageSize());
        return transferRecordPageDTO;
    }
    private List<PageTransferRecordRsp> buildTransferRecordPageDTO(List<TransferRecordPageRspDTO> list){
        if(CollUtil.isEmpty(list)){
            return Collections.emptyList();
        }

        return list.stream().map(o->{
            PageTransferRecordRsp pageTransferRecordRsp = new PageTransferRecordRsp();
            pageTransferRecordRsp.setId(o.getId());
            pageTransferRecordRsp.setBillMonth(o.getBillMonth());
            pageTransferRecordRsp.setOperaterName(o.getOperaterName());
            pageTransferRecordRsp.setOperaterTime(o.getOperaterTime());
            pageTransferRecordRsp.setStatusName(o.getStatusName());
            return pageTransferRecordRsp;
        }).collect(Collectors.toList());
    }

    public PageData<PageTransferRecordDetailRsp> pageTransferDetail(PageTransferRecordDetailReq pageTransferRecordDetailReq) {
        PageData<TransferRecordDetailPageRspDTO> transferRecordPageRspDTOPageData = wutaiFacade.transferRecordDetailPage(buildTransferRecordDetailPageDTO(pageTransferRecordDetailReq));
        List<TransferRecordDetailPageRspDTO> list = transferRecordPageRspDTOPageData.getList();
        return PageData.create(buildTransferRecordDetailPageDTO(list),transferRecordPageRspDTOPageData.getTotalCount(),transferRecordPageRspDTOPageData.getPageNo(),transferRecordPageRspDTOPageData.getPageSize());
    }

    private TransferRecordDetailPageDTO buildTransferRecordDetailPageDTO(PageTransferRecordDetailReq pageTransferRecordDetailReq){
        TransferRecordDetailPageDTO transferRecordPageDTO = new TransferRecordDetailPageDTO();
        transferRecordPageDTO.setTransferRecordId(pageTransferRecordDetailReq.getTransferRecordId());
        transferRecordPageDTO.setPageNo(pageTransferRecordDetailReq.getPageNo());
        transferRecordPageDTO.setPageSize(pageTransferRecordDetailReq.getPageSize());
        return transferRecordPageDTO;
    }

    private List<PageTransferRecordDetailRsp> buildTransferRecordDetailPageDTO(List<TransferRecordDetailPageRspDTO> list){
        if(CollUtil.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream().map(o->{
            PageTransferRecordDetailRsp pageTransferRecordRsp = new PageTransferRecordDetailRsp();
            pageTransferRecordRsp.setId(o.getId());
            pageTransferRecordRsp.setIncomeBillNo(o.getIncomeBillNo());
            pageTransferRecordRsp.setIncomeAccountAmount(o.getIncomeAccountAmount());
            pageTransferRecordRsp.setIncomeSubjectName(o.getIncomeSubjectName());
            pageTransferRecordRsp.setIncomeBillStatusName(o.getIncomeBillStatusName());
            pageTransferRecordRsp.setNeedTransferAmount(o.getNeedTransferAmount());
            pageTransferRecordRsp.setTransferAmount(o.getTransferAmount());
            pageTransferRecordRsp.setPayBillNo(o.getPayBillNo());
            pageTransferRecordRsp.setPayAccountAmount(o.getPayAccountAmount());
            pageTransferRecordRsp.setPaySubjectName(o.getPaySubjectName());
            pageTransferRecordRsp.setPayBillStatusName(o.getPayBillStatusName());
            pageTransferRecordRsp.setOpenTime(o.getOpenTime());
            pageTransferRecordRsp.setPaySubjectId(o.getPaySubjectId());
            pageTransferRecordRsp.setPayAccountTypeStr(o.getPayAccountTypeStr());
            pageTransferRecordRsp.setPaySettleSubjectName(o.getPaySettleSubjectName());
            pageTransferRecordRsp.setIncomeSubjectId(o.getIncomeSubjectId());
            pageTransferRecordRsp.setIncomeAccountTypeStr(o.getIncomeAccountTypeStr());
            pageTransferRecordRsp.setIncomeSettleSubjectName(o.getIncomeSettleSubjectName());
            pageTransferRecordRsp.setTransferReason(o.getTransferReason());
            return pageTransferRecordRsp;
        }).collect(Collectors.toList());
    }

    public BizResult<Boolean> transferRecordDetailExport(PageTransferRecordDetailReq req,CurrentUserReq userReq){
        TransferRecordDetailExportDTO transferRecordDetailExportDTO = new TransferRecordDetailExportDTO();
        transferRecordDetailExportDTO.setTransferRecordId(req.getTransferRecordId());
        transferRecordDetailExportDTO.setUserMail(userReq.getUserMail());
        return wutaiFacade.transferRecordDetailExport(transferRecordDetailExportDTO);
    }

    public BizResult<String> transferRecordExecute(Long userId){
        String result = wutaiFacade.transferRecordCheck();
        if(StringUtils.isEmpty(result)){
            return BizResult.error("5001","校验失败，请重新发起");
        }
        if(StringUtils.isNotEmpty(result) && !"success".equals(result)){
            return BizResult.error("5001",result);
        }
        Boolean flag = wutaiFacade.transferRecordExecute(userId);
        return flag ? BizResult.create("抵扣记录创建成功") : BizResult.error("5001","该账期已有处理中的抵扣记录，请勿重复操作");
    }

    public Boolean transferCheckPermission(CurrentUserReq userReq){
        if(CollUtil.isEmpty(transferBizConfig.getUserIds())){
            return false;
        }
        if(transferBizConfig.getUserIds().contains(userReq.getUserId())){
            return true;
        }
        return false;
    }
}
