package so.dian.fis.settle.biz.manager.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceReq;
import so.dian.fis.settle.remote.LvyClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.lvy.pojo.dto.AccountBalanceDTO;
import so.dian.lvy.pojo.dto.AccountBillItemDTO;
import so.dian.lvy.pojo.dto.AccountBillItemSourceDTO;
import so.dian.lvy.pojo.dto.AccountDTO;
import so.dian.lvy.pojo.dto.AccountStatisticsDTO;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.dto.BillDetailDTO;
import so.dian.lvy.pojo.dto.PayApplyDTO;
import so.dian.lvy.pojo.dto.QueryAccountListDTO;
import so.dian.lvy.pojo.param.AddBillParam;
import so.dian.lvy.pojo.param.BatchBillManualParam;
import so.dian.lvy.pojo.param.BillBatchOperationParam;
import so.dian.lvy.pojo.param.BillManualParam;
import so.dian.lvy.pojo.param.BillOperationParam;
import so.dian.lvy.pojo.query.AccountBalanceNewQuery;
import so.dian.lvy.pojo.query.AccountListQuery;
import so.dian.lvy.pojo.query.BillItemListQuery;
import so.dian.lvy.pojo.query.BillListQuery;
import so.dian.lvy.pojo.query.PayApplyDetailQuery;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * LvyManager
 *
 * <AUTHOR>
 */
@Slf4j(topic = "remote")
@Service
public class LvyManager {

    @Resource
    private LvyClient lvyClient;

    //-------account-------
    public PageResult<QueryAccountListDTO> pageAccount(AccountListQuery query) {
        return lvyClient.pageAccount(query);
    }

    public AccountDTO getAccountById(Long accountId) {
        if (Objects.isNull(accountId)) {
            return null;
        }
        BizResult<AccountDTO> accountResult = lvyClient.getAccountById(accountId);
        if (Objects.isNull(accountResult)) {
            log.error("lvyManager getAccountById result is null, accountId:{}", accountId);
            return null;
        }
        if (!accountResult.isSuccess()) {
            log.error("lvyManager getAccountById result is fail, accountId:{}", accountId);
            return null;
        }
        return accountResult.getData();
    }

    public AccountStatisticsDTO queryAccountAmount(AccountListQuery query) {
        BizResult<AccountStatisticsDTO> statisticsDTOBizResult = lvyClient.statisticsAccount(query);
        if (Objects.isNull(statisticsDTOBizResult)) {
            log.error("lvyManager queryAccountAmount result is null, query:{}", query);
            return null;
        }
        if (!statisticsDTOBizResult.isSuccess()) {
            log.error("lvyManager queryAccountAmount result is fail, query:{}", query);
            return null;
        }
        return statisticsDTOBizResult.getData();
    }

    public Boolean freeze(Long accountId, Long userId, String userName) {
        BizResult<Boolean> freezeResult = lvyClient.freeze(accountId, userId, userName);
        if (Objects.isNull(freezeResult)) {
            log.error("lvyManager freezeAccount result is null, accountId:{},userId:{},userName:{}", accountId, userId,
                    userName);
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR);
        }
        if (!freezeResult.isSuccess()) {
            log.error("lvyManager freezeAccount result is not success, accountId:{},userId:{},userName:{}", accountId,
                    userId, userName);
            throw BizException.create(freezeResult);
        }
        if (!freezeResult.getData()) {
            log.error("lvyManager freezeAccount result is fail, accountId:{},userId:{},userName:{}", accountId, userId,
                    userName);
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR, "冻结失败");
        }
        return true;
    }

    public Boolean unFreeze(Long accountId, Long userId, String userName) {
        BizResult<Boolean> freezeResult = lvyClient.unFreeze(accountId, userId, userName);
        if (Objects.isNull(freezeResult)) {
            log.error("lvyManager unFreezeAccount result is null, accountId:{},userId:{},userName:{}", accountId,
                    userId,
                    userName);
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR);
        }
        if (!freezeResult.isSuccess()) {
            log.error("lvyManager unFreezeAccount result is not success, accountId:{},userId:{},userName:{}", accountId,
                    userId, userName);
            throw BizException.create(freezeResult);
        }
        if (!freezeResult.getData()) {
            log.error("lvyManager unFreezeAccount result is fail, accountId:{},userId:{},userName:{}", accountId,
                    userId,
                    userName);
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR, "解冻失败");
        }
        return true;
    }

    //-------bill-------
    public String addBill(AddBillParam param) {
        log.info("资金变更请求参数:{}", param);
        BizResult<String> addResult = lvyClient.addBill(param);
        log.info("资金变更结果addBill:{},billNo:{}", addResult, param.getBillParam().getOutBizNo());
        if (Objects.isNull(addResult) || !addResult.isSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "资金变更失败");
        }
        return addResult.getData();

    }

    @Deprecated
    public Boolean addManualBill(BillManualParam param) {
        log.info("add manual bill param:{}", JSON.toJSONString(param));
        BizResult<String> addResult = lvyClient.addBillByManual(param);
        log.info("add manual bill result:{}", addResult);
        if (Objects.isNull(addResult) || !addResult.isSuccess()) {
            return false;
        }
        return addResult.isSuccess();
    }

    public BizResult<Integer> addManualBillBatch(BatchBillManualParam params) {
        log.info("add manual bill batch param:{}", JSON.toJSONString(params));
        BizResult<Integer> addResult = lvyClient.batchManual(params);
        log.info("add manual bill batch result:{}", addResult);
        return addResult;
    }

    public String addManualBillReturnStr(BillManualParam param) {
        BizResult<String> addResult = lvyClient.addBillByManual(param);
        log.info("add manual bill result:{}", addResult);
        checkResult(addResult);
        return addResult.getData();
    }

    public String editBill(String billNo, BillManualParam billManualParam) {
        BizResult<String> editResult = lvyClient.editBill(billNo, billManualParam);
        log.info("edit manual bill result:{}", editResult);
        checkResult(editResult);
        return editResult.getData();
    }

    public String discardBill(BillOperationParam billOperationParam) {
        BizResult<String> discardResult = lvyClient.discardBill(billOperationParam);
        log.info("edit manual bill result:{}", discardResult);
        checkResult(discardResult);
        return discardResult.getData();
    }

    public Boolean batchApprovalBill(BillBatchOperationParam param) {
        BizResult<Boolean> approvalResult = lvyClient.approveBillByBatch(param);
        log.info("batch approval bill result:{}, param:{}", approvalResult, JSONObject.toJSONString(param));
        checkResult(approvalResult);
        return approvalResult.getData();
    }

    public String approvalBill(BillOperationParam billOperationParam) {
        BizResult<String> approvalResult = lvyClient.approveBill(billOperationParam);
        log.info("approval bill result:{}, param:{}", approvalResult, JSONObject.toJSONString(billOperationParam));
        checkResult(approvalResult);
        return approvalResult.getData();
    }

    public String confirmBill(BillOperationParam param) {
        BizResult<String> approvalResult = lvyClient.confirmationBill(param);
        log.info("approval bill result:{}, param:{}", approvalResult, JSONObject.toJSONString(param));
        checkResult(approvalResult);
        return approvalResult.getData();
    }

    public String settleBillAgain(BillOperationParam param) {
        BizResult<String> approvalResult = lvyClient.settlementBillAgain(param);
        log.info("settle bill again result:{}, param:{}", approvalResult, JSONObject.toJSONString(param));
        checkResult(approvalResult);
        return approvalResult.getData();
    }

    public List<BillDTO> pageBillList(BillListQuery listQuery) {
        log.info("pageBillList get listQuery:{}",listQuery);
        PageResult<BillDTO> billPageResult = lvyClient.listBillByPage(listQuery);
        if (Objects.isNull(billPageResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billPageResult.isSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, billPageResult.getMsg());
        }
        return billPageResult.getData();
    }
    public PageResult<BillDTO> pageBill(BillListQuery listQuery) {
        PageResult<BillDTO> billPageResult = lvyClient.listBillByPage(listQuery);
        if (Objects.isNull(billPageResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billPageResult.isSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, billPageResult.getMsg());
        }
        return billPageResult;
    }

    /**
     * 获取结算的总数量
     */
    public int getBillTotalCount(BillListQuery listQuery) {
        if (Objects.isNull(listQuery)) {
            return 0;
        }
        // 设置一页只有一条,只获取总量
        listQuery.setPageNo(1);
        listQuery.setPageSize(1);
        PageResult<BillDTO> billPageResult = pageBill(listQuery);
        return billPageResult.getTotalCount();
    }

    public Long getBillStatistics(BillListQuery listQuery) {
        BizResult<Long> statisticsResult = lvyClient.getBillStatistics(listQuery);
        if (Objects.isNull(statisticsResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!statisticsResult.isSuccess()) {
            throw BizException.create(statisticsResult);
        }
        if (Objects.isNull(statisticsResult.getData())) {
            return 0L;
        }
        return statisticsResult.getData();
    }

    public BillDTO getBillInfo(String billNo, Integer settleSubjectType, Long settleSubjectId) {
        BizResult<BillDTO> billDTOBizResult = lvyClient.getBillInfo(billNo, settleSubjectType, settleSubjectId);
        if (Objects.isNull(billDTOBizResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billDTOBizResult.isSuccess()) {
            throw BizException.create(billDTOBizResult);
        }
        return billDTOBizResult.getData();
    }

    public BillDetailDTO getBillDetail(String billNo, Integer settleSubjectType, Long settleSubjectId) {
        BizResult<BillDetailDTO> billDetailDTOBizResult = lvyClient
                .getBillDetail(billNo, settleSubjectType, settleSubjectId);
        if (Objects.isNull(billDetailDTOBizResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billDetailDTOBizResult.isSuccess()) {
            throw BizException.create(billDetailDTOBizResult);
        }
        return billDetailDTOBizResult.getData();
    }

    public PageResult<AccountBillItemDTO> pageBillItem(BillItemListQuery listQuery) {
        log.info("pageBillItem --> listQuery:{}", JSON.toJSONString(listQuery));
        return lvyClient.getBillItems(listQuery);
    }

    public Long statisticsBillItems(BillItemListQuery listQuery) {
        BizResult<Long> billItemStatisticsResult = lvyClient.getBillItemStatistics(listQuery);
        if (Objects.isNull(billItemStatisticsResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billItemStatisticsResult.isSuccess()) {
            throw BizException.create(billItemStatisticsResult);
        }
        return billItemStatisticsResult.getData();
    }

    public PageResult<String> getBillItemNos(BillItemListQuery query) {
        return lvyClient.getBillItemNos(query);
    }

    public List<AccountBillItemSourceDTO> getBillShops(String billNo, List<Long> shopIdList) {
        BizResult<List<AccountBillItemSourceDTO>> billItemSourceListResult = lvyClient.getBillShops(billNo, shopIdList);
        if (Objects.isNull(billItemSourceListResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billItemSourceListResult.isSuccess()) {
            throw BizException.create(billItemSourceListResult);
        }
        return billItemSourceListResult.getData();
    }

    //-------payApply--------
    public PayApplyDTO getPayApplyByApplyNo(String payBillApplyNo) {
        if (StringUtils.isBlank(payBillApplyNo)) {
            return null;
        }
        PayApplyDetailQuery payApplyDetailQuery = new PayApplyDetailQuery();
        payApplyDetailQuery.setPayApplyNo(payBillApplyNo);
        BizResult<PayApplyDTO> addResult = lvyClient.getPayApplyByApplyNo(payApplyDetailQuery);
        log.info("add manual bill result:{}", addResult);
        if (Objects.isNull(addResult) || !addResult.isSuccess()) {
            return null;
        }
        return addResult.getData();

    }

    //--------balance----------
    public PageResult<AccountBalanceDTO> pageBalanceList(PageAccountBalanceReq pageAccountBalanceReq) {
        AccountBalanceNewQuery query = new AccountBalanceNewQuery();
        query.setAccountId(pageAccountBalanceReq.getAccountId());
        query.setBalanceBizDocType(pageAccountBalanceReq.getBalanceBizDocType());
        query.setPageNo(pageAccountBalanceReq.getPageNo());
        query.setPageSize(pageAccountBalanceReq.getPageSize());
        return lvyClient.listBillingDetails(query);
    }


    private void checkResult(BizResult<?> result) {

        if (Objects.isNull(result)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }

        if (!result.isSuccess()) {
            throw BizException.create(result);
        }
    }
}
