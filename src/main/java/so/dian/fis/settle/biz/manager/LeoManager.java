package so.dian.fis.settle.biz.manager;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.common.logger.Log;
import so.dian.fis.settle.common.constant.LoggerConstants;
import so.dian.fis.settle.remote.LeoRemoteService;

import java.util.Objects;

@Service
public class LeoManager {
    @Autowired
    private LeoRemoteService leoRemoteService;

    /**
     * 获取付款单的税前金额
     *
     * @param applyNo
     * @return
     */
    public Long getpayPlanBeforeTaxAmount(String applyNo) {
        String result = leoRemoteService.getpayPlanBeforeTaxAmount(applyNo);
        Log.info(LoggerConstants.BIZ, ".查询付款申请单税前金额:applyNo:{},result:{}", applyNo, result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (Objects.nonNull(jsonObject)) {
            Boolean successFlag = jsonObject.getBoolean("success");
            if (Objects.nonNull(successFlag) && successFlag) {
                return jsonObject.getLong("data");
            }
        }
        return null;
    }
}
