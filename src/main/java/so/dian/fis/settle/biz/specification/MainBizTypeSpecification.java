package so.dian.fis.settle.biz.specification;

import java.util.Objects;
import java.util.concurrent.locks.StampedLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.agent.api.constant.AgentBaseEnum.AgentHierarchyEnum;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;

/**
 * MainBizTypeSpecification
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/20 10:58
 */
@Component
public class MainBizTypeSpecification {

    @Autowired
    private AgentManager agentManager;

    /**
     * 一级代理商
     *
     * @param req
     * @return
     */
    public boolean tier1MainBiz(CurrentUserReq req) {
        Long settleSubjectId = req.getSettleSubjectId();
        AgentDTO agentDTO = agentManager.findById(settleSubjectId);
        Integer hierarchy = agentDTO.getHierarchy();
        AgentHierarchyEnum agentHierarchyEnum = AgentHierarchyEnum.get(hierarchy).orElse(null);
        return Objects.equals(agentHierarchyEnum, AgentHierarchyEnum.ONE_HIERARCHY);
    }

    /**
     * 归属小电
     * @param req
     * @return
     */
    public boolean tierDian(CurrentUserReq req) {
        Long settleSubjectId = req.getSettleSubjectId();
        AgentDTO agentDTO = agentManager.findById(settleSubjectId);
        Integer hierarchy = agentDTO.getHierarchy();
        AgentHierarchyEnum agentHierarchyEnum = AgentHierarchyEnum.get(hierarchy).orElse(null);
        return Objects.equals(agentHierarchyEnum, AgentHierarchyEnum.TOP_HIERARCHY);
    }


    /**
     * 二级代理商
     *
     * @param req
     * @return
     */
    public boolean tier2MainBiz(CurrentUserReq req) {
        Long settleSubjectId = req.getSettleSubjectId();
        AgentDTO agentDTO = agentManager.findById(settleSubjectId);
        Integer hierarchy = agentDTO.getHierarchy();
        AgentHierarchyEnum agentHierarchyEnum = AgentHierarchyEnum.get(hierarchy).orElse(null);
        return Objects.equals(agentHierarchyEnum, AgentHierarchyEnum.TWO_HIERARCHY);
    }

}
