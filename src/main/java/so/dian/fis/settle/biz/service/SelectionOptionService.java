package so.dian.fis.settle.biz.service;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.agent.api.constant.AgentBaseEnum;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.pojo.val.AgentVal;
import so.dian.fis.settle.controller.account.enums.UserRoleExtendEnum;
import so.dian.fis.settle.controller.account.response.NameValueRsp;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2022/10/13 18:34
 * @description:
 */
@Service
public class SelectionOptionService {

    @Autowired
    private AgentManager agentManager;

    public List<NameValueRsp<String, String>> belongCompanys(CurrentUserReq userReq) {

        List<NameValueRsp<String, String>> nameValues = Lists.newArrayList();
        if (SettleSubjectTypeEnum.XIAODIAN.getCode().equals(userReq.getSettleSubjectType())) {
            nameValues.add(new NameValueRsp<>("0", "杭州友电"));
            nameValues.add(new NameValueRsp<>("1", "杭州伊电园（旧）"));
            nameValues.add(new NameValueRsp<>("3", "杭州伊电园（新）"));
            List<String> roleList = Arrays.asList(UserRoleEnum.CITY_PLANING_MANAGER.getRoleName(), UserRoleEnum.CHANNEL_OPERATE.getRoleName());
            if (roleList.contains(userReq.getCurrentRole())){
                return nameValues;
            }
            List<AgentDTO> agentJVDTOS = agentManager.listAgentByType(AgentBaseEnum.AgentTypeEnum.JV_COMPANY_TYPE);

            agentJVDTOS
                    .stream()
                    .map(AgentVal::of)
                    .filter(agentVal -> !agentVal.isSecond())
                    .map(AgentVal::getAgentDTO)
                    .forEach(agentDTO -> nameValues.add(new NameValueRsp<>(agentDTO.getAgentId().toString(), agentDTO.getAgentName())));

            if(UserRoleExtendEnum.OUTSOURCED_CUSTOMER_SERVICE_M.getRoleName().equals(userReq.getCurrentRole())){
                List<AgentDTO> agentDTOS = agentManager.listAgentByType(AgentBaseEnum.AgentTypeEnum.AGENT_TYPE);
                agentDTOS
                        .stream()
                        .map(AgentVal::of)
                        .filter(agentVal -> !agentVal.isDian())
                        .map(AgentVal::getAgentDTO)
                        .forEach(agentDTO -> nameValues.add(new NameValueRsp<>(agentDTO.getAgentId().toString(), agentDTO.getAgentName())));
            }
        }else {
            AgentDTO agentDTO = agentManager.findById(userReq.getSettleSubjectId());

            if (!Objects.isNull(agentDTO)) {
                nameValues.add(new NameValueRsp<>(agentDTO.getAgentId().toString(), agentDTO.getAgentName()));
            }
        }

        return nameValues;
    }
}
