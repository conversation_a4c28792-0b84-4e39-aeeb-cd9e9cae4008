package so.dian.fis.settle.biz.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.fis.settle.biz.specification.MainBizTypeSpecification;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.config.properties.XdBizCommonsFrontendSplitProperties;
import so.dian.fis.settle.controller.account.enums.AccountStatusEnum;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.enums.MerchantTypeEnum;
import so.dian.fis.settle.controller.account.enums.UserRoleExtendEnum;
import so.dian.fis.settle.controller.account.response.AccountStatusRsp;
import so.dian.fis.settle.controller.account.response.AccountTypeRsp;
import so.dian.fis.settle.controller.account.response.BalanceTypeRsp;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;
import so.dian.lvy.pojo.enums.BillRelateAccountTypeEnum;
import so.dian.withdraw.platform.enums.MainBizTypeEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jiaoge
 * @Date: 2020/4/2 2:28 PM
 * @Description:
 */
@Slf4j
@Service
public class ConstantService {


    @Autowired
    private MainBizTypeSpecification mainBizTypeSpecification;

    @Autowired
    private XdBizCommonsFrontendSplitProperties frontendSplitProperties;

    /**
     * 资金账户、结算系统都用的这个，需求不同的话，自己拆成两个方法
     */
    public List<NameValueDTO> getObjectType(CurrentUserReq userReq) {
        if (userReq == null) {
            return new ArrayList<>();
        }
        if (mainBizTypeSpecification.tierDian(userReq)) {
            return LocalEnumUtils.getEnumCodeAndDesc(MainBizTypeEnum.class);
        } else if (mainBizTypeSpecification.tier1MainBiz(userReq)) {
            List<MainBizTypeEnum> objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                    MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                    MainBizTypeEnum.JOIN_MERCHANT,//加盟商户
                    MainBizTypeEnum.AGENT
            );
            List<NameValueDTO> nameValueDTOList = new ArrayList<>(objectTypeEnums.size());
            for (EnumInterface<MainBizTypeEnum> objectTypeEnum : objectTypeEnums) {
                nameValueDTOList.add(new NameValueDTO(objectTypeEnum.getDesc(), objectTypeEnum.getCode().toString()));
            }
            return nameValueDTOList;
        } else {
            List<MainBizTypeEnum> objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                    MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                    MainBizTypeEnum.JOIN_MERCHANT//加盟商户
            );
            List<NameValueDTO> nameValueDTOList = new ArrayList<>(objectTypeEnums.size());
            for (EnumInterface<MainBizTypeEnum> objectTypeEnum : objectTypeEnums) {
                nameValueDTOList.add(new NameValueDTO(objectTypeEnum.getDesc(), objectTypeEnum.getCode().toString()));
            }
            return nameValueDTOList;
        }
    }

    public List<NameValueDTO> getObjectTypeNew(CurrentUserReq userReq) {
        if (userReq == null) {
            return new ArrayList<>();
        }
        if(UserRoleExtendEnum.OUTSOURCED_CUSTOMER_SERVICE_M.getRoleName().equals(userReq.getCurrentRole())){
            return getNameValueDTOList();
        }

        List<String> roleList = Arrays.asList(UserRoleEnum.CITY_PLANING_MANAGER.getRoleName(), UserRoleEnum.CHANNEL_OPERATE.getRoleName());
        if (roleList.contains(userReq.getCurrentRole())){
            List<MainBizTypeEnum> objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.AGENT,//代理商
                    MainBizTypeEnum.JV_CORP//合资
            );
            List<NameValueDTO> nameValueDTOList = new ArrayList<>(objectTypeEnums.size());
            for (EnumInterface<MainBizTypeEnum> objectTypeEnum : objectTypeEnums) {
                nameValueDTOList.add(new NameValueDTO(objectTypeEnum.getDesc(), objectTypeEnum.getCode().toString()));
            }
            return nameValueDTOList;
        }
        if (mainBizTypeSpecification.tierDian(userReq)) {
            List<MainBizTypeEnum> mainBizTypeEnums = Arrays.asList(MainBizTypeEnum.NORMAL_MERCHANT,
                    MainBizTypeEnum.JOIN_MERCHANT,
                    MainBizTypeEnum.BRAND_MERCHANT,
                    MainBizTypeEnum.AGENT, MainBizTypeEnum.JV_CORP, MainBizTypeEnum.OPERATION_SERVICE);
            List<NameValueDTO> nameValueDTOList = new ArrayList<>();

            for (MainBizTypeEnum mainBizTypeEnum : mainBizTypeEnums) {
                nameValueDTOList.add(new NameValueDTO(mainBizTypeEnum.getDesc(), mainBizTypeEnum.getCode().toString()));
            }
            return nameValueDTOList;
        } else if (mainBizTypeSpecification.tier1MainBiz(userReq)) {
            List<MainBizTypeEnum> objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                    MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                    MainBizTypeEnum.JOIN_MERCHANT,//加盟商户
                    MainBizTypeEnum.AGENT
            );
            List<NameValueDTO> nameValueDTOList = new ArrayList<>(objectTypeEnums.size());
            for (EnumInterface<MainBizTypeEnum> objectTypeEnum : objectTypeEnums) {
                nameValueDTOList.add(new NameValueDTO(objectTypeEnum.getDesc(), objectTypeEnum.getCode().toString()));
            }
            return nameValueDTOList;
        } else {
            return getNameValueDTOList();
        }
    }

    private List<NameValueDTO> getNameValueDTOList(){
        List<MainBizTypeEnum> objectTypeEnums = Lists.newArrayList(
                MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                MainBizTypeEnum.JOIN_MERCHANT//加盟商户
        );
        List<NameValueDTO> nameValueDTOList = new ArrayList<>(objectTypeEnums.size());
        for (EnumInterface<MainBizTypeEnum> objectTypeEnum : objectTypeEnums) {
            nameValueDTOList.add(new NameValueDTO(objectTypeEnum.getDesc(), objectTypeEnum.getCode().toString()));
        }
        return nameValueDTOList;
    }

    /**
     * 资金账户、结算系统都用的这个，需求不同的话，自己拆成两个方法
     */
    public List<NameValueDTO> getAccountObjectType(CurrentUserReq userReq) {
        if (userReq == null) {
            return new ArrayList<>();
        }
        List<MainBizTypeEnum> objectTypeEnums = null;
        if (mainBizTypeSpecification.tierDian(userReq)) {
            objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                    MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                    MainBizTypeEnum.JOIN_MERCHANT,//加盟商户
                    MainBizTypeEnum.OPEN_SERVICE,//开户型服务商
                    MainBizTypeEnum.RESOURCE_SERVICE,//资源型服务商
                    MainBizTypeEnum.DIAN_USERS,//消费者用户
                    MainBizTypeEnum.KP_SERVICE,//KP服务商
                    MainBizTypeEnum.IOT_MERCHANT,//IoT商户
                    MainBizTypeEnum.JV_CORP
            );
        } else if (mainBizTypeSpecification.tier1MainBiz(userReq)) {
            objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                    MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                    MainBizTypeEnum.JOIN_MERCHANT,//加盟商户,
                    MainBizTypeEnum.AGENT
            );

        } else {
            objectTypeEnums = Lists.newArrayList(
                    MainBizTypeEnum.NORMAL_MERCHANT,//普通商户
                    MainBizTypeEnum.BRAND_MERCHANT,//品牌商户
                    MainBizTypeEnum.JOIN_MERCHANT//加盟商户
            );
        }
        List<NameValueDTO> nameValueDTOList = new ArrayList<>(objectTypeEnums.size());
        for (EnumInterface<MainBizTypeEnum> objectTypeEnum : objectTypeEnums) {
            nameValueDTOList.add(new NameValueDTO(objectTypeEnum.getDesc(), objectTypeEnum.getCode().toString()));
        }
        return nameValueDTOList;
    }

    /**
     * 新迁移资金账户类型
     */
    public List<NameValueDTO> getObjectNewType(CurrentUserReq userReq) {
        if (userReq == null) {
            return new ArrayList<>();
        }
        return LocalEnumUtils.getEnumCodeAndDesc(MerchantTypeEnum.class);
    }

    public List<AccountTypeRsp> getAccountType(CurrentUserReq req) {
        List<AccountTypeRsp> list = new ArrayList<>();
        if(UserRoleExtendEnum.OUTSOURCED_CUSTOMER_SERVICE_M.getRoleName().equals(req.getCurrentRole())){
            AccountTypeRsp accountTypeRsp = new AccountTypeRsp();
            accountTypeRsp.setKey(AccountTypeEnum.SHARE_ACCOUNT.getKey());
            accountTypeRsp.setValue(AccountTypeEnum.SHARE_ACCOUNT.getValue());
            list.add(accountTypeRsp);
            AccountTypeRsp accountTypeRsp1 = new AccountTypeRsp();
            accountTypeRsp1.setKey(AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getKey());
            accountTypeRsp1.setValue(AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getValue());
            list.add(accountTypeRsp1);
            AccountTypeRsp accountTypeRsp2 = new AccountTypeRsp();
            accountTypeRsp2.setKey(AccountTypeEnum.IOT_PING_AN_SHARE_ACCOUNT.getKey());
            accountTypeRsp2.setValue(AccountTypeEnum.IOT_PING_AN_SHARE_ACCOUNT.getValue());
            list.add(accountTypeRsp2);
            return list;
        }
        if (Objects.equals(SettleSubjectTypeEnum.XIAODIAN.getCode(), req.getSettleSubjectType())) {
            for (AccountTypeEnum levelEnum : AccountTypeEnum.values()) {
                AccountTypeRsp accountTypeRsp = new AccountTypeRsp();
                accountTypeRsp.setKey(levelEnum.getKey());
                accountTypeRsp.setValue(levelEnum.getValue());
                list.add(accountTypeRsp);
            }
        } else {
            AccountTypeRsp accountTypeRsp = new AccountTypeRsp();
            accountTypeRsp.setKey(AccountTypeEnum.SHARE_ACCOUNT.getKey());
            accountTypeRsp.setValue(AccountTypeEnum.SHARE_ACCOUNT.getValue());
            list.add(accountTypeRsp);
            if (frontendSplitProperties.getAccessControl().getAllowedAgentId().contains(req.getSettleSubjectId())) {
                AccountTypeRsp accountTypeRsp1 = new AccountTypeRsp();
                accountTypeRsp1.setKey(AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getKey());
                accountTypeRsp1.setValue(AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getValue());
                list.add(accountTypeRsp1);
            }
        }
        return list;
    }

    public List<BalanceTypeRsp> getBalanceBizType() {
        List<BalanceTypeRsp> list = new ArrayList<>();
        for (AccountBillBizTypeEnum levelEnum : AccountBillBizTypeEnum.values()) {
            if (AccountBillBizTypeEnum.分成退款.getCode().equals(levelEnum.getCode())
                    || AccountBillBizTypeEnum.商家推广.getCode().equals(levelEnum.getCode())) {
                continue;
            }
            BalanceTypeRsp balanceTypeRsp = new BalanceTypeRsp();
            balanceTypeRsp.setKey(levelEnum.getCode());
            balanceTypeRsp.setValue(levelEnum.getDesc());
            list.add(balanceTypeRsp);
        }
        return list;
    }

    public List<AccountStatusRsp> getAccountStatus() {
        List<AccountStatusRsp> list = new ArrayList<>();
        for (AccountStatusEnum levelEnum : AccountStatusEnum.values()) {
            AccountStatusRsp accountStatusRsp = new AccountStatusRsp();
            accountStatusRsp.setKey(levelEnum.getKey());
            accountStatusRsp.setValue(levelEnum.getValue());
            list.add(accountStatusRsp);
        }
        return list;
    }

    public List<AccountTypeRsp> getBillAccountType(CurrentUserReq req) {
        List<AccountTypeRsp> list = new ArrayList<>();
        AccountTypeRsp shareAccount = new AccountTypeRsp();
        shareAccount.setKey(AccountTypeEnum.SHARE_ACCOUNT.getKey());
        shareAccount.setValue(AccountTypeEnum.SHARE_ACCOUNT.getValue());
        list.add(shareAccount);
        if (Objects.equals(SettleSubjectTypeEnum.XIAODIAN.getCode(), req.getSettleSubjectType())) {
            AccountTypeRsp pinganAccount = new AccountTypeRsp();
            pinganAccount.setKey(AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getKey());
            pinganAccount.setValue(AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getValue());
            list.add(pinganAccount);

            AccountTypeRsp iotPinganAccount = new AccountTypeRsp();
            iotPinganAccount.setKey(AccountTypeEnum.IOT_PING_AN_SHARE_ACCOUNT.getKey());
            iotPinganAccount.setValue(AccountTypeEnum.IOT_PING_AN_SHARE_ACCOUNT.getValue());
            list.add(iotPinganAccount);
        }
        return list;
    }
}
