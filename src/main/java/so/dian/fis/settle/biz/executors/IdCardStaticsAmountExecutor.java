package so.dian.fis.settle.biz.executors;

import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.fis.settle.client.pojo.request.IdCardMonthQuotaQueryDTO;
import so.dian.fis.settle.client.pojo.response.IdCardMonthQuotaDTO;
import so.dian.fis.settle.config.BizConfig;
import so.dian.fis.settle.facade.PapIdCardStaticsFacade;
import so.dian.fis.settle.facade.WithdrawIdCardStaticsFacade;
import so.dian.fis.settle.facade.WutaiFacade;
import so.dian.payapply.platform.dto.IdCardPayApplyAmountQueryDTO;
import so.dian.payapply.platform.dto.IdCardPayApplyAmountStaticsDTO;
import so.dian.withdraw.platform.dto.IdCardWithdrawAmountDTO;
import so.dian.withdraw.platform.query.IdCardWithdrawAmountQueryDTO;
import so.dian.wutai.client.pojo.dto.IdCardPaymentAmountDTO;
import so.dian.wutai.client.pojo.dto.IdCardPaymentAmountQueryDTO;

/**
 * IdCardStaticsAmountExecutor
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:19
 */
@Slf4j
@Component
public class IdCardStaticsAmountExecutor implements Executor<IdCardMonthQuotaQueryDTO, IdCardMonthQuotaDTO> {

    @Autowired
    private WutaiFacade wutaiIdCardStaticsFacade;

    @Autowired
    private PapIdCardStaticsFacade papIdCardStaticsFacade;

    @Autowired
    private WithdrawIdCardStaticsFacade withdrawIdCardStaticsFacade;

    @Autowired
    private BizConfig bizConfig;

    @Override
    public IdCardMonthQuotaDTO execute(IdCardMonthQuotaQueryDTO idCardMonthQuotaQueryDTO) {

        Long totalQuota = bizConfig.getTotalQuota();

        IdCardPaymentAmountQueryDTO paymentQuery = new IdCardPaymentAmountQueryDTO();
        paymentQuery.setIdCard(idCardMonthQuotaQueryDTO.getIdCard());
        IdCardPaymentAmountDTO paymentAmountDTO = wutaiIdCardStaticsFacade.staticsMonthAmount(paymentQuery);

        if (Objects.nonNull(paymentAmountDTO)) {
            totalQuota -= paymentAmountDTO.getPaymentAmount();
        }
        IdCardWithdrawAmountQueryDTO idCardWithdrawAmountQueryDTO = new IdCardWithdrawAmountQueryDTO();
        idCardWithdrawAmountQueryDTO.setIdCard(idCardMonthQuotaQueryDTO.getIdCard());

        IdCardWithdrawAmountDTO idCardWithdrawAmountDTO = withdrawIdCardStaticsFacade.staticsMonthAmount(
                idCardWithdrawAmountQueryDTO);
        if (Objects.nonNull(idCardWithdrawAmountDTO)) {
            totalQuota -= idCardWithdrawAmountDTO.getWithdrawAmount();
        }

        IdCardPayApplyAmountQueryDTO idCardPayApplyAmountQueryDTO = new IdCardPayApplyAmountQueryDTO();
        idCardPayApplyAmountQueryDTO.setIdCard(idCardMonthQuotaQueryDTO.getIdCard());

        IdCardPayApplyAmountStaticsDTO idCardPayApplyAmountStaticsDTO = papIdCardStaticsFacade.staticsMonth(
                idCardPayApplyAmountQueryDTO);
        if (Objects.nonNull(idCardPayApplyAmountStaticsDTO)) {
            totalQuota -= idCardPayApplyAmountStaticsDTO.getMonthAmount();
        }

        if (totalQuota <= 0) {
            return IdCardMonthQuotaDTO.zero(idCardMonthQuotaQueryDTO.getIdCard());
        }

        return IdCardMonthQuotaDTO.of(totalQuota, idCardMonthQuotaQueryDTO.getIdCard());
    }
}
