package so.dian.fis.settle.biz.manager.remote;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.pojo.dto.TenantUserAdminAgentDTO;
import so.dian.fis.settle.facade.SunReaverFacade;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.sunreaver.api.resp.TenantUserAdminAgentResp;
import so.dian.sunreaver.common.PermissionDomainTypeEnum;
import so.dian.sunreaver.common.TenantAdminTypeEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SunReaverManager {
    @Autowired
    private SunReaverFacade sunReaverFacade;

    public List<String> intersectionAgentIds(List<String> mainBizIds, CurrentUserReq currentUserReq){
        List<Long> agentIds = getAgentIds(currentUserReq.getUserId());
        if (CollectionUtils.isEmpty(agentIds)){
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限");
        }
        List<String> agentStringIds = agentIds.stream().map(String::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainBizIds)){
            return agentStringIds;
        }
        return mainBizIds.stream().filter(agentStringIds::contains).collect(Collectors.toList());
    }

    public List<Long> getAgentIds(Long userId) {
        log.info("PermissionsCommon getAgentIds get userId:{}", userId);
        TenantUserAdminAgentDTO tenantUserAdminAgentDTO = new TenantUserAdminAgentDTO();
        tenantUserAdminAgentDTO.setUserId(userId);
        tenantUserAdminAgentDTO.setDomain(PermissionDomainTypeEnum.FINANCIAL.getCode());
        tenantUserAdminAgentDTO.setAdminTypes(Arrays.asList(TenantAdminTypeEnum.TENANT_SUPER_ADMINISTRATOR.getCode(),TenantAdminTypeEnum.TENANT_ADMINISTRATOR.getCode()));
        TenantUserAdminAgentResp tenantUserAdminAgent = sunReaverFacade.getTenantUserAdminAgent(tenantUserAdminAgentDTO);
        if (Objects.isNull(tenantUserAdminAgent) || tenantUserAdminAgent.getTenantAdminTypeMap().isEmpty()) {
            return null;
        }
        return new ArrayList<>(tenantUserAdminAgent.getTenantAdminTypeMap().keySet());
    }
}
