package so.dian.fis.settle.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.agent.api.constant.AgentBaseEnum;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.common.logger.Log;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.handler.BillHandler;
import so.dian.fis.settle.biz.handler.ExcelHandler;
import so.dian.fis.settle.biz.manager.DingTalkManager;
import so.dian.fis.settle.biz.manager.LeoManager;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.biz.manager.remote.YandangManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.constant.LoggerConstants;
import so.dian.fis.settle.common.enums.error.BizErrorCodeEnum;
import so.dian.fis.settle.common.exception.BillErrorCodeEnum;
import so.dian.fis.settle.common.exception.CommonErrorCodeEnum;
import so.dian.fis.settle.common.pojo.converter.BillConverter;
import so.dian.fis.settle.common.pojo.converter.CommonConverter;
import so.dian.fis.settle.common.pojo.validator.BillMerchantTypedator;
import so.dian.fis.settle.common.pojo.validator.BillValidator;
import so.dian.fis.settle.common.util.Assert;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.request.AddBillPcReq;
import so.dian.fis.settle.controller.account.request.AddBillReq;
import so.dian.fis.settle.controller.account.request.ApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.BatchAddManualBillReq;
import so.dian.fis.settle.controller.account.request.BatchApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.ConfirmBillPcReq;
import so.dian.fis.settle.controller.account.request.DiscardBillPcReq;
import so.dian.fis.settle.controller.account.request.EditBillPcReq;
import so.dian.fis.settle.controller.account.request.LvyBillShopPcReq;
import so.dian.fis.settle.controller.account.request.PageBillItemReq;
import so.dian.fis.settle.controller.account.request.PcBillListReq;
import so.dian.fis.settle.controller.account.request.SettleBillAgainPcReq;
import so.dian.fis.settle.controller.account.request.SettleListExportPcReq;
import so.dian.fis.settle.controller.account.response.BillDetailPcRsp;
import so.dian.fis.settle.controller.account.response.BillInfoRsp;
import so.dian.fis.settle.controller.account.response.BillItemNoListRsp;
import so.dian.fis.settle.controller.account.response.BillItemPcRsp;
import so.dian.fis.settle.controller.account.response.BillItemShopRsp;
import so.dian.fis.settle.controller.account.response.PcBillListRsp;
import so.dian.fis.settle.facade.ContractExplainFacade;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.lvy.pojo.dto.AccountBillItemDTO;
import so.dian.lvy.pojo.dto.AccountBillItemSourceDTO;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.dto.BillDetailDTO;
import so.dian.lvy.pojo.dto.PayApplyDTO;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;
import so.dian.lvy.pojo.enums.BalanceTypeEnum;
import so.dian.lvy.pojo.enums.BillPeriodTypeEnum;
import so.dian.lvy.pojo.enums.BillRelateAccountTypeEnum;
import so.dian.lvy.pojo.enums.DecisionStatusEnum;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;
import so.dian.lvy.pojo.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.param.AddBillParam;
import so.dian.lvy.pojo.param.BatchBillManualParam;
import so.dian.lvy.pojo.param.BillParam;
import so.dian.lvy.pojo.query.BillListQuery;
import so.dian.rick.merchant.client.merchant.req.GetMerchantAccountReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;
import so.dian.yandang.client.pojo.enums.BizTypeEnum;
import so.dian.yandang.client.pojo.enums.PayBillStatusEnum;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * BillService
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BillService {

    @Autowired
    DingTalkManager dingTalkManager;
    @Autowired
    YandangManager yandangManager;
    @Autowired
    LeoManager leoManager;
    @Resource
    private LvyManager lvyManager;
    @Resource
    private BillHandler billHandler;

    @Autowired
    private AgentManager agentManager;
    @Resource
    private BillWhiteListService billWhiteListService;

    @Autowired
    private ContractExplainFacade contractExplainFacade;
    @Autowired
    @Qualifier(value = "exportSettlementExecutor")
    private ThreadPoolExecutor exportSettlementExecutor;
    @Autowired
    private ExcelHandler excelHandler;

    @Resource
    private RickMerchantManager rickMerchantManager;

    /**
     * 最大导入数量限制
     */
    private Integer MAX_LIMIT_1000 = 1000;
    @Value("#{'${approve.roleList}'.split(',')}")
    private List<String> allowApproveRoleList;
    //    @Value("${approve.userList}")
    @Value("#{'${approve.userList}'.split(',')}")
    private List<Long> allowApproveUserList;

    public BizResult<List<PcBillListRsp>> listBillPc(CurrentUserReq userReq, PcBillListReq listReq) {
        BillListQuery listQuery = billHandler.buildBillListQuery(userReq, listReq);
        if (Objects.isNull(listQuery)) {
            return BizResult.create(Lists.newArrayList());
        }
        List<BillDTO> queryBillList = lvyManager.pageBillList(listQuery);
        return BizResult.create(billHandler.buildPcBillListRspList(queryBillList));
    }


    /**
     *  结算导出
     * @param userReq
     * @param settleListExportPcReq
     * @return
     */
    public BizResult<String> settleListExportPc(CurrentUserReq userReq,SettleListExportPcReq settleListExportPcReq){
        log.info("结算导出userReq:{},settleListExportPcReq:{}",userReq,settleListExportPcReq);
        if (Objects.isNull(userReq)|| StringUtils.isBlank(userReq.getUserMail())) {
            throw BizException.create(BillErrorCodeEnum.SETTLEMENT_EXPORT_ERROR, "用户邮箱不存在，请先联系HR添加邮箱！");
        }
        //
        PcBillListReq req = new PcBillListReq();
        BeanUtils.copyProperties(settleListExportPcReq,req);
        BillListQuery billListQuery = billHandler.buildBillListQuery(userReq, req);
        billListQuery.setNeedCount(true);
        // 获取结算总量
        int count = lvyManager.getBillTotalCount(billListQuery);
        log.info("获取count总量：{}",count);
        if (count>10000){
            throw BizException.create(BillErrorCodeEnum.SETTLEMENT_EXPORT_ERROR, "单次导出最多1万条，请调整筛选条件后重新导出。");
        }
        // excel导出
        exportSettlementExecutor.execute(() -> excelHandler.SettlementExport(billListQuery,userReq,count));
        return BizResult.create("下载任务已创建，请稍后前往邮箱"+userReq.getUserMail()+"查看，若超过10分钟未收到文件，请重新下载");
    }

    public BizResult<Long> statisticsForPc(CurrentUserReq userReq, PcBillListReq listReq) {
        BillListQuery listQuery = billHandler.buildBillListQuery(userReq, listReq);
        if (Objects.isNull(listQuery)) {
            return BizResult.create(0L);
        }
        Long amount = lvyManager.getBillStatistics(listQuery);
        return BizResult.create(amount);
    }

    public BizResult<String> addBillForPc(CurrentUserReq userReq, AddBillPcReq addBillPcReq) {
        log.info("addBillForPc, user:{}, addReq:{}", userReq, JSONObject.toJSONString(addBillPcReq));
        if (addBillPcReq.getAccountType() == null) {
            addBillPcReq.setAccountType(AccountTypeEnum.SHARE_ACCOUNT.getKey());
        }
        GetMerchantAccountReq merchantAccount = new GetMerchantAccountReq();
        merchantAccount.setMerchantId(String.valueOf(addBillPcReq.getBillingMainBizId()));
        merchantAccount.setMerchantType(addBillPcReq.getBillingMainBizType());
        merchantAccount.setAccountDimensionFactor1(userReq.getSettleSubjectType());
        merchantAccount.setAccountDimensionFactor3(addBillPcReq.getSettleSubjectId().toString());
        merchantAccount.setAccountType(addBillPcReq.getAccountType());
        MerchantAccountResp account = rickMerchantManager.getAccount(merchantAccount);
        if (account == null) {
            throw BizException.create(BillErrorCodeEnum.ACCOUNT_NOT_FOUND, "账户信息不存在请核对后重新提交");
        }

        if (StringUtils.isNotBlank(addBillPcReq.getComment()) && addBillPcReq.getComment().length() > 200) {
            throw BizException.create(BillErrorCodeEnum.LIMIT_EXCEEDED, "备注含标点符号最多可输入200个字符");
        }
        checkOperateSupport(userReq, addBillPcReq.getBalanceType());
        if (Objects.equals(addBillPcReq.getBizType(), AccountBillBizTypeEnum.代付代理商商户分成.getCode()) && !Objects.equals(addBillPcReq.getBillingMainBizType(), MainBizTypeEnum.AGENT.getCode())) {
            throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_SECOND);
        }

        if(!BillValidator.validAgentDirectBizType(addBillPcReq.getBizType(),addBillPcReq.getBillingMainBizType())){
            throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_BIZ_TYPE);
        }
        // 判断业务类型是否允许创建
        if (BillMerchantTypedator.isValidManualBillBizType(addBillPcReq.getBillingMainBizType())) {
            if (!BillValidator.isValidManualBillBizType(addBillPcReq.getBizType())) {
                throw BizException.create(BillErrorCodeEnum.MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT);
            }
        } else {
            if (!BillValidator.isOldValidManualBillBizType(addBillPcReq.getBizType())) {
                throw BizException.create(BillErrorCodeEnum.OLD_MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT);
            }
        }


        return BizResult
                .create(lvyManager.addManualBillReturnStr(billHandler.buildAddBillParam(userReq, addBillPcReq)));
    }
    public BizResult<String> editBillForPc(CurrentUserReq userReq, EditBillPcReq editBillPcReq) {
        log.info("editBillForPc, user:{}, editBillPcReq:{}", userReq, JSONObject.toJSONString(editBillPcReq));
        GetMerchantAccountReq merchantAccount = new GetMerchantAccountReq();
        merchantAccount.setMerchantId(String.valueOf(editBillPcReq.getBillingMainBizId()));
        merchantAccount.setMerchantType(editBillPcReq.getBillingMainBizType());
        merchantAccount.setAccountDimensionFactor1(userReq.getSettleSubjectType());
        merchantAccount.setAccountDimensionFactor3(editBillPcReq.getSettleSubjectId().toString());
        merchantAccount.setAccountType(editBillPcReq.getAccountType());
        MerchantAccountResp account = rickMerchantManager.getAccount(merchantAccount);
        if (account == null) {
            throw BizException.create(BillErrorCodeEnum.ACCOUNT_NOT_FOUND, "账户信息不存在请核对后重新提交");
        }
        if (Objects.equals(editBillPcReq.getBizType(), AccountBillBizTypeEnum.代付代理商商户分成.getCode()) && !Objects.equals(editBillPcReq.getBillingMainBizType(), MainBizTypeEnum.AGENT.getCode())) {
            throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_SECOND);
        }

        BillDetailDTO billDetail = lvyManager.getBillDetail(editBillPcReq.getBillNo(), userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        if (this.isDividedPABill(billDetail)) {
            throw BizException.create(BillErrorCodeEnum.PING_AN_NOT_ALLOW,
                    String.format(BillErrorCodeEnum.PING_AN_NOT_ALLOW.getDesc(), "编辑"));
        }

        if (StringUtils.isNotBlank(editBillPcReq.getComment()) && editBillPcReq.getComment().length() > 200) {
            throw BizException.create(BillErrorCodeEnum.LIMIT_EXCEEDED, "修改原因含标点符号最多可输入200个字符");
        }
        if(!BillValidator.validAgentDirectBizType(editBillPcReq.getBizType(),editBillPcReq.getBillingMainBizType())){
            throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_BIZ_TYPE);
        }
        // 判断业务类型是否允许编辑
        if (BillMerchantTypedator.isValidManualBillBizType(editBillPcReq.getBillingMainBizType())) {
            if (!BillValidator.isValidManualBillBizType(editBillPcReq.getBizType())) {
                throw BizException.create(BillErrorCodeEnum.MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT);
            }
        } else {
            if (!BillValidator.isOldValidManualBillBizType(editBillPcReq.getBizType())) {
                throw BizException.create(BillErrorCodeEnum.OLD_MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT);
            }
        }
        checkOperateSupport(userReq, editBillPcReq.getBalanceType());
        return BizResult.create(lvyManager
                .editBill(editBillPcReq.getBillNo(), billHandler.buildEditBillParam(userReq, editBillPcReq)));
    }

    public BizResult<String> discardBillForPc(CurrentUserReq userReq, DiscardBillPcReq discardBillPcReq) {
        log.info("discardBillForPc, user:{}, discardReq:{}", userReq, JSONObject.toJSONString(discardBillPcReq));
        BillDetailDTO billDetail = lvyManager.getBillDetail(discardBillPcReq.getBillNo(), userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        if (this.isDividedPABill(billDetail)) {
            throw BizException.create(BillErrorCodeEnum.PING_AN_NOT_ALLOW,
                    String.format(BillErrorCodeEnum.PING_AN_NOT_ALLOW.getDesc(), "作废"));
        }
        return BizResult.create(lvyManager.discardBill(billHandler.buildDiscardBillParam(userReq, discardBillPcReq)));
    }

    private boolean isAllowApprove(CurrentUserReq userReq) {
        if (allowApproveRoleList.contains(userReq.getCurrentRole())) {
            return true;
        }
        if (allowApproveUserList.contains(userReq.getUserId())) {
            return true;
        }
        return false;
    }

    public BizResult<Boolean> batchApprovalBillForPc(CurrentUserReq userReq,
                                                     BatchApprovalBillPcReq batchApprovalBillPcReq) {
        log.info("batchApprovalBillForPc, user:{}, approvalReq:{}", userReq, batchApprovalBillPcReq);
        if (!isAllowApprove(userReq)) {
            log.info("batchApprovalBillForPc, 不允许审批 user:{}, approvalReq:{}", userReq, batchApprovalBillPcReq);
            throw BizException.create(BillErrorCodeEnum.NOT_ALLOW_APPROVE);
        }
        List<String> billNos = batchApprovalBillPcReq.getBillNos();
        for (String billNo : billNos) {
            BillDetailDTO billDetail = lvyManager.getBillDetail(billNo, userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
            if (Objects.isNull(billDetail)) {
                continue;
            }
            if (this.isDividedPABill(billDetail) && DecisionStatusEnum.REJECT.getCode().equals(batchApprovalBillPcReq.getDecision())) {
                throw BizException.create(BillErrorCodeEnum.PING_AN_NOT_ALLOW,
                        String.format(BillErrorCodeEnum.PING_AN_NOT_ALLOW.getDesc(), "批量审批拒绝"));
            }
            checkOperateSupport(userReq, billDetail.getBalanceType());
        }
        return BizResult.create(lvyManager
                .batchApprovalBill(billHandler.buildBillBatchApprovalParam(userReq, batchApprovalBillPcReq)));
    }

    public BizResult<String> approvalBillForPc(CurrentUserReq userReq, ApprovalBillPcReq approvalBillPcReq) {
        log.info("batchApprovalBillForPc, user:{}, approvalReq:{}", userReq, approvalBillPcReq);
        if (!isAllowApprove(userReq)) {
            log.info("batchApprovalBillForPc, 不允许审批 user:{}, approvalReq:{}", userReq, approvalBillPcReq);
            throw BizException.create(BillErrorCodeEnum.NOT_ALLOW_APPROVE);
        }
        BillDetailDTO billDetail = lvyManager.getBillDetail(approvalBillPcReq.getBillNo(), userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        if (this.isDividedPABill(billDetail) && DecisionStatusEnum.REJECT.getCode().equals(approvalBillPcReq.getDecision())) {
            throw BizException.create(BillErrorCodeEnum.PING_AN_NOT_ALLOW,
                    String.format(BillErrorCodeEnum.PING_AN_NOT_ALLOW.getDesc(), "审批拒绝"));
        }
        checkOperateSupport(userReq, billDetail.getBalanceType());
        return BizResult
                .create(lvyManager.approvalBill(billHandler.buildApprovalBillParam(userReq, approvalBillPcReq)));

    }

    public BizResult<String> confirmBillForPc(CurrentUserReq userReq, ConfirmBillPcReq confirmBillPcReq) {
        log.info("confirmBillForPc, user:{}, confirmReq:{}", userReq, confirmBillPcReq);
        BillDetailDTO billDetail = lvyManager.getBillDetail(confirmBillPcReq.getBillNo(), userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        checkOperateSupport(userReq, billDetail.getBalanceType());
        return BizResult.create(lvyManager.confirmBill(billHandler.buildConfirmBillParam(userReq, confirmBillPcReq)));
    }

    public BizResult<String> settleBillAgainForPc(CurrentUserReq userReq, SettleBillAgainPcReq settleBillAgainPcReq) {
        log.info("settleBillAgainForPc, user:{}, settleBillAgainPcReq:{}", userReq, settleBillAgainPcReq);
        BillDetailDTO billDetail = lvyManager.getBillDetail(settleBillAgainPcReq.getBillNo(), userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        checkOperateSupport(userReq, billDetail.getBalanceType());
        return BizResult.create(lvyManager
                .settleBillAgain(billHandler.buildSettleBillAgainParam(userReq, settleBillAgainPcReq.getBillNo())));
    }

    public BizResult<BillInfoRsp> getBillInfo(CurrentUserReq userReq, String billNo) {
        if (StringUtils.isBlank(billNo)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        PageResult<AccountBillItemDTO> billItemPageResult = lvyManager
                .pageBillItem(billHandler.buildBillItemListQuery(userReq, billNo));
        List<AccountBillItemDTO> billItemDTOS = billItemPageResult.getData();
        if (billItemPageResult == null || !billItemPageResult.isSuccess() || billItemDTOS == null) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        // 该账单关联多家门店数据，不支持编辑
        if (billItemDTOS.size() > 1) {
            throw BizException.create(BillErrorCodeEnum.NOT_EDIT);
        }
        BillDTO billDTO = lvyManager.getBillInfo(billNo, userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        return BizResult.create(billHandler.buildBillInfoRsp(billDTO, billItemDTOS.size() == 1 ? billItemDTOS.get(0) : null));
    }

    public BizResult<BillDetailPcRsp> getBillDetailForPc(CurrentUserReq userReq, String billNo) {
        BillDetailDTO billDetailDTO = lvyManager
                .getBillDetail(billNo, userReq.getSettleSubjectType(), userReq.getSettleSubjectId());
        if (Objects.isNull(billDetailDTO)) {
            throw BizException.create(CommonErrorCodeEnum.RECORD_NOT_EXISTED, "账单不存在");
        }
        return BizResult.create(billHandler.buildBillDetail(billDetailDTO));
    }

    public BizResult<PageData<BillItemPcRsp>> pageBillItem(CurrentUserReq userReq, PageBillItemReq pageBillItemReq) {
        PageResult<AccountBillItemDTO> billItemPageResult = lvyManager
                .pageBillItem(billHandler.buildBillItemQuery(userReq, pageBillItemReq));
        return CommonConverter.convertPageResultToBizResult(billItemPageResult,
                billHandler.buildBillItemPcRsp(billItemPageResult.getData()));
    }

    public BizResult<Long> statisticsBillItems(CurrentUserReq userReq, PageBillItemReq pageBillItemReq) {
        return BizResult
                .create(lvyManager.statisticsBillItems(billHandler.buildBillItemQuery(userReq, pageBillItemReq)));
    }

    public BizResult<BillItemNoListRsp> getBillItemNos(CurrentUserReq userReq, PageBillItemReq pageBillItemReq) {
        PageResult<String> billItemNoPageResult = lvyManager
                .getBillItemNos(billHandler.buildBillItemQuery(userReq, pageBillItemReq));
        return BizResult.create(billHandler.buildBillItemNoListRsp(billItemNoPageResult));
    }

    public BizResult<PageData<BillItemShopRsp>> getBillShops(CurrentUserReq userReq,
                                                             LvyBillShopPcReq lvyBillShopPcReq) {
        List<AccountBillItemSourceDTO> billItemSourceDTOList = lvyManager
                .getBillShops(lvyBillShopPcReq.getBillNo(), lvyBillShopPcReq.getShopIds());
        return BizResult.create(billHandler.buildBillItemShopPage(billItemSourceDTOList));
    }

    public BizResult<Integer> batchAddManualBill(CurrentUserReq userReq,
                                                 BatchAddManualBillReq batchAddManualBillReq) {
        log.info("batchAddManualBill, user:{}, addReq:{}", userReq, JSONObject.toJSONString(batchAddManualBillReq));
        // 指定人可以操作
        boolean hasPermission = billWhiteListService.checkHasPermission(userReq.getUserName());
        if (!hasPermission) {
            throw BizException.create(BizErrorCodeEnum.USER_UNAUTHORIZED);
        }
        List<AddBillReq> addBillReqList = batchAddManualBillReq.getAddBillReqList();
        if (addBillReqList.size() > MAX_LIMIT_1000) {
            throw BizException.create(BizErrorCodeEnum.MAX_LIMIT_1000);
        }
        // 校验业务逻辑，判断业务类型是否允许创建
        addBillReqList.forEach(o -> {

            checkOperateSupport(userReq, o.getBalanceType());

            GetMerchantAccountReq merchantAccount = new GetMerchantAccountReq();
            merchantAccount.setMerchantId(String.valueOf(o.getBillingMainBizId()));
            merchantAccount.setMerchantType(o.getBillingMainBizType());
            merchantAccount.setAccountDimensionFactor1(o.getSettleSubjectType());
            merchantAccount.setAccountDimensionFactor3(o.getSettleSubjectId().toString());
            merchantAccount.setAccountType(AccountTypeEnum.fromBillRelateAccountTypeCode(o.getBillAccountType()).getKey());
            MerchantAccountResp account = rickMerchantManager.getAccount(merchantAccount);
            if (account == null) {
                throw BizException.create(BillErrorCodeEnum.ACCOUNT_NOT_FOUND, "序号: " + o.getSeq() + "," + "账户信息不存在请核对后重新提交");
            }

            if(!BillValidator.validAgentDirectBizType(o.getBizType(),o.getBillingMainBizType())){
                throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_BIZ_TYPE, "序号: " + o.getSeq() + "," + BillErrorCodeEnum.NOT_SUPPORT_BIZ_TYPE.getDesc());
            }
            if (StringUtils.isNotBlank(o.getComment()) && o.getComment().length() > 200) {
                throw BizException.create(BillErrorCodeEnum.LIMIT_EXCEEDED, "序号: " + o.getSeq() + "," + "备注含标点符号最多可输入200个字符");
            }

            if (BillMerchantTypedator.isValidManualBillBizType(o.getBillingMainBizType())) {
                if (Objects.equals(o.getBizType(), AccountBillBizTypeEnum.代付代理商商户分成.getCode()) && !Objects.equals(o.getBillingMainBizType(), MainBizTypeEnum.AGENT.getCode())) {
                    throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_SECOND, "序号: " + o.getSeq() + ",结算主体类型除代理商，不支持该业务类型");
                }
                if (!BillValidator.isValidManualBillBizType(o.getBizType())) {
                    throw BizException.create(BillErrorCodeEnum.MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT, "序号：" + o.getSeq() + ",手工账单不支持该业务类型");
                }
            } else {
                if (Objects.equals(o.getBizType(), AccountBillBizTypeEnum.代付代理商商户分成.getCode()) && !Objects.equals(o.getBillingMainBizType(), MainBizTypeEnum.AGENT.getCode())) {
                    throw BizException.create(BillErrorCodeEnum.NOT_SUPPORT_SECOND, "序号: " + o.getSeq() + ",结算主体类型除代理商，不支持该业务类型");
                }
                if (!BillValidator.isOldValidManualBillBizType(o.getBizType())) {
                    throw BizException.create(BillErrorCodeEnum.OLD_MANUAL_BILL_BIZ_TYPE_NOT_SUPPORT, "序号：" + o.getSeq() + ",结算主体类型除代理商、合资公司、运营型服务商，不支持该业务类型");
                }
            }

        });

        BatchBillManualParam batchBillManualParam = BillConverter.buildLvyBatchBillManualParam(userReq, addBillReqList);
        BizResult<Integer> bizResult = lvyManager.addManualBillBatch(batchBillManualParam);
        return bizResult;
    }

    private void checkOperateSupport(CurrentUserReq req, Integer balanceType) {
        log.info("checkOperateSupport get balanceType:{}",balanceType);
        if (Objects.equals(balanceType, BalanceTypeEnum.BALANCE_IN_TYPE.getCode())) {
            this.checkBalanceInType(req, balanceType);
        }else if (Objects.equals(balanceType, BalanceTypeEnum.BALANCE_OUT_TYPE.getCode())) {
            this.checkBalanceOutType(req, balanceType);
        }
    }

    private void checkBalanceOutType(CurrentUserReq req, Integer balanceType) {
        log.info("checkBalanceOutType get balanceType:{}",balanceType);
        Long settleSubjectId = req.getSettleSubjectId();
        Integer settleSubjectType = req.getSettleSubjectType();
        if (!Objects.equals(balanceType, BalanceTypeEnum.BALANCE_OUT_TYPE.getCode())) {
            return;
        }
        if (!Objects.equals(SettleSubjectTypeEnum.代理商.getCode(), settleSubjectType)) {
            return;
        }

        AgentDTO agent = agentManager.findById(settleSubjectId);
        // 若提交人所属公司为走代付模式的代理商,走代付模式的代理商包括：一级个人代理商,
        // 收支类型为“支出”类型的账单时，需要进行报错“不支持操作支出类账单”
        if (Objects.equals(agent.getHierarchy(), AgentBaseEnum.AgentHierarchyEnum.ONE_HIERARCHY.getCode())
             && Objects.equals(agent.getSubType(), AgentBaseEnum.AgentSubTypeEnum.PERSONAL.getId())) {
            throw BizException.create(BillErrorCodeEnum.AGENT_NOT_SUPPORT_OUT_PROXY_PAY);
        }

    }

    private void checkBalanceInType(CurrentUserReq req, Integer balanceType) {
        log.info("checkOperateSupport get balanceType:{}",balanceType);
        Long settleSubjectId = req.getSettleSubjectId();
        Integer settleSubjectType = req.getSettleSubjectType();
        if (!Objects.equals(balanceType, BalanceTypeEnum.BALANCE_IN_TYPE.getCode())) {
            return;
        }
        if (!Objects.equals(SettleSubjectTypeEnum.代理商.getCode(), settleSubjectType)) {
            return;
        }

        AgentDTO agent = agentManager.findById(settleSubjectId);

        if (Objects.equals(agent.getHierarchy(), AgentBaseEnum.AgentHierarchyEnum.TWO_HIERARCHY.getCode())) {
            Boolean proxyPay = contractExplainFacade.isProxyPay(agent.getAgentId());
            if (proxyPay) {
                throw BizException.create(BillErrorCodeEnum.AGENT_NOT_SUPPORT_PROXY_PAY);
            }
        } else if (Objects.equals(agent.getBizTypeEnum(), AgentBaseEnum.AgentBizTypeEnum.PERSONAL_AGENT_TYPE.getId())) {
            throw BizException.create(BillErrorCodeEnum.AGENT_NOT_SUPPORT_PROXY_PAY);
        }

    }


    /**
     * 分成预付业务 ，打款成功，扣减资金流水
     */
    public void addBill(String billNo) {
        Log.info(LoggerConstants.BIZ, "处理付款单号:{}", billNo);
        PayBillInfoRsp payBillInfoRsp = yandangManager.getPayBillInfo(billNo);

        if (Objects.isNull(payBillInfoRsp)) {
            Log.info(LoggerConstants.BIZ, "付款单不存在:{}", billNo);
            return;
        }
        Integer bizType = payBillInfoRsp.getBizType();
        if (!BizTypeEnum.ADVANCE_PAYMENT.getCode().equals(bizType)) {
            Log.info(LoggerConstants.BIZ, "业务类型非分成预付:{}", billNo);
            return;
        }
        Integer tradeState = payBillInfoRsp.getTradeState();
        if (!PayBillStatusEnum.PAY_SUCCESS.getCode().equals(tradeState)) {
            Log.info(LoggerConstants.BIZ, "打款结果非成功:{}", billNo);
            return;
        }
        PayApplyDTO payApplyDTO = lvyManager.getPayApplyByApplyNo(payBillInfoRsp.getOutBizNo());
        if (Objects.isNull(payApplyDTO)) {
            dingTalkManager.sendWarnMsg("分成预付资金扣减失败", "付款单申请款不存在," + billNo);
            Log.info(LoggerConstants.BIZ, "付款申请单不存在:{}", billNo);
            return;
        }
        //获取付款单的税前金额
        Long beforeTaxAmount = leoManager.getpayPlanBeforeTaxAmount(payBillInfoRsp.getOutBizNo());
        if (Objects.isNull(beforeTaxAmount)) {
            dingTalkManager.sendWarnMsg("查询付款申请单税前金额失败", "查询付款申请单税前金额失败," + billNo);
            Assert.notNull(beforeTaxAmount);
        }
        Integer objectType = payApplyDTO.getBizType();
        AddBillParam addBillParam = new AddBillParam();
        addBillParam.setUserId(0L);
        addBillParam.setUserName("system");
        BillParam billParam = new BillParam();
        billParam.setAmount(beforeTaxAmount);
        billParam.setBalanceType(BalanceTypeEnum.BALANCE_OUT_TYPE.getCode());
        billParam.setSettleSubjectId(payBillInfoRsp.getSettleSubjectId());
        billParam.setSettleSubjectType(payBillInfoRsp.getSettleSubjectType());
        billParam.setOutBizNo(payBillInfoRsp.getBillNo());
        billParam.setNeedCheck(false);
        billParam.setBillingMainBizId(payBillInfoRsp.getObjectId());
        billParam.setBillingMainBizType(objectType);
        billParam.setBillName("预付分成");
        billParam.setPeriodType(BillPeriodTypeEnum.DAILY.getCode());
        Long payFinishTime = System.currentTimeMillis();
        if (Objects.nonNull(payBillInfoRsp.getPayFinishTime())) {
            payFinishTime = payBillInfoRsp.getPayFinishTime();
        }
        Date billPeriod = new Date(payFinishTime);
        billParam.setPeriodStart(billPeriod);
        billParam.setPeriodEnd(billPeriod);
        billParam.setBizType(AccountBillBizTypeEnum.分成预付.getCode());
        addBillParam.setBillParam(billParam);
        lvyManager.addBill(addBillParam);
    }

    /**
     * 是否为平安前端分账的清分关联账单
     * @param billDetail
     * @return
     */
    private boolean isDividedPABill(BillDetailDTO billDetail) {
        if (billDetail == null) {
            return false;
        }

        Integer accountType = billDetail.getBillAccountType();
        boolean isPAFrontAccount = BillRelateAccountTypeEnum.P_A_FRONT_ACCOUNT.getCode().equals(accountType)
                || BillRelateAccountTypeEnum.IOT_P_A_FRONT_ACCOUNT.getCode().equals(accountType);

        return isPAFrontAccount && Boolean.TRUE.equals(billDetail.getDividedBill());
    }
}
