package so.dian.fis.settle.biz.handler;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.PageResult;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.entity.dto.ShopInfoDTO;
import so.dian.fis.settle.biz.manager.MainBizInfoManager;
import so.dian.fis.settle.biz.manager.remote.ShopCenterManager;
import so.dian.fis.settle.biz.manager.remote.ShopManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.exception.CommonErrorCodeEnum;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.request.*;
import so.dian.fis.settle.controller.account.response.*;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.kunlun.caifa.enums.SettleSubjectIdEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.dto.AccountBillItemDTO;
import so.dian.lvy.pojo.dto.AccountBillItemSourceDTO;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.dto.BillDetailDTO;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;
import so.dian.lvy.pojo.enums.BillGenerateTypeEnum;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;
import so.dian.lvy.pojo.param.*;
import so.dian.lvy.pojo.query.BillItemListQuery;
import so.dian.lvy.pojo.query.BillListQuery;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * BillHandler
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BillHandler {

    public static final Long NO_SENSE = 0L;
    @Resource
    private MainBizInfoManager mainBizInfoManager;
    @Resource
    private ShopManager shopManager;
    @Resource
    private ShopCenterManager shopCenterManager;

    public BillListQuery buildBillListQuery(CurrentUserReq userReq, PcBillListReq listReq) {
        BillListQuery result = new BillListQuery();
        if (!Objects.equals(userReq.getSettleSubjectType(), SettleSubjectTypeEnum.XIAODIAN.getCode())) {
            result.setSettleSubjectType(userReq.getSettleSubjectType());
            result.setSettleSubjectId(userReq.getSettleSubjectId());
        } else {
            result.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
            result.setSettleSubjectId(listReq.getSettleSubjectId());
        }

        if (Objects.isNull(listReq.getBillingMainBizType())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        result.setBillingMainBizType(listReq.getBillingMainBizType());
        result.setAgentDirectSign(Optional.ofNullable(listReq.getIsAgentDirectSign()).map(aBoolean -> aBoolean ? 1 : 0).orElse(null));

        // 设置mainBizId查询条件
        // 如果商户/渠道 名称条件不为空
        if (StringUtils.isNotBlank(listReq.getBillingMainBizName())) {
            // 获取商户/渠道id列表
            List<Long> mainBizIdList = mainBizInfoManager
                    .listMainBizIdByTypeAndName(listReq.getBillingMainBizType(), listReq.getBillingMainBizName());
            // 如果未获取到，结束
            if (CollectionUtils.isEmpty(mainBizIdList)) {
                return null;
            }
            List<Long> mainBizIdFromReq = listReq.getBillingMainBizIds();
            // 如果参数里没有带mainBizId信息，那么设置mainBizId查询条件为根据name查询出来的
            // 否则取两者交集
            if (CollectionUtils.isEmpty(mainBizIdFromReq)) {
                result.setBillingMainBizIds(mainBizIdList);
            } else {
                List<Long> mainBizIdIntersection = (List<Long>) CollectionUtils
                        .intersection(mainBizIdList, mainBizIdFromReq);
                if (CollectionUtils.isEmpty(mainBizIdIntersection)) {
                    return null;
                }
                result.setBillingMainBizIds(mainBizIdIntersection);
            }
        } else if (CollectionUtils.isNotEmpty(listReq.getBillingMainBizIds())) {
            // 没有传mainBizName条件，且传了mainBizId条件，那么用参数里的mainBizId作为查询条件
            result.setBillingMainBizIds(listReq.getBillingMainBizIds());
        }

        result.setId(listReq.getId());
        result.setBillNo(listReq.getBillNo());
        result.setBillName(listReq.getBillName());
        result.setPeriodType(listReq.getPeriodType());
        result.setPeriodStart(listReq.getPeriodStart());
        result.setPeriodEnd(listReq.getPeriodEnd());
        result.setGenType(listReq.getGenType());
        if (CollectionUtils.isNotEmpty(listReq.getBizTypeList())) {
            result.setBizTypeList(listReq.getBizTypeList().stream().map(Integer::valueOf).collect(Collectors.toList()));
        }
        result.setBalanceType(listReq.getBalanceType());
        if (CollectionUtils.isNotEmpty(listReq.getStatusList())) {
            result.setStatusList(listReq.getStatusList().stream().map(Integer::valueOf).collect(Collectors.toList()));
        }
        result.setAmountMin(listReq.getAmountMin());
        result.setAmountMax(listReq.getAmountMax());
        result.setCreateStart(listReq.getCreateStart());
        result.setCreateEnd(listReq.getCreateEnd());
        result.setPageNo(listReq.getPageNo());
        result.setPageSize(listReq.getPageSize());

        //转换下
        if (CollUtil.isNotEmpty(listReq.getBillAccountTypes())) {
            result.setBillAccountTypes(listReq.getBillAccountTypes()
                    .stream().map(AccountTypeEnum::toBillRelateAccountTypeCode)
                    .collect(Collectors.toList()));
        }
        return result;
    }

    public List<PcBillListRsp> buildPcBillListRspList(List<BillDTO> billDTOList) {
        if (CollectionUtils.isEmpty(billDTOList)) {
            return Lists.newArrayList();
        }
        List<Long> mainBizIdList = LocalListUtils.transferList(billDTOList, BillDTO::getBillingMainBizId);
        Integer mainBizType = billDTOList.get(0).getBillingMainBizType();
        Map<Long, String> mainBizIdNameMap = mainBizInfoManager.getIdNameMap(mainBizType, mainBizIdList);
        List<PcBillListRsp> result = Lists.newArrayList();
        for (BillDTO billDTO : billDTOList) {
            AccountBillBizTypeEnum bizTypeEnum = LocalEnumUtils.findByCode(AccountBillBizTypeEnum.class, billDTO.getBizType());
            if (Objects.isNull(bizTypeEnum)) {
                continue;
            }
            PcBillListRsp pcBillRsp = new PcBillListRsp();
            BeanUtils.copyProperties(billDTO, pcBillRsp);
            pcBillRsp.setBillingMainBizName(mainBizIdNameMap.get(billDTO.getBillingMainBizId()));
            pcBillRsp.setIsAgentDirectSign(Optional.ofNullable(billDTO.getAgentDirectSign()).map(integer -> Objects.equals(integer, 1)).orElse(false));
            result.add(pcBillRsp);
        }
        return result;
    }

    public BillManualParam buildAddBillParam(CurrentUserReq userReq, AddBillPcReq addBillPcReq) {
        BillManualParam billManualParam = new BillManualParam();
        // 组装操作人信息
        OperationParam operationParam = new OperationParam();
        operationParam.setOperatorId(userReq.getUserId());
        operationParam.setOperatorName(userReq.getUserName());
        operationParam.setComment(addBillPcReq.getComment());
        operationParam.setSettleSubjectType(userReq.getSettleSubjectType());
        operationParam.setSettleSubjectId(userReq.getSettleSubjectId());
        billManualParam.setOperationParam(operationParam);
        BillParam billParam = new BillParam();
        billParam.setBillName(addBillPcReq.getBillName());
        billParam.setPeriodType(addBillPcReq.getPeriodType());
        billParam.setPeriodStart(addBillPcReq.getPeriodStart());
        billParam.setPeriodEnd(addBillPcReq.getPeriodEnd());
        billParam.setBizType(addBillPcReq.getBizType());
        billParam.setBalanceType(addBillPcReq.getBalanceType());
        billParam.setAmount(addBillPcReq.getAmount());
        billParam.setBillingMainBizId(addBillPcReq.getBillingMainBizId());
        billParam.setBillingMainBizType(addBillPcReq.getBillingMainBizType());
        billParam.setNeedCheck(addBillPcReq.getNeedCheck());
        // 现在默认都是手动生成,克隆也是手动生成
        billParam.setGenType(BillGenerateTypeEnum.手动.getCode());
        // 门店id
        billParam.setShopId(addBillPcReq.getShopId());

        // 登陆账号为我司
        if (Objects.equals(userReq.getSettleSubjectType(), SettleSubjectTypeEnum.XIAODIAN.getCode())) {
            // 登陆账户的结算方类型和新增账单的结算方类型不一致
            if (!Objects.equals(userReq.getSettleSubjectType(), addBillPcReq.getSettleSubjectType())) {
                throw BizException.create(CommonErrorCodeEnum.NO_PERMISSION);
            }
            // IOT商户必须为友电
            if (MainBizTypeUtils.isIotMerchant(addBillPcReq.getBillingMainBizType())) {
                billParam.setSettleSubjectId(SettleSubjectIdEnum.友电.getCode().longValue());
                billParam.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
            } else {
                // 其他获取参数里的结算方信息
                billParam.setSettleSubjectType(addBillPcReq.getSettleSubjectType());
                billParam.setSettleSubjectId(addBillPcReq.getSettleSubjectId());
            }
        } else {
            // 渠道用登陆账号的结算方作为参数的结算方
            billParam.setSettleSubjectType(userReq.getSettleSubjectType());
            billParam.setSettleSubjectId(userReq.getSettleSubjectId());
        }
        billParam.setBillAccountType(AccountTypeEnum.toBillRelateAccountTypeCode(addBillPcReq.getAccountType()));
        billManualParam.setBillParam(billParam);
        return billManualParam;
    }

    public BillManualParam buildEditBillParam(CurrentUserReq userReq, EditBillPcReq editBillPcReq) {
        return buildAddBillParam(userReq, editBillPcReq);
    }

    public BillInfoRsp buildBillInfoRsp(BillDTO billDTO, AccountBillItemDTO accountBillItemDTO) {
        if (Objects.isNull(billDTO)) {
            return null;
        }
        BillInfoRsp billInfoRsp = new BillInfoRsp();
        BeanUtils.copyProperties(billDTO, billInfoRsp);
        String mainBizName = mainBizInfoManager
                .getNameByMainBiz(billDTO.getBillingMainBizType(), billDTO.getBillingMainBizId());
        billInfoRsp.setBillingMainBizName(mainBizName);
        // SourceBizId = 0 是没有意义的值，不返回给前端
        if (Objects.nonNull(accountBillItemDTO) && !Objects.equals(accountBillItemDTO.getSourceBizId(), NO_SENSE)) {
            billInfoRsp.setShopId(accountBillItemDTO.getSourceBizId());
        }
        billInfoRsp.setAccountType(AccountTypeEnum.fromBillRelateAccountTypeCode(billInfoRsp.getBillAccountType()).getKey());
        return billInfoRsp;
    }

    public BillOperationParam buildDiscardBillParam(CurrentUserReq userReq, DiscardBillPcReq discardBillPcReq) {
        BillOperationParam billOperationParam = new BillOperationParam();
        billOperationParam.setBillNo(discardBillPcReq.getBillNo());
        billOperationParam.setComment(discardBillPcReq.getComment());
        billOperationParam.setOperatorId(userReq.getUserId());
        billOperationParam.setOperatorName(userReq.getUserName());
        billOperationParam.setSettleSubjectId(userReq.getSettleSubjectId());
        billOperationParam.setSettleSubjectType(userReq.getSettleSubjectType());
        return billOperationParam;
    }

    public BillBatchOperationParam buildBillBatchApprovalParam(CurrentUserReq userReq,
                                                               BatchApprovalBillPcReq batchApprovalBillPcReq) {
        BillBatchOperationParam param = new BillBatchOperationParam();
        param.setBillNos(batchApprovalBillPcReq.getBillNos());
        param.setDecision(batchApprovalBillPcReq.getDecision());
        param.setComment(batchApprovalBillPcReq.getComment());
        param.setSettleSubjectId(userReq.getSettleSubjectId());
        param.setSettleSubjectType(userReq.getSettleSubjectType());
        param.setOperatorId(userReq.getUserId());
        param.setOperatorName(userReq.getUserName());
        return param;
    }

    public BillOperationParam buildApprovalBillParam(CurrentUserReq userReq, ApprovalBillPcReq approvalBillPcReq) {
        BillOperationParam param = new BillOperationParam();
        param.setBillNo(approvalBillPcReq.getBillNo());
        param.setDecision(approvalBillPcReq.getDecision());
        param.setComment(approvalBillPcReq.getComment());
        param.setSettleSubjectId(userReq.getSettleSubjectId());
        param.setSettleSubjectType(userReq.getSettleSubjectType());
        param.setOperatorId(userReq.getUserId());
        param.setOperatorName(userReq.getUserName());
        return param;
    }

    public BillOperationParam buildConfirmBillParam(CurrentUserReq userReq, ConfirmBillPcReq confirmBillPcReq) {
        BillOperationParam billOperationParam = new BillOperationParam();
        billOperationParam.setBillNo(confirmBillPcReq.getBillNo());
        billOperationParam.setBillItemNos(confirmBillPcReq.getBillItemNos());
        billOperationParam.setComment(confirmBillPcReq.getComment());
        billOperationParam.setSettleSubjectId(userReq.getSettleSubjectId());
        billOperationParam.setSettleSubjectType(userReq.getSettleSubjectType());
        billOperationParam.setOperatorId(userReq.getUserId());
        billOperationParam.setOperatorName(userReq.getUserName());
        return billOperationParam;
    }

    public BillOperationParam buildSettleBillAgainParam(CurrentUserReq userReq, String billNo) {
        BillOperationParam billOperationParam = new BillOperationParam();
        billOperationParam.setBillNo(billNo);
        billOperationParam.setSettleSubjectId(userReq.getSettleSubjectId());
        billOperationParam.setSettleSubjectType(userReq.getSettleSubjectType());
        billOperationParam.setOperatorId(userReq.getUserId());
        billOperationParam.setOperatorName(userReq.getUserName());
        return billOperationParam;
    }

    public BillDetailPcRsp buildBillDetail(BillDetailDTO billDetailDTO) {
        if (Objects.isNull(billDetailDTO)) {
            return null;
        }
        BillDetailPcRsp result = new BillDetailPcRsp();
        BeanUtils.copyProperties(billDetailDTO, result);
        String mainBizName = mainBizInfoManager
                .getNameByMainBiz(billDetailDTO.getBillingMainBizType(), billDetailDTO.getBillingMainBizId());
        result.setBillingMainBizName(mainBizName);
        return result;
    }

    public List<BillItemPcRsp> buildBillItemPcRsp(List<AccountBillItemDTO> billItemDTOList) {
        if (CollectionUtils.isEmpty(billItemDTOList)) {
            return Lists.newArrayList();
        }
        List<Long> sourceBizIdList = LocalListUtils.transferList(billItemDTOList, AccountBillItemDTO::getSourceBizId);
        final Map<Long, String> shopIdNameMap;
        if (Objects.equals(MainBizTypeEnum.IOT_MERCHANT.getCode(), billItemDTOList.get(0).getBillingMainBizType())) {
            shopIdNameMap = Maps.newHashMap();
        } else {
            shopIdNameMap = shopCenterManager.getShopByShopIds(sourceBizIdList);
        }
        return LocalListUtils.transferList(billItemDTOList,
                billItemDTO -> buildBillItemPcRsp(billItemDTO, shopIdNameMap.get(billItemDTO.getSourceBizId())));
    }

    public BillItemPcRsp buildBillItemPcRsp(AccountBillItemDTO billItemDTO, String shopName) {
        BillItemPcRsp billItemPcRsp = new BillItemPcRsp();
        BeanUtils.copyProperties(billItemDTO, billItemPcRsp);
        billItemPcRsp.setSourceBizName(shopName);
        return billItemPcRsp;
    }

    public BillItemListQuery buildBillItemQuery(CurrentUserReq userReq, PageBillItemReq pageBillItemReq) {
        pageBillItemReq.setSettleSubjectId(userReq.getSettleSubjectId());
        pageBillItemReq.setSettleSubjectType(userReq.getSettleSubjectType());
        return pageBillItemReq;
    }

    public BillItemNoListRsp buildBillItemNoListRsp(PageResult<String> billItemNoPageResult) {
        if (Objects.isNull(billItemNoPageResult)) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        if (!billItemNoPageResult.isSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
        BillItemNoListRsp result = new BillItemNoListRsp();
        result.setList(billItemNoPageResult.getData());
        result.setTotalCount(billItemNoPageResult.getTotalCount());
        return result;
    }

    public PageData<BillItemShopRsp> buildBillItemShopPage(List<AccountBillItemSourceDTO> billItemSourceDTOList) {
        if (CollectionUtils.isEmpty(billItemSourceDTOList)) {
            return PageData.create(Lists.newArrayList(), 0L);
        }
        List<Long> shopIdList = LocalListUtils
                .transferList(billItemSourceDTOList, AccountBillItemSourceDTO::getSourceBizId);
        Map<Long, ShopInfoDTO> shopIdMap = shopManager.getShopIdMapByIdList(shopIdList);
        List<BillItemShopRsp> billItemShopRsps = LocalListUtils.transferList(billItemSourceDTOList,
                billItemSourceDTO -> buildBillItemShopRsp(billItemSourceDTO,
                        shopIdMap.get(billItemSourceDTO.getSourceBizId())));
        return PageData.create(billItemShopRsps, Long.valueOf(billItemShopRsps.size()));
    }

    private BillItemShopRsp buildBillItemShopRsp(AccountBillItemSourceDTO billItemSourceDTO, ShopInfoDTO shopInfoDTO) {
        BillItemShopRsp result = new BillItemShopRsp();
        result.setShopId(billItemSourceDTO.getSourceBizId());
        if (Objects.nonNull(shopInfoDTO)) {
            result.setShopName(shopInfoDTO.getShopName());
            result.setShopAddress(shopInfoDTO.getFullAddress());
        }
        return result;
    }

    public BillItemListQuery buildBillItemListQuery(CurrentUserReq userReq, String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }
        BillItemListQuery listQuery = new BillItemListQuery();
        listQuery.setBillNo(billNo);
        listQuery.setStatus(1);
        listQuery.setSettleSubjectId(userReq.getSettleSubjectId());
        listQuery.setSettleSubjectType(userReq.getSettleSubjectType());
        listQuery.setPageNo(1);
        listQuery.setPageSize(10);
        return listQuery;
    }

}
