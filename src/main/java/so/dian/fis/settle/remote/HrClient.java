package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;

/**
 * HR远程调用 <br/>
 *
 * <AUTHOR>
 * @date 2019-12-03 11:20
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@FeignClient(name = "hr",url = "hr:8080", fallbackFactory = HrClient.HrFallbackFactory.class)
public interface HrClient {

    /**
     * 根据id获取员工信息
     *
     * @param id 必传 会返回员工的权限及仓库列表信息
     */
    @RequestMapping(value = "/employee/getById", method = RequestMethod.GET)
    BizResult<AgentEmployeeDTO> getById(@RequestParam("id") Long id);

    @Slf4j(topic = "remote")
    @Service
    class HrFallbackFactory implements FallbackFactory<HrClient> {

        @Override
        public HrClient create(Throwable throwable) {

            return new HrClient() {

                @Override
                public BizResult<AgentEmployeeDTO> getById(Long id) {
                    log.error("|hr fallback| getById | id:{}", id, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
