package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.common.rpc.result.DianResult;
import so.dian.sunreaver.api.req.TenantUserAdminAgentReq;
import so.dian.sunreaver.api.resp.TenantUserAdminAgentResp;

@FeignClient(name = "sunreaver", url = "sunreaver:8080", fallbackFactory = SunReaverClient.SunReaverClientFallbackFactory.class)
public interface SunReaverClient {
    /**
     * 查询授权代理
     */
    @PostMapping("/other/tenant/admin/user/findAuthorizeAgentIdsByUser")
    DianResult<TenantUserAdminAgentResp> findAuthorizeAgentIdsByUser(@RequestBody TenantUserAdminAgentReq req);
    @Service
    @Slf4j
    class SunReaverClientFallbackFactory implements FallbackFactory<SunReaverClient> {


        @Override
        public SunReaverClient create(Throwable throwable) {
            return new SunReaverClient() {


                @Override
                public DianResult<TenantUserAdminAgentResp> findAuthorizeAgentIdsByUser(TenantUserAdminAgentReq req) {
                    log.error("SunReaverClient findAuthorizeAgentIdsByUser fallback, req:{}", req, throwable);
                    return DianResult.fail("60002", "远程调用错误");
                }
            };
        }
    }
}
