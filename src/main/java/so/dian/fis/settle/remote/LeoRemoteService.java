package so.dian.fis.settle.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "${feign.leo.name}", url = "${feign.leo.url}", fallback = LeoRemoteService.LeoRemoteServiceFallback.class)
public interface LeoRemoteService {

    @RequestMapping(value = {"/1.0/payPlan/getByApplyNo"})
    String getpayPlanBeforeTaxAmount(@RequestParam("applyNo") String applyNo);

    @Service
    @Slf4j(topic = "error")
    class LeoRemoteServiceFallback implements LeoRemoteService {

        @Override
        public String getpayPlanBeforeTaxAmount(String applyNo) {
            log.error("查询付款申请单税前金额,applyNo:{}-status:{}", applyNo);
            return null;
        }

    }
}

