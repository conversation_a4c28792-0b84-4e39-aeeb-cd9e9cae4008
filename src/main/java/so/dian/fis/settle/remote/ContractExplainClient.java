package so.dian.fis.settle.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.contract.explain.client.dto.agent.AgentContractInfoDTO;

/**
 * ContractExplainClient
 *
 * <AUTHOR>
 * @desc
 * @date 2023/10/13 10:11
 */
@FeignClient(name = "contract-explain",url = "contract-explain:8080", fallbackFactory = ContractExplainClient.ContractExplainFallback.class)
public interface ContractExplainClient {

    @RequestMapping(method = RequestMethod.GET, value = "/contract/getAgentMerchantWithhold")
    BizResult<Boolean> getAgentMerchantWithhold(@RequestParam(name = "partyBAgentId") Long partyBAgentId);
    @RequestMapping(value = "/contract/getSecondaryAgentContractInfo", method = RequestMethod.GET)
    BizResult<AgentContractInfoDTO> getSecondaryAgentContractInfo(@RequestParam(name = "partyBAgentId") Long partyBAgentId);
    @Slf4j(topic = "remote")
    @Component
    public static class ContractExplainFallback implements FallbackFactory<ContractExplainClient> {

        @Override
        public ContractExplainClient create(Throwable cause) {
            return new ContractExplainClient() {
                @Override
                public BizResult<Boolean> getAgentMerchantWithhold(Long partyBAgentId) {
                    return BizResult.error(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<AgentContractInfoDTO> getSecondaryAgentContractInfo(Long partyBAgentId) {
                    log.error("ContractExplainClient getSecondaryAgentContractInfo fallback, partyBAgentId:{}", partyBAgentId,cause);
                    return BizResult.error(ErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
