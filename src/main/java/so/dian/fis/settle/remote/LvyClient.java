package so.dian.fis.settle.remote;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.PageResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.lvy.pojo.dto.*;
import so.dian.lvy.pojo.param.*;
import so.dian.lvy.pojo.query.*;

import java.util.List;

@FeignClient(name = "lvy",url = "lvy:8080", fallbackFactory = LvyClient.LvyClientFallBackFactory.class)
public interface LvyClient {

    //---------account----------
    @GetMapping(value = "/accounts/{accountId}")
    BizResult<AccountDTO> getAccountById(@PathVariable("accountId") Long accountId);

    @PostMapping(value = "/account/page", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    PageResult<QueryAccountListDTO> pageAccount(@RequestBody AccountListQuery query);

    @PostMapping(value = "/account/statistics", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<AccountStatisticsDTO> statisticsAccount(@RequestBody AccountListQuery query);

    @PostMapping(value = "/freeze/{accountId}")
    BizResult<Boolean> freeze(
            @PathVariable("accountId") Long accountId,
            @RequestParam(name = "userId") Long userId,
            @RequestParam(name = "userName") String userName);

    @PostMapping(value = "/unFreeze/{accountId}")
    BizResult<Boolean> unFreeze(
            @PathVariable("accountId") Long accountId,
            @RequestParam(name = "userId") Long userId,
            @RequestParam(name = "userName") String userName);

    //------------bill----------
    @RequestMapping(
            value = {"/bills/addBill"},
            method = {RequestMethod.POST},
            consumes = {"application/json;charset=UTF-8"}
    )
    BizResult<String> addBill(@RequestBody AddBillParam addBillParam);

    @RequestMapping(value = "/bills/manual", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<String> addBillByManual(@RequestBody BillManualParam param);

    @RequestMapping(value = "/bills/{billNo}", method = RequestMethod.PUT, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<String> editBill(@PathVariable("billNo") String billNo, @RequestBody BillManualParam param);

    @RequestMapping(value = "/bills/discardBill", method = RequestMethod.PUT)
    BizResult<String> discardBill(@RequestBody BillOperationParam param);

    @RequestMapping(value = "/bills/approve/batch", method = RequestMethod.PUT)
    BizResult<Boolean> approveBillByBatch(@RequestBody BillBatchOperationParam param);

    @RequestMapping(value = "/bills/approveBill", method = RequestMethod.PUT)
    BizResult<String> approveBill(@RequestBody BillOperationParam param);

    @RequestMapping(value = "/bills/confirmationBill", method = RequestMethod.PUT)
    BizResult<String> confirmationBill(@RequestBody BillOperationParam param);

    @RequestMapping(value = "/bills/settlementBill/again", method = RequestMethod.PUT)
    BizResult<String> settlementBillAgain(@RequestBody BillOperationParam param);

    @RequestMapping(value = "/bills", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    PageResult<BillDTO> listBillByPage(@RequestBody BillListQuery query);

    @RequestMapping(value = "/bills/statistics", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<Long> getBillStatistics(@RequestBody BillListQuery query);

    @RequestMapping(value = "/bills/{billNo}/detail", method = RequestMethod.GET)
    BizResult<BillDetailDTO> getBillDetail(@PathVariable("billNo") String billNo,
            @RequestParam(name = "settleSubjectType") Integer settleSubjectType,
            @RequestParam(name = "settleSubjectId") Long settleSubjectId);

    @RequestMapping(value = "/bills/{billNo}", method = RequestMethod.GET)
    BizResult<BillDTO> getBillInfo(@PathVariable("billNo") String billNo,
            @RequestParam(name = "settleSubjectType") Integer settleSubjectType,
            @RequestParam(name = "settleSubjectId") Long settleSubjectId);

    @RequestMapping(value = "/bills/items", method = RequestMethod.POST)
    PageResult<AccountBillItemDTO> getBillItems(@RequestBody BillItemListQuery query);

    @RequestMapping(value = "/bills/items/statistics", method = RequestMethod.POST)
    BizResult<Long> getBillItemStatistics(@RequestBody BillItemListQuery query);

    @RequestMapping(value = "/bills/itemNos", method = RequestMethod.POST)
    PageResult<String> getBillItemNos(@RequestBody BillItemListQuery query);

    @RequestMapping(value = "/bills/{billNo}/shops", method = RequestMethod.GET)
    BizResult<List<AccountBillItemSourceDTO>> getBillShops(@PathVariable("billNo") String billNo,
            @RequestParam(value = "shopIds", required = false) List<Long> shopIds);

    //----------payApply---------
    @RequestMapping(value = "/payApply/info",method =  RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<PayApplyDTO> getPayApplyByApplyNo(@RequestBody PayApplyDetailQuery query);

    //---------accountBalance-----------
    @RequestMapping(value = "/account/billing/details", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    PageResult<AccountBalanceDTO> listBillingDetails(@RequestBody AccountBalanceNewQuery accountBalanceQuery);

    /**
     * 导入批量新增账单
     */
    @RequestMapping(value = "/bills/batchManual", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<Integer> batchManual(@RequestBody BatchBillManualParam param);

    @Slf4j(topic = "error")
    @Service
    class LvyClientFallBackFactory implements FallbackFactory<LvyClient> {

        @Override
        public LvyClient create(Throwable throwable) {

            return new LvyClient() {

                @Override
                public BizResult<AccountDTO> getAccountById(Long accountId) {
                    log.error("lvy getAccountById fallback, accountId:{}", accountId, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public PageResult<QueryAccountListDTO> pageAccount(AccountListQuery query) {
                    log.error("lvy pageAccount fallback, params:{}", query, throwable);
                    return PageResult.error(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<AccountStatisticsDTO> statisticsAccount(AccountListQuery query) {
                    log.error("lvy statisticsAccount fallback, params:{}", query, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Boolean> freeze(Long accountId, Long userId, String userName) {
                    log.error("lvy freeze fallback, accountId:{},userId:{},userName:{}", accountId, userId, userName,
                            throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Boolean> unFreeze(Long accountId, Long userId, String userName) {
                    log.error("lvy unFreeze fallback, accountId:{},userId:{},userName:{}", accountId, userId, userName,
                            throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> addBill(AddBillParam param) {
                    log.error("lvy addBill fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> addBillByManual(BillManualParam param) {
                    log.error("lvy addBillByManual fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> editBill(String billNo, BillManualParam param) {
                    log.error("lvy editBill fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> discardBill(BillOperationParam param) {
                    log.error("lvy discard bill fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Boolean> approveBillByBatch(
                        BillBatchOperationParam param) {
                    log.error("lvy approveBillByBatch fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> approveBill(BillOperationParam param) {
                    log.error("lvy approveBill fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> confirmationBill(BillOperationParam param) {
                    log.error("lvy confirmBill fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<String> settlementBillAgain(BillOperationParam param) {
                    log.error("lvy settlementBillAgain fallback, params:{}", JSONObject.toJSONString(param), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public PageResult<BillDTO> listBillByPage(BillListQuery query) {
                    log.error("lvy pageBill fallback, params:{}", query, throwable);
                    return PageResult.error(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Long> getBillStatistics(BillListQuery query) {
                    log.error("lvy statisticsBill fallback, params:{}", query, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<BillDTO> getBillInfo(String billNo, Integer settleSubjectType, Long settleSubjectId) {
                    log.error("lvy getBillInfo fallback, billNo:{},settleSubjectId:{}", billNo, settleSubjectId, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public PageResult<AccountBillItemDTO> getBillItems(BillItemListQuery query) {
                    log.error("lvy getBillItems fallback, params:{}", query, throwable);
                    return PageResult.error(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Long> getBillItemStatistics(BillItemListQuery query) {
                    log.error("lvy getBillItemStatistics fallback, params:{}", query, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public PageResult<String> getBillItemNos(BillItemListQuery query) {
                    log.error("lvy getBillItemNos fallback, params:{}", query, throwable);
                    return PageResult.error(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<List<AccountBillItemSourceDTO>> getBillShops(String billNo, List<Long> shopIds) {
                    log.error("lvy getBillShops fallback, billNo:{},shopIds:{}", billNo, shopIds, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<BillDetailDTO> getBillDetail(String billNo, Integer settleSubjectType,
                        Long settleSubjectId) {
                    log.error("lvy getBillDetail fallback, billNo:{},settleSubjectId:{}", billNo, settleSubjectId, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<PayApplyDTO> getPayApplyByApplyNo(PayApplyDetailQuery query) {
                    log.error("lvy getPayApplyByApplyNo fallback, params:{}", query );
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public PageResult<AccountBalanceDTO> listBillingDetails(AccountBalanceNewQuery accountBalanceQuery) {
                    log.error("lvy listBillingDetails fallback, params:{}", accountBalanceQuery, throwable);
                    return PageResult.error(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Integer> batchManual(BatchBillManualParam param) {
                    log.error("lvy batchManual fallback, params:{}", param, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
