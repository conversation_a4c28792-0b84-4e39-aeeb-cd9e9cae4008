package so.dian.fis.settle.remote;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.agent.api.dto.PageDTO;
import so.dian.agent.api.dto.param.AgentQueryParamDTO;
import so.dian.agent.api.dto.request.QueryAgentListRequest;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;

import java.util.List;

/**
 * 代理商远程调用 <br/>
 *
 * <AUTHOR>
 * @date 2019-12-03 11:20
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@FeignClient(name = "agent", url = "agent:8080", fallbackFactory = AgentClient.AgentFallbackFactory.class)
public interface AgentClient {

    /**
     * 根据id获取agent
     */
    @RequestMapping(value = "/agent/getAgentById", method = RequestMethod.GET)
    BizResult<AgentDTO> getAgentById(@RequestParam("agentId") Long agentId);

    @RequestMapping(value = "/agent/getIdsByName", method = RequestMethod.GET)
    BizResult<List<Long>> getIdsByName(@RequestParam("agentName") String agentName, @RequestParam("agentType") Integer agentType);

    @RequestMapping(value = "/agent/getNameByIds", method = RequestMethod.GET)
    BizResult<List<AgentDTO>> getNameByIds(@RequestParam("agentIds") List<Long> agentIds);

    /**
     * 根据类型查询agent数据
     *
     * @param agentQueryParamDTO agentQueryParamDTO
     * @return 结果集
     */
    @PostMapping(value = "/agent/listChannelByType")
    BizResult<PageDTO<AgentDTO>> listChannelByType(@RequestBody AgentQueryParamDTO agentQueryParamDTO);

    /**
     * 根据id获取agent
     *
     * @param queryAgentListRequest
     * @return
     */
    @RequestMapping(value = "/agent/getAgentByIdsAndOptions", method = RequestMethod.POST)
    BizResult<List<AgentDTO>> getAgentByIdsAndOptions(@RequestBody QueryAgentListRequest queryAgentListRequest);




    @Slf4j(topic = "remote")
    @Service
    class AgentFallbackFactory implements FallbackFactory<AgentClient> {

        @Override
        public AgentClient create(Throwable throwable) {

            return new AgentClient() {

                @Override
                public BizResult<AgentDTO> getAgentById(Long agentId) {
                    log.error("agent getAgentById fallback, agentId:{}", agentId, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<List<Long>> getIdsByName(String agentName, Integer agentType) {
                    log.error("agent getIdsByName fallback, agentName:{},agentType:{}", agentName, agentType, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<List<AgentDTO>> getNameByIds(List<Long> agentIds) {
                    log.error("agent getIdsByName fallback, agentIds:{}", agentIds, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<PageDTO<AgentDTO>> listChannelByType(AgentQueryParamDTO agentQueryParamDTO) {
                    log.error("agent listChannelByType fallback, param:{}", JSON.toJSONString(agentQueryParamDTO), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<List<AgentDTO>> getAgentByIdsAndOptions(QueryAgentListRequest queryAgentListRequest) {
                    log.error("agent fallback | getAgentByIdsAndOptions | queryAgentConstant:{}",queryAgentListRequest, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
