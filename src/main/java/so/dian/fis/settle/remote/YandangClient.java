package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

@FeignClient(name = "yandang",url = "yandang:8080", fallbackFactory = YandangClient.PolarisFallbackFactory.class)
public interface YandangClient {


    /**
     * 根据付款单号查询
     */
    @GetMapping(value = "/payBill/info/{billNo}")
    BizResult<PayBillInfoRsp> getPayBillInfo(@PathVariable("billNo") String billNo);


    @Slf4j(topic = "error")
    @Service
    public static class PolarisFallbackFactory implements FallbackFactory<YandangClient> {

        @Override
        public YandangClient create(Throwable throwable) {

            return new YandangClient() {

                @Override
                public BizResult<PayBillInfoRsp> getPayBillInfo(String billNo) {
                    log.error("yandang getPayBillInfo fallback, billNo:{}", billNo);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK, "调用失败");
                }
            };
        }
    }
}
