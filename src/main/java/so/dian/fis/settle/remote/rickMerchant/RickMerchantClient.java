package so.dian.fis.settle.remote.rickMerchant;

import org.springframework.cloud.openfeign.FeignClient;
import so.dian.fis.settle.config.CommonFallbackFactory;
import so.dian.rick.merchant.client.api.MerchantApi;

/**
 * shopcenter 服务
 *
 * @author: shike
 */
@FeignClient(name = "rick-merchant", url = "rick-merchant:8080/rick-merchant", fallbackFactory = CommonFallbackFactory.class)
public interface RickMerchantClient extends MerchantApi {

}
