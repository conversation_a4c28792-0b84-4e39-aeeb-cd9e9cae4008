package so.dian.fis.settle.remote;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.fis.settle.remote.WutaiClient.WutaiIdCardStaticsFallbackFactory;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.wutai.client.pojo.dto.*;

import java.util.List;

/**
 * WutaiIdCardStaticsClient
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 15:56
 */
@FeignClient(name = "wutai",url = "wutai:8080",fallbackFactory = WutaiIdCardStaticsFallbackFactory.class)
public interface WutaiClient {

    /**
     * 账户操作日志
     * @param accountOperationLogDTOList
     * @return
     */
    @PostMapping(value = "/account/operation/log")
    BizResult<Boolean> accountOperationLog(@RequestBody List<AccountOperationLogDTO> accountOperationLogDTOList);

    @PostMapping(value = "/idCard/payment/month/statics")
    BizResult<IdCardPaymentAmountDTO> staticsMonthWithdrawAmount(@RequestBody IdCardPaymentAmountQueryDTO query);


    @PostMapping(value = "/transfer/record/page")
    BizResult<PageData<TransferRecordPageRspDTO>> transferRecordPage(@RequestBody TransferRecordPageDTO query);


    @PostMapping(value = "/transfer/record/detail/page")
    BizResult<PageData<TransferRecordDetailPageRspDTO>> transferRecordDetailPage(@RequestBody TransferRecordDetailPageDTO query);


    @PostMapping(value = "/transfer/record/detail/export")
    BizResult<Boolean> transferRecordDetailExport(@RequestBody TransferRecordDetailExportDTO query);

    @GetMapping(value = "/transfer/record/execute")
    BizResult<Boolean> transferRecordExecute(@RequestParam("operatorId") Long operatorId);

    @GetMapping(value = "/transfer/record/check")
    BizResult<String> transferRecordCheck();
    @Slf4j
    @Component
    public static class WutaiIdCardStaticsFallbackFactory implements FallbackFactory<WutaiClient> {

        @Override
        public WutaiClient create(Throwable cause) {
            log.error("error reomote.",cause);
            return new WutaiClient() {
                @Override
                public BizResult<Boolean> accountOperationLog(List<AccountOperationLogDTO> accountOperationLogDTOList) {
                    log.error("accountOperationLog异常，query={}", JSONObject.toJSONString(accountOperationLogDTOList));
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());
                }

                @Override
                public BizResult<IdCardPaymentAmountDTO> staticsMonthWithdrawAmount(IdCardPaymentAmountQueryDTO query) {
                    log.error("staticsMonthWithdrawAmount异常，query={}", JSONObject.toJSONString(query));
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());
                }

                @Override
                public BizResult<PageData<TransferRecordPageRspDTO>> transferRecordPage(TransferRecordPageDTO query) {
                    log.error("transferRecordPage异常，query={}", JSONObject.toJSONString(query));

                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());                }

                @Override
                public BizResult<PageData<TransferRecordDetailPageRspDTO>> transferRecordDetailPage(TransferRecordDetailPageDTO query) {
                    log.error("transferRecordDetailPage异常，query={}", JSONObject.toJSONString(query));
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());                }

                @Override
                public BizResult<Boolean> transferRecordDetailExport(TransferRecordDetailExportDTO query) {
                    log.error("transferRecordDetailExport异常，query={}", JSONObject.toJSONString(query));
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());                   }

                @Override
                public BizResult<Boolean> transferRecordExecute(Long operatorId) {
                    log.error("transferRecordExecute异常，query={}", operatorId);
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());
                }

                @Override
                public BizResult<String> transferRecordCheck() {
                    log.error("transferRecordCheck异常");
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),
                            ErrorCodeEnum.FALLBACK.getDesc());
                }
            };
        }
    }

}
