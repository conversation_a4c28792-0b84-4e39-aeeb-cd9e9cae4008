package so.dian.fis.settle.remote.rickMerchant;

import org.springframework.cloud.openfeign.FeignClient;
import so.dian.fis.settle.config.CommonFallbackFactory;
import so.dian.rick.merchant.client.api.ConfigApi;

/**
 * @Author: ahuang
 * @CreateTime: 2024-11-25 18:31
 * @Description:
 */
@FeignClient(name = "configApi", url = "rick-merchant:8080/rick-merchant", fallbackFactory = CommonFallbackFactory.class)
public interface RickConfigClient extends ConfigApi {
}
