package so.dian.fis.settle.remote;

import com.google.common.collect.Maps;
import io.github.notoday.dingtalk.robot.DingRobotHelper;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
public class DingTalkClient {

    private static final Map<String, DingRobotHelper> dingTalkClientMap = Maps.newConcurrentMap();

    public DingRobotHelper getDingRobotHelper(String token, String secret) {
        DingRobotHelper dingRobotHelper = dingTalkClientMap.get(token);
        if (Objects.isNull(dingRobotHelper)) {
            dingRobotHelper = new DingRobotHelper(token, secret);
            dingTalkClientMap.put(token, dingRobotHelper);
        }
        return dingRobotHelper;
    }
}
