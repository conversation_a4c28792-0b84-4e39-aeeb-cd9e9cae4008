package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.fis.settle.remote.PapIdCardStaticsClient.PapIdCardStaticsFallbackFactory;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.payapply.platform.dto.IdCardPayApplyAmountQueryDTO;
import so.dian.payapply.platform.dto.IdCardPayApplyAmountStaticsDTO;

/**
 * PapIdCardStaticsClient
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:00
 */
@FeignClient(name = "payapply-platform",url = "payapply-platform:8080",fallbackFactory = PapIdCardStaticsFallbackFactory.class)
public interface PapIdCardStaticsClient {

    @PostMapping(value = "/idCard/payApply/month/statics")
    BizResult<IdCardPayApplyAmountStaticsDTO> staticsMonthPayApplyAmount(@RequestBody @Valid IdCardPayApplyAmountQueryDTO query);


    @Slf4j
    @Component
    public static class PapIdCardStaticsFallbackFactory implements FallbackFactory<PapIdCardStaticsClient>{

        @Override
        public PapIdCardStaticsClient create(Throwable cause) {
            log.error("remote error.",cause);
            return new PapIdCardStaticsClient() {
                @Override
                public BizResult<IdCardPayApplyAmountStaticsDTO> staticsMonthPayApplyAmount(
                        IdCardPayApplyAmountQueryDTO query) {
                    return BizResult.error(ErrorCodeEnum.FALLBACK.getCode().toString(),ErrorCodeEnum.FALLBACK.getDesc());
                }
            };
        }
    }

}
