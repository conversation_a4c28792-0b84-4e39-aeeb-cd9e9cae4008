package so.dian.fis.settle.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.customer.dto.MerchantDTO;
import so.dian.customer.dto.request.AccountMerchantQueryDTO;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;

import java.util.List;

@FeignClient(name = "customer", url = "customer:8080",fallbackFactory = CustomerClient.CustomerFallbackFactory.class)
public interface CustomerClient {

    @PostMapping("/customer/account/listMerchantByMerchantIds")
    BizResult<List<MerchantDTO>> listMerchantByMerchantIds(@RequestBody List<Long> merchantIds);

    @PostMapping(value = "/customer/account/listMerchantByCondition", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<List<MerchantDTO>> listMerchantByCondition(@RequestBody AccountMerchantQueryDTO query);

    @Slf4j(topic = "remote")
    @Service
    class CustomerFallbackFactory implements FallbackFactory<CustomerClient> {

        @Override
        public CustomerClient create(Throwable throwable) {

            return new CustomerClient() {
                @Override
                public BizResult<List<MerchantDTO>> listMerchantByMerchantIds(List<Long> merchantIds) {
                    log.error("customer listMerchantByMerchantIds fallback, merchantIds:{}", merchantIds, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<List<MerchantDTO>> listMerchantByCondition(AccountMerchantQueryDTO query) {
                    log.error("customer listMerchantByCondition fallback, query:{}", query, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
