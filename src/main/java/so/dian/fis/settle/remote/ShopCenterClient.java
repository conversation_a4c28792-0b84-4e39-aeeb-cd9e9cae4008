package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.shop.shopcenter.model.mdo.LeoShopDO;

import java.util.List;

/**
 * shopcenter 服务
 * @author: shike
 */
@FeignClient(name = "shopcenter",url = "shopcenter:8080", fallbackFactory = ShopCenterClient.ShopClientFallbackFactory.class)
public interface ShopCenterClient {

    /**
     * 根据shopIds查询门店列表
     *
     * @param shopIds 参数封装对象
     * @return 门店信息列表
     */
    @PostMapping("/shopcenter/leo/shop/getShopByShopIds")
    BizResult<List<LeoShopDO>> getShopByShopIds(@RequestBody List<Long> shopIds);

    @Slf4j
    @Service
    class ShopClientFallbackFactory implements FallbackFactory<ShopCenterClient> {

        @Override
        public ShopCenterClient create(Throwable throwable) {
            return new ShopCenterClient() {
                @Override
                public BizResult<List<LeoShopDO>> getShopByShopIds(List<Long> shopIds) {
                    log.error("shopcenter . 服务不可用 --> shopIds:{}", shopIds, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
