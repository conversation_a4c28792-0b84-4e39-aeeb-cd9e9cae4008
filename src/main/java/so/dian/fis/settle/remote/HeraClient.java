package so.dian.fis.settle.remote;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.hera.client.dto.merchant.req.pingan.*;
import so.dian.hera.client.dto.merchant.rsp.pingan.*;
import so.dian.hera.client.entity.HeraBizResult;
import so.dian.hera.client.exception.ErrorCodeEnum;

import javax.validation.Valid;

@FeignClient(name = "hera",url = "hera:8080", fallbackFactory = HeraClient.HeraFallbackFactory.class)
public interface HeraClient {

    /**
     * KFEJZB6010
     * 查询银行子账户余额
     * 查询会员子账户以及平台的功能子账户的余额。。
     *
     * 1.此接口不幂等（CnsmrSeqNo）
     * 2.实时返回查询结果
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/acctId/balance/query", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HeraBizResult<AcctIdBalanceQueryRspDTO> acctIdBalanceQuery(@Valid @RequestBody AcctIdBalanceQueryReqDTO req);

    /**
     * KFEJZB6033
     * 会员提现-不验证
     * 此接口受理会员发起的提现申请。会员子账户的可提现余额、可用余额会减少，市场的资金汇总账户(监管账户)会减少相应的发生金额，提现到会员申请的收款账户。
     *
     * 1.此接口幂等（CnsmrSeqNo）
     * 2.命中反欺诈黑名单禁止出入金
     * 3.工商巡检异常禁止出入金
     * 4.签约行为登记，不登记无法提现
     * 5.超过5w提现时会有短信提醒
     * 6.打标的营销子账号默认不支持提现
     * 7.此接口是异步交易，实时返回交易受理结果，调用6110查询提现结果
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/membership/withdrawCash", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HeraBizResult<MembershipWithdrawCashRspDTO> membershipWithdrawCash(@Valid @RequestBody MembershipWithdrawCashReqDTO req);

    /**
     * KFEJZB6050
     * 查询普通转账充值明细
     *
     * 接口用于查询会员主动转账进资金汇总账户的明细情况。
     * 若会员使用绑定账号转入，则直接入账到会员子账户。
     * 若未使用绑定账号转入，则系统无法自动清分到对应子账户，则转入挂账子账户由平台自行清分。
     * 若是 “见证+收单充值”T0充值记录时备注Note为“见证+收单充值,订单号” 此接口可以查到T0到账的“见证+收单充值”充值记录
     *
     * 1.此接口不幂等（CnsmrSeqNo）
     * 2.此接口仅支持30天内交易查询，更长时间请获取对账文件明细，保存到平台本地查询。
     * 3.存在多个子账户绑定同一张银行卡情况的平台，在发生退票时，接口查询返回退票入账记录，入账类型(InAcctType)为02，见证子帐户的帐号(SubAcctNo)和交易网会员代码(TranNetMemberCode)为挂账子账户账号，格式：市场代码+000000000002（例如：3001000000000002）。
     * 4.使用同名子账户（2个，00和SH）且绑定同一张银行卡的平台，该银行卡转账入金，资金自动匹配入同名子账户的OO子账户，接口查询返回入账记录，入账类型(InAcctType)为02，见证子帐户的帐号(SubAcctNo)为该商户同名子账户的00子账户。若满足上述约定3的情况，优先按约定3处理。
     * 5.无上述约定3和4的情况，每个银行卡只唯一绑定一个子账户，该银行卡的入账，入账类型(InAcctType)为02，见证子帐户的帐号(SubAcctNo)是银行卡对应绑定的子账户。
     * 6.不满足上述约定3、4、5的情况，即银行卡未绑定任何一个子账户，该银行卡的入账，入账类型(InAcctType)为03，见证子帐户的帐号(SubAcctNo)是挂账子账户。
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/common/transfer/recharge/query", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HeraBizResult<CommonTransferRechargeQueryRspDTO> commonTransferRechargeQuery(@Valid @RequestBody CommonTransferRechargeQueryReqDTO req);

    /**
     * KFEJZB6072
     * 查询银行时间段内交易明细
     *
     * 1.此接口不幂等（CnsmrSeqNo）
     * 2.此接口是同步的，实时返回查询结果
     * 3.此接口仅支持30天内交易查询，更长时间请获取对账文件明细，保存到平台本地查询
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/bank/transaction/details/query", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HeraBizResult<BankTransactionDetailsQueryRspDTO> bankTransactionDetailsQuery(@Valid @RequestBody BankTransactionDetailsQueryReqDTO req);

    /**
     * KFEJZB6073
     * 查询银行时间段内清分提现明细
     *
     * 1.此接口不幂等（CnsmrSeqNo）
     * 2.此接口是同步的，实时返回查询结果
     * 3.此接口仅支持30天内交易查询，更长时间请获取对账文件明细，保存到平台本地查询
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/bank/withdraw/cash/details/query", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HeraBizResult<BankWithdrawCashDetailsQueryRspDTO> bankWithdrawCashDetailsQuery(@Valid @RequestBody BankWithdrawCashDetailsQueryReqDTO req);


    @Slf4j(topic = "remote")
    @Service
    class HeraFallbackFactory implements FallbackFactory<HeraClient> {

        @Override
        public HeraClient create(Throwable throwable) {
            return new HeraClient() {
                @Override
                public HeraBizResult<AcctIdBalanceQueryRspDTO> acctIdBalanceQuery(AcctIdBalanceQueryReqDTO req) {
                    log.error("调用hera接口acctIdBalanceQuery发生异常, param:{}", JSON.toJSONString(req), throwable);
                    return HeraBizResult.create(ErrorCodeEnum.UNKNOWN_ERROR);
                }

                @Override
                public HeraBizResult<MembershipWithdrawCashRspDTO> membershipWithdrawCash(MembershipWithdrawCashReqDTO req) {
                    log.error("调用hera接口membershipWithdrawCash发生异常, param:{}", JSON.toJSONString(req), throwable);
                    return HeraBizResult.create(ErrorCodeEnum.UNKNOWN_ERROR);
                }

                @Override
                public HeraBizResult<CommonTransferRechargeQueryRspDTO> commonTransferRechargeQuery(CommonTransferRechargeQueryReqDTO req) {
                    log.error("调用hera接口commonTransferRechargeQuery发生异常, param:{}", JSON.toJSONString(req), throwable);
                    return HeraBizResult.create(ErrorCodeEnum.UNKNOWN_ERROR);
                }

                @Override
                public HeraBizResult<BankTransactionDetailsQueryRspDTO> bankTransactionDetailsQuery(BankTransactionDetailsQueryReqDTO req) {
                    log.error("调用hera接口bankTransactionDetailsQuery发生异常, param:{}", JSON.toJSONString(req), throwable);
                    return HeraBizResult.create(ErrorCodeEnum.UNKNOWN_ERROR);
                }

                @Override
                public HeraBizResult<BankWithdrawCashDetailsQueryRspDTO> bankWithdrawCashDetailsQuery(BankWithdrawCashDetailsQueryReqDTO req) {
                    log.error("调用hera接口bankWithdrawCashDetailsQuery发生异常, param:{}", JSON.toJSONString(req), throwable);
                    return HeraBizResult.create(ErrorCodeEnum.UNKNOWN_ERROR);
                }
            };
        }
    }
}