package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.withdraw.platform.dto.IdCardWithdrawAmountDTO;
import so.dian.withdraw.platform.param.MerchantDrawApplyNewParams;
import so.dian.withdraw.platform.query.IdCardWithdrawAmountQueryDTO;

/**
 * <p> 付款单 </p>
 *
 * <AUTHOR>
 * @date 2019-12-20 14:55
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@FeignClient(name = "withdraw-platform", url = "withdraw-platform:8080",fallbackFactory = WithdrawPlatformClient.WithdrawPlatformFallbackFactory.class)
public interface
WithdrawPlatformClient {

    /**
     * 冻结账户，撤销对应的提现单
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/withdraw/cancelNew")
    BizResult<Boolean> cancelNew(@RequestBody MerchantDrawApplyNewParams params);

    /**
     * 身份证号余额
     *
     * @param query
     * @return
     */
    @PostMapping(value = "/idCard/withdraw/month/statics")
    BizResult<IdCardWithdrawAmountDTO> staticsMonthWithdrawAmount(@RequestBody IdCardWithdrawAmountQueryDTO query);


    @Slf4j(topic = "error")
    @Component
    public static class WithdrawPlatformFallbackFactory implements FallbackFactory<WithdrawPlatformClient> {

        @Override
        public WithdrawPlatformClient create(Throwable cause) {
            log.error("remote error.",cause);
            return new WithdrawPlatformClient() {
                @Override
                public BizResult<Boolean> cancelNew(MerchantDrawApplyNewParams params) {
                    log.error("WithdrawPlatformClient fallback| cancelNew | request:{}", params);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<IdCardWithdrawAmountDTO> staticsMonthWithdrawAmount(
                        IdCardWithdrawAmountQueryDTO query) {
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }


}
