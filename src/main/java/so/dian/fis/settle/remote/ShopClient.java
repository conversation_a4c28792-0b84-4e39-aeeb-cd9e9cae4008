package so.dian.fis.settle.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.entity.dto.ShopInfoDTO;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;

import java.util.List;

/**
 * ShopRemoteService
 *
 * <AUTHOR>
 * @date 2018/7/18
 */
@FeignClient(name = "shop", url = "shop:8080",fallbackFactory = ShopClient.ShopClientFallbackFactory.class)
public interface ShopClient {

    /**
     * 根据shopIdList查询门店列表
     *
     * @param shopIds 参数封装对象
     * @return 门店信息列表
     */
    @RequestMapping(value = "/shops/queryShopByShopIds", method = RequestMethod.GET)
    BizResult<List<ShopInfoDTO>> queryShopByShopIds(@RequestParam("shopIds") List<Long> shopIds);

    @Slf4j(topic = "error")
    @Service
    class ShopClientFallbackFactory implements FallbackFactory<ShopClient> {

        @Override
        public ShopClient create(Throwable throwable) {
            return new ShopClient() {
                @Override
                public BizResult<List<ShopInfoDTO>> queryShopByShopIds(List<Long> shopIds) {
                    log.error("ShopClient queryShopByShopIds: 服务不可用, idList:{}", shopIds);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
