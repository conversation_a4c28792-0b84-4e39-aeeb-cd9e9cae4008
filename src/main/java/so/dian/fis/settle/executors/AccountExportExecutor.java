package so.dian.fis.settle.executors;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import so.dian.center.common.util.DateUtil;
import so.dian.center.common.util.MoneyUtil;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.fis.settle.biz.handler.ParamBuilder;
import so.dian.fis.settle.biz.manager.MailManager;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.common.pojo.bo.AccountExportBO;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.fis.settle.controller.account.enums.AccountStatusEnum;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.executors.cmd.AccountExportAppCmd;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgExportReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;
import so.dian.withdraw.platform.enums.MainBizTypeEnum;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * AccountExportExecutor
 *
 * <AUTHOR>
 * @desc
 * @date 2022/12/21 15:55
 */
@Slf4j
@Component
public class AccountExportExecutor implements Executor<AccountExportAppCmd, Boolean> {

    @Resource
    private RickMerchantManager rickMerchantManager;

    @Autowired
    private CustomerManager customerManager;

    @Autowired
    private AgentManager agentManager;

    @Autowired
    private List<ParamBuilder> paramBuilders;

    @Autowired
    private MailManager mailManager;

    @Autowired
    @Qualifier(value = "exportExecutor")
    private ThreadPoolExecutor exportExecutor;

    @Override
    public Boolean execute(AccountExportAppCmd accountExportAppCmd) {

        ListAccountMerchantPgExportReq req = null;
        for (ParamBuilder paramBuilder : paramBuilders) {
            if (paramBuilder.support(accountExportAppCmd)) {
                req = (ListAccountMerchantPgExportReq) paramBuilder.build(accountExportAppCmd);
            }
        }

        if (Objects.isNull(req)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "查询参数为空,不可导出");
        }

        Integer integer = rickMerchantManager.countAccount(req);
        if (Objects.isNull(integer) || integer.compareTo(0) <= 0) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "导出数据为空");
        }

        ListAccountMerchantPgExportReq finalReq = req;

        exportExecutor.execute(() -> {

            int pageNo = 1;
            Integer pageSize = 100;
            finalReq.setPageNo(pageNo);
            finalReq.setPageSize(pageSize);
            String path = "/var/tmp/" + "资金账户列表";
            List<MerchantAccountResp> merchantAccountResps = rickMerchantManager.pageAccount(finalReq);
            File tempFile;
            try {
                tempFile = File.createTempFile(path, ".xlsx");
            } catch (IOException e) {
                log.error("create temp file error.", e);
                return;
            }
            int count = 0;
            Map<Integer, WriteSheet> writeSheetMap = Maps.newHashMap();
            int floor = (int) Math.ceil(integer / 10000.0);
            for (Integer i = 1; i <= floor; i++) {
                writeSheetMap.put(i, EasyExcel.writerSheet(i, "资金账户-" + i).head(AccountExportBO.class).build());
            }

            ExcelWriter writer = EasyExcel.write(tempFile).build();
            while (CollectionUtils.isNotEmpty(merchantAccountResps)) {
                List<Long> settleSubjectIds = merchantAccountResps.stream()
                        .map(MerchantAccountResp::getAccountDimensionFactor3).filter(StringUtils::isNotBlank)
                        .map(Long::parseLong).distinct().collect(
                                Collectors.toList());
                Map<Long, String> idNameMap = agentManager.getIdNameMap(settleSubjectIds);
                idNameMap.put(0L, "杭州友电");
                idNameMap.put(1L, "杭州伊电园（旧）");
                idNameMap.put(3L, "杭州伊电园（新）");

                List<Long> merchantIds = merchantAccountResps.stream()
                        .filter(resp -> MainBizTypeUtils.isMerchant(resp.getMerchantType()))
                        .map(MerchantAccountResp::getMerchantId)
                        .map(Long::parseLong)
                        .distinct()
                        .collect(Collectors.toList());
                Map<Long, String> merchantNameMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(merchantIds)) {
                    merchantNameMap = customerManager.getIdNameMap(merchantIds);
                }

                List<Long> agentIds = merchantAccountResps
                        .stream()
                        .filter(resp -> MainBizTypeUtils.isAgent(resp.getMerchantType()))
                        .map(MerchantAccountResp::getMerchantId)
                        .map(Long::parseLong)
                        .distinct()
                        .collect(Collectors.toList());
                Map<Long, String> agentNameMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(agentIds)) {
                    agentNameMap = agentManager.getIdNameMap(agentIds);
                }

                List<AccountExportBO> accountExportBOS = Lists.newArrayList();
                for (MerchantAccountResp merchantAccountResp : merchantAccountResps) {
                    AccountExportBO accountExportBO = new AccountExportBO();
                    accountExportBO.setAccountNo(merchantAccountResp.getAccountNo());
                    accountExportBO.setAccountType(Arrays.stream(AccountTypeEnum.values())
                            .filter(accountTypeEnum -> Objects.equals(accountTypeEnum.getKey(),
                                    merchantAccountResp.getAccountType())).findFirst().orElse(null).getValue());
                    accountExportBO.setAvailableBalance(MoneyUtil.fen2Yuan(merchantAccountResp.getAvailableAmount()));
                    accountExportBO.setBalance(MoneyUtil.fen2Yuan(merchantAccountResp.getTotalAmount()));
                    accountExportBO.setCreateTime(
                            DateUtil.format(new Date(merchantAccountResp.getGmtCreate()), "yyyy-MM-dd HH:mm:ss"));
                    accountExportBO.setFrozenBalance(MoneyUtil.fen2Yuan(merchantAccountResp.getFrozenAmount()));
                    if (MainBizTypeUtils.isMerchant(merchantAccountResp.getMerchantType())) {
                        accountExportBO.setMainBizName(
                                merchantNameMap.get(Long.valueOf(merchantAccountResp.getMerchantId())));
                    } else if (MainBizTypeUtils.isAgent(merchantAccountResp.getMerchantType())) {
                        accountExportBO.setMainBizName(
                                agentNameMap.get(Long.valueOf(merchantAccountResp.getMerchantId())));
                    } else {
                        accountExportBO.setMainBizName(merchantAccountResp.getMerchantName());
                    }
                    accountExportBO.setMainBizId(merchantAccountResp.getMerchantId());
                    accountExportBO.setMainBizType(
                            LocalEnumUtils.findByCode(MainBizTypeEnum.class, merchantAccountResp.getMerchantType())
                                    .getDesc());
                    if (StringUtils.isNotBlank(merchantAccountResp.getAccountDimensionFactor3())) {
                        accountExportBO.setSettleSubjectName(
                                idNameMap.get(Long.parseLong(merchantAccountResp.getAccountDimensionFactor3())));
                    }
                    accountExportBO.setStatus(AccountStatusEnum.ofCode(merchantAccountResp.getStatus()).getValue());
                    accountExportBOS.add(accountExportBO);

                }
                count += accountExportBOS.size();
                WriteSheet writeSheet = writeSheetMap.get((int) Math.ceil(count / 10000.0));

                writer.write(accountExportBOS, writeSheet);
                if (merchantAccountResps.size() < 100) {
                    break;
                }

                merchantAccountResps.clear();
                finalReq.setPageNo(++ pageNo);
                finalReq.setPageSize(pageSize);
                merchantAccountResps = rickMerchantManager.pageAccount(finalReq);

            }
            writer.finish();
            String email = accountExportAppCmd.getReq().getEmail() != null ? accountExportAppCmd.getReq().getEmail()
                    : accountExportAppCmd.getCurrentUserReq().getUserMail();
            try {
                mailManager.sendEmail(email, "资金账户导出",
                        "资金账户已导出,请查收" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"),
                        tempFile);
            } catch (IOException e) {
                log.error("send email error.{}", email, e);
            }
        });

        return true;
    }
}
