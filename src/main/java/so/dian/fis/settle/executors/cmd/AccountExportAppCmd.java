package so.dian.fis.settle.executors.cmd;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.AccountExportReq;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgReq;

/**
 * AccountExportAppCmd
 *
 * <AUTHOR>
 * @desc
 * @date 2022/12/21 16:45
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class AccountExportAppCmd extends AppCmd {

    private AccountExportReq req;

    private ListAccountMerchantPgReq pgReq;

    public static AccountExportAppCmd of(AccountExportReq req, CurrentUserReq currentUserReq) {
        return new AccountExportAppCmd(req, currentUserReq);
    }


    private AccountExportAppCmd(AccountExportReq req, CurrentUserReq currentUserReq) {
        super(currentUserReq);
        this.req = req;
    }
}
