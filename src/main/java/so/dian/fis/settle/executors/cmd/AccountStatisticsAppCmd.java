package so.dian.fis.settle.executors.cmd;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.AccountListNewReq;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountStatisticsAppCmd extends AppCmd {

    private AccountListNewReq pageAccountNewReq;

    public static AccountStatisticsAppCmd of(AccountListNewReq req, CurrentUserReq currentUserReq){
        AccountStatisticsAppCmd statisticsAppCmd = new AccountStatisticsAppCmd();
        statisticsAppCmd.setPageAccountNewReq(req);
        statisticsAppCmd.setCurrentUserReq(currentUserReq);
        return statisticsAppCmd;
    }
}
