package so.dian.fis.settle.executors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.fis.settle.biz.handler.AccountHandler;
import so.dian.fis.settle.biz.handler.ParamBuilder;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.biz.service.ConstantService;
import so.dian.fis.settle.common.pojo.PageData;
import so.dian.fis.settle.controller.account.enums.UserRoleExtendEnum;
import so.dian.fis.settle.controller.account.response.AccountListNewRsp;
import so.dian.fis.settle.executors.cmd.AccountPageAppCmd;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgExportReq;
import so.dian.rick.merchant.client.merchant.resp.MerchantAccountResp;
import so.dian.withdraw.platform.enums.MainBizTypeEnum;

import java.lang.reflect.Array;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AccountPageExecutor
 *
 * <AUTHOR>
 * @desc
 * @date 2023/1/4 10:52
 */
@Component
public class AccountPageExecutor implements Executor<AccountPageAppCmd, so.dian.fis.settle.common.pojo.PageData<AccountListNewRsp>> {

    @Autowired
    private List<ParamBuilder> paramBuilders;

    @Autowired
    private RickMerchantManager rickMerchantManager;

    @Autowired
    private AccountHandler accountHandler;

    @Autowired
    private AgentManager agentManager;

    @Override
    public so.dian.fis.settle.common.pojo.PageData<AccountListNewRsp> execute(AccountPageAppCmd pageAppCmd) {
        PageData<AccountListNewRsp> pageData = PageData.create(Collections.emptyList(), 0L);

        if(UserRoleExtendEnum.OUTSOURCED_CUSTOMER_SERVICE_M.getRoleName().equals(pageAppCmd.getCurrentUserReq().getCurrentRole())){
            // 判断主体id一定要填写，否者查询失败，报错
            if(Objects.isNull(pageAppCmd.getPageAccountNewReq()) || Objects.isNull(pageAppCmd.getPageAccountNewReq().getMainBizId())){
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请输入对象ID，再进行查询");
            }
            if(CollectionUtils.isEmpty(pageAppCmd.getPageAccountNewReq().getMainBizType())){
                pageAppCmd.getPageAccountNewReq().setMainBizType(Arrays.asList(MainBizTypeEnum.NORMAL_MERCHANT.getCode().toString(),
                        MainBizTypeEnum.BRAND_MERCHANT.getCode().toString(),
                        MainBizTypeEnum.JOIN_MERCHANT.getCode().toString()));
            }
        }
        ListAccountMerchantPgExportReq req = null;
        for (ParamBuilder paramBuilder : paramBuilders) {
            if (paramBuilder.support(pageAppCmd)) {
                req = (ListAccountMerchantPgExportReq) paramBuilder.build(pageAppCmd);
            }
        }
        if (Objects.isNull(req)) {
            return pageData;
        }

        Integer pageNo = pageAppCmd.getPageAccountNewReq().getPageNo();
        Integer pageSize = pageAppCmd.getPageAccountNewReq().getPageSize();
        req.setPageNo(pageNo);
        req.setPageSize(pageSize);
        List<MerchantAccountResp> merchantAccountResps = rickMerchantManager.pageAccount(req);

        List<AccountListNewRsp> accountListNewRsps = accountHandler.buildListRspFromRickAccountList(null,
                merchantAccountResps);
        if (CollUtil.isNotEmpty(accountListNewRsps)) {
            Map<Long, String> belongCompanyMap = new HashMap<>();
            belongCompanyMap.put(0L, "杭州友电");
            belongCompanyMap.put(1L, "杭州伊电园（旧）");
            belongCompanyMap.put(3L, "杭州伊电园（新）");
            List<Long> ids = accountListNewRsps.stream().map(AccountListNewRsp::getBelongCompanyId)
                    .filter(id -> !Objects.isNull(id) && !belongCompanyMap.containsKey(id)).collect(
                            Collectors.toList());
            belongCompanyMap.putAll(agentManager.getIdNameMap(ids));
            accountListNewRsps.forEach(acct -> acct.setBelongCompany(belongCompanyMap.get(acct.getBelongCompanyId())));
        }

        pageData.setList(accountListNewRsps);
        pageData.setPageNo(pageNo.longValue());
        pageData.setPageSize(pageSize);
        return pageData;
    }
}
