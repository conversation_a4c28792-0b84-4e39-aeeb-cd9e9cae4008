package so.dian.fis.settle.executors.builder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.fis.settle.biz.handler.ParamBuilder;
import so.dian.fis.settle.executors.cmd.AccountPageAppCmd;
import so.dian.fis.settle.executors.cmd.AccountStatisticsAppCmd;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgExportReq;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgReq;

import java.util.Objects;

/**
 * @author: miaoshuai
 * @create: 2023/07/03 10:52
 * @description:
 */
@Component
public class AccountStatisticsBuilder implements ParamBuilder<AccountStatisticsAppCmd, ListAccountMerchantPgReq> {

    @Autowired
    private AccountPageParamBuilder pageParamBuilder;

    @Override
    public ListAccountMerchantPgReq build(AccountStatisticsAppCmd statisticsAppCmd) {

        AccountPageAppCmd pageAppCmd = AccountPageAppCmd.of(statisticsAppCmd.getPageAccountNewReq(), statisticsAppCmd.getCurrentUserReq());
        ListAccountMerchantPgExportReq merchantPgExportReq = pageParamBuilder.build(pageAppCmd);
        if (Objects.isNull(merchantPgExportReq)){
            return null;
        }
        ListAccountMerchantPgReq merchantPgReq = new ListAccountMerchantPgReq();
        merchantPgReq.setMerchantIds(merchantPgExportReq.getMerchantIds());
        merchantPgReq.setAccountType(merchantPgExportReq.getAccountType());
        merchantPgReq.setAccountNo(merchantPgExportReq.getAccountNo());
        merchantPgReq.setStartCreateTime(merchantPgExportReq.getStartCreateTime());
        merchantPgReq.setEndCreateTime(merchantPgExportReq.getEndCreateTime());
        merchantPgReq.setMerchantTypes(merchantPgExportReq.getMerchantTypes());
        merchantPgReq.setAccountFactorReqs(merchantPgExportReq.getAccountFactorReqs());
        merchantPgReq.setStatus(merchantPgExportReq.getStatus());
        return merchantPgReq;
    }

    @Override
    public Boolean support(Object source) {
        return source instanceof AccountStatisticsAppCmd;
    }
}
