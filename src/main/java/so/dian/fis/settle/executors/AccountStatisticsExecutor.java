package so.dian.fis.settle.executors;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;
import so.dian.fis.settle.controller.account.enums.UserRoleExtendEnum;
import so.dian.fis.settle.controller.account.response.AccountStatisticsRsp;
import so.dian.fis.settle.executors.builder.AccountStatisticsBuilder;
import so.dian.fis.settle.executors.cmd.AccountStatisticsAppCmd;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgReq;
import so.dian.rick.merchant.client.merchant.resp.AccountStatisticsResp;
import so.dian.withdraw.platform.enums.MainBizTypeEnum;

import java.util.Arrays;
import java.util.Objects;

@Component
public class AccountStatisticsExecutor implements Executor<AccountStatisticsAppCmd, AccountStatisticsRsp> {

    @Autowired
    private AccountStatisticsBuilder statisticsBuilder;

    @Autowired
    private RickMerchantManager merchantManager;

    @Override
    public AccountStatisticsRsp execute(AccountStatisticsAppCmd accountPageAppCmd) {
        if(UserRoleExtendEnum.OUTSOURCED_CUSTOMER_SERVICE_M.getRoleName().equals(accountPageAppCmd.getCurrentUserReq().getCurrentRole())){
            // 判断主体id一定要填写，否者查询失败，报错
            if(Objects.isNull(accountPageAppCmd.getPageAccountNewReq()) || Objects.isNull(accountPageAppCmd.getPageAccountNewReq().getMainBizId())){
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请输入对象ID，再进行查询");
            }
            if(CollectionUtils.isEmpty(accountPageAppCmd.getPageAccountNewReq().getMainBizType())){
                accountPageAppCmd.getPageAccountNewReq().setMainBizType(Arrays.asList(MainBizTypeEnum.NORMAL_MERCHANT.getCode().toString(),
                        MainBizTypeEnum.BRAND_MERCHANT.getCode().toString(),
                        MainBizTypeEnum.JOIN_MERCHANT.getCode().toString()));
            }
        }
        AccountStatisticsRsp statisticsRsp = new AccountStatisticsRsp();
        ListAccountMerchantPgReq merchantPgReq = statisticsBuilder.build(accountPageAppCmd);
        if (Objects.isNull(merchantPgReq)) {
            return statisticsRsp;
        }
        AccountStatisticsResp statisticsResp = merchantManager.statisticsAccount(merchantPgReq);
        if (Objects.isNull(statisticsResp))
            return statisticsRsp;

        BeanUtil.copyProperties(statisticsResp, statisticsRsp);
        statisticsRsp.setTotalCount(statisticsResp.getTotalAccount());
        return statisticsRsp;
    }
}
