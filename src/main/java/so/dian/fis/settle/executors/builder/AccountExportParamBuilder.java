package so.dian.fis.settle.executors.builder;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.fis.settle.biz.handler.ParamBuilder;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.fis.settle.biz.manager.remote.SunReaverManager;
import so.dian.fis.settle.biz.service.ConstantService;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.constant.CommonConstants;
import so.dian.fis.settle.common.util.biz.MainBizTypeUtils;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;
import so.dian.fis.settle.controller.account.request.AccountExportReq;
import so.dian.fis.settle.executors.cmd.AccountExportAppCmd;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.kunlun.caifa.enums.SettleSubjectIdEnum;
import so.dian.rick.merchant.client.merchant.req.AccountFactorReq;
import so.dian.rick.merchant.client.merchant.req.ListAccountMerchantPgExportReq;
import so.dian.withdraw.platform.enums.SettleSubjectTypeEnum;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * AccountExportParamBuilder
 *
 * <AUTHOR>
 * @desc
 * @date 2022/12/21 17:02
 */
@Slf4j
@Component
public class AccountExportParamBuilder implements ParamBuilder<AccountExportAppCmd, ListAccountMerchantPgExportReq> {

    @Autowired
    private CustomerManager customerManager;

    @Autowired
    private AgentManager agentManager;
    @Autowired
    private ConstantService constantService;
    @Autowired
    private SunReaverManager sunReaverManager;

    @Override
    public ListAccountMerchantPgExportReq build(AccountExportAppCmd accountExportAppCmd) {
        if (Objects.isNull(accountExportAppCmd)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "数据为空");
        }
        AccountExportReq req = accountExportAppCmd.getReq();
        CurrentUserReq currentUserReq = accountExportAppCmd.getCurrentUserReq();

        // 类型必选
        AccountExportReq pageAccountNewReq = req;
        if (Objects.isNull(pageAccountNewReq.getAccountType())) {
            log.info("buildRickMerchantAccountListQuery parameter error, param:{}", req);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "账户类型不能为空");
        }

        if (CollectionUtils.isEmpty(pageAccountNewReq.getMainBizType())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "对象类型不能为空");
        }
        //subjectName,status,merchantType,merchantId,accountType,accountNo
        ListAccountMerchantPgExportReq pageQueryAccountMerchantReq = new ListAccountMerchantPgExportReq();
        pageQueryAccountMerchantReq.setMerchantTypes(
                Optional.ofNullable(pageAccountNewReq.getMainBizType()).orElse(Lists.newArrayList()).stream()
                        .map(Integer::valueOf).collect(
                                Collectors.toList()));
        List<String> mainBizIds = buildMerchantIds(pageAccountNewReq);
        if (UserRoleEnum.CITY_PLANING_MANAGER.getRoleName().equals(currentUserReq.getCurrentRole())||UserRoleEnum.CHANNEL_OPERATE.getRoleName().equals(currentUserReq.getCurrentRole())){
            mainBizIds = sunReaverManager.intersectionAgentIds(mainBizIds, currentUserReq);
            if (CollectionUtils.isEmpty(mainBizIds)){
                return null;
            }
        }
        pageQueryAccountMerchantReq.setMerchantIds(mainBizIds);

        if (Objects.nonNull(pageAccountNewReq.getCreateTimeStart())) {
            pageQueryAccountMerchantReq.setStartCreateTime(pageAccountNewReq.getCreateTimeStart().getTime());
        }

        if (Objects.nonNull(pageAccountNewReq.getCreateTimeEnd())) {
            pageQueryAccountMerchantReq.setEndCreateTime(pageAccountNewReq.getCreateTimeEnd().getTime());
        }

        if (StringUtils.isNotBlank(pageAccountNewReq.getAccountNo())) {
            pageQueryAccountMerchantReq.setAccountNo(pageAccountNewReq.getAccountNo());
        }

        if (Objects.nonNull(pageAccountNewReq.getAccountType())) {
            pageQueryAccountMerchantReq.setAccountType(pageAccountNewReq.getAccountType());
        }
        pageQueryAccountMerchantReq.setStatus(pageAccountNewReq.getStatus());
        if (CollectionUtils.isNotEmpty(pageAccountNewReq.getSettleSubjectId())) {
            List<String> settleSubjectIds = pageAccountNewReq.getSettleSubjectId();
            List<AccountFactorReq> reqs = Lists.newArrayList();
            for (String settleSubjectId : settleSubjectIds) {
                // 如果当前登录的是小电员工，且查询的所属公司是合资公司，需要将结算主体类型设置成合资公司的类型
                Long aLong = Long.valueOf(settleSubjectId);
                Integer settleSubjectType = currentUserReq.getSettleSubjectType();
                if (!SettleSubjectIdEnum.containByXD(aLong)) {
                    AgentDTO agentDTO = agentManager.findById(aLong);
                    settleSubjectType = CommonConstants.AGENT_SETTLE_TYPE_MAPPING.get(agentDTO.getType());
                }
                AccountFactorReq accountFactorReq = new AccountFactorReq();
                accountFactorReq.setAccountDimensionFactor1(settleSubjectType);
                accountFactorReq.setAccountDimensionFactor3(settleSubjectId);
                reqs.add(accountFactorReq);
            }
            pageQueryAccountMerchantReq.setAccountFactorReqs(reqs);
        }
        if (AccountTypeEnum.SHARE_ACCOUNT.getKey().equals(pageAccountNewReq.getAccountType())
                || AccountTypeEnum.PING_AN_SHARE_ACCOUNT.getKey().equals(pageAccountNewReq.getAccountType())
                || AccountTypeEnum.IOT_PING_AN_SHARE_ACCOUNT.getKey().equals(pageAccountNewReq.getAccountType())) {
            AccountFactorReq accountFactorReq = null;
            if (CollectionUtils.isEmpty(pageAccountNewReq.getSettleSubjectId())) {
                accountFactorReq = new AccountFactorReq();
                accountFactorReq.setAccountDimensionFactor1(currentUserReq.getSettleSubjectType());
                accountFactorReq.setAccountDimensionFactor3(currentUserReq.getSettleSubjectId().toString());
            }
            if (Objects.nonNull(accountFactorReq)) {
                List<AccountFactorReq> reqs = Optional.ofNullable(pageQueryAccountMerchantReq.getAccountFactorReqs())
                        .orElse(Lists.newArrayList());
                reqs.add(accountFactorReq);
                pageQueryAccountMerchantReq.setAccountFactorReqs(reqs);
            }
        }
        // 设置主体类型默认值
        if (org.springframework.util.CollectionUtils.isEmpty(accountExportAppCmd.getReq().getMainBizType())) {
            // 从下拉框接口获取
            List<NameValueDTO> valueDTOList = constantService.getObjectTypeNew(accountExportAppCmd.getCurrentUserReq());
            if (!org.springframework.util.CollectionUtils.isEmpty(valueDTOList)) {
                pageQueryAccountMerchantReq.setMerchantTypes(valueDTOList.stream().filter(k -> !org.springframework.util.StringUtils.isEmpty(k.getValue())).map(k -> Integer.valueOf(k.getValue())).collect(Collectors.toList()));
            }
        }
        if (Objects.equals(AccountTypeEnum.AGENT_ACCOUNT.getKey(), pageAccountNewReq.getAccountType())) {
            pageQueryAccountMerchantReq.setAccountFactorReqs(null);
        }

        return pageQueryAccountMerchantReq;
    }

    private List<String> buildMerchantIds(AccountExportReq req) {
        List<String> mainBizTypes = req.getMainBizType();
        if (CollectionUtils.isEmpty(mainBizTypes)) {
            return null;
        }
        List<String> mainBizIds = Lists.newArrayList();
        for (String mainBizTypeStr : mainBizTypes) {
            int mainBizType = Integer.parseInt((mainBizTypeStr));

            if (MainBizTypeUtils.isMerchant(mainBizType)) {
                if (StringUtils.isNotBlank(req.getMainBizName())) {
                    List<Long> merchantIds = customerManager.getMerchantIdsByMerchantName(req.getMainBizName());
                    if (CollectionUtils.isEmpty(merchantIds)) {
                        continue;
                    }
                    if (Objects.nonNull(req.getMainBizId())) {
                        if (!merchantIds.contains(req.getMainBizId())) {
                            continue;
                        }
                    }
                    mainBizIds.addAll(merchantIds.stream().map(String::valueOf).collect(Collectors.toList()));
                } else if (Objects.nonNull(req.getMainBizId())) {
                    mainBizIds.add(req.getMainBizId().toString());
                }
            } else if (MainBizTypeUtils.isAgent(mainBizType)) {
                if (StringUtils.isNotBlank(req.getMainBizName())) {
                    List<Long> agentIds = agentManager.getIdsByName(req.getMainBizName(), mainBizType);
                    if (CollectionUtils.isEmpty(agentIds)) {
                        continue;
                    }
                    if (Objects.nonNull(req.getMainBizId())) {
                        if (!agentIds.contains(req.getMainBizId())) {
                            continue;
                        }
                    }
                    mainBizIds.addAll(agentIds.stream().map(String::valueOf).collect(Collectors.toList()));
                } else if (Objects.nonNull(req.getMainBizId())) {
                    mainBizIds.add(req.getMainBizId().toString());
                }

            } else if (MainBizTypeUtils.isIotMerchant(mainBizType)) {
                if (Objects.nonNull(req.getMainBizId())) {
                    mainBizIds.add(req.getMainBizId().toString());
                }
            } else if (Objects.nonNull(req.getMainBizId())) {
                mainBizIds.add(req.getMainBizId().toString());
            }
        }
        return mainBizIds;
    }

    @Override
    public Boolean support(Object accountExportAppCmd) {
        return accountExportAppCmd instanceof AccountExportAppCmd;
    }
}
