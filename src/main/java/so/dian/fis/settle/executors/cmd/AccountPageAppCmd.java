package so.dian.fis.settle.executors.cmd;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.AccountListNewReq;
import so.dian.fis.settle.controller.account.request.PageAccountNewReq;

/**
 * AccountPageAppCmd
 *
 * <AUTHOR>
 * @desc
 * @date 2023/1/4 10:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccountPageAppCmd extends AppCmd {

    private AccountListNewReq pageAccountNewReq;

    public static AccountPageAppCmd of(AccountListNewReq req, CurrentUserReq currentUserReq){
        AccountPageAppCmd accountPageAppCmd = new AccountPageAppCmd();
        accountPageAppCmd.setPageAccountNewReq(req);
        accountPageAppCmd.setCurrentUserReq(currentUserReq);
        return accountPageAppCmd;
    }

}
