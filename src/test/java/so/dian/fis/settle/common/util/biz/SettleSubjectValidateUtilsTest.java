package so.dian.fis.settle.common.util.biz;



import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import so.dian.lvy.pojo.enums.SettleSubjectTypeEnum;

/**
 * SettleSubjectValidateUtilsTest
 *
 * <AUTHOR>
 */
public class SettleSubjectValidateUtilsTest {

    @Test
    public void testValidatePermission() {
        new SettleSubjectValidateUtils();
        Boolean result = SettleSubjectValidateUtils
                .validatePermission(SettleSubjectTypeEnum.小电.getCode(), 1L, SettleSubjectTypeEnum.代理商.getCode(), 100L);
        Assertions.assertFalse(result);
        result = SettleSubjectValidateUtils
                .validatePermission(SettleSubjectTypeEnum.小电.getCode(), 1L, SettleSubjectTypeEnum.小电.getCode(), 3L);
        Assertions.assertTrue(result);
        result = SettleSubjectValidateUtils
                .validatePermission(SettleSubjectTypeEnum.代理商.getCode(), 100L, SettleSubjectTypeEnum.代理商.getCode(),
                        35L);
        Assertions.assertFalse(result);
    }

}
