package so.dian.fis.settle.common.util.biz;



import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import so.dian.fis.settle.controller.account.enums.AccountTypeEnum;

/**
 * MainBizTypeUtilsTest
 *
 * <AUTHOR>
 */
public class MainBizTypeUtilsTest {

    @Test
    public void testIsMerchant() {
        new MainBizTypeUtils();
        boolean result = MainBizTypeUtils.isMerchant(null);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isMerchant(0);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isMerchant(4);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isMerchant(5);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isMerchant(6);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isMerchant(7);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isMerchant(1);
        Assertions.assertTrue(result);
        result = MainBizTypeUtils.isMerchant(2);
        Assertions.assertTrue(result);
        result = MainBizTypeUtils.isMerchant(3);
        Assertions.assertTrue(result);
    }

    @Test
    public void testIsAgent() {
        boolean result = MainBizTypeUtils.isMerchant(null);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isAgent(0);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isAgent(1);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isAgent(2);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isAgent(3);
        Assertions.assertTrue(!result);
        result = MainBizTypeUtils.isAgent(4);
        Assertions.assertTrue(result);
        result = MainBizTypeUtils.isAgent(5);
        Assertions.assertTrue(result);
        result = MainBizTypeUtils.isAgent(6);
        Assertions.assertTrue(result);
        result = MainBizTypeUtils.isAgent(7);
        Assertions.assertTrue(result);
    }

    @Test
    public void testIsIotMerchant() {
        boolean result = MainBizTypeUtils.isIotMerchant(10);
        Assertions.assertTrue(result);
    }

    @Test
    public void testGetAgentTypeByMainBizType() {
        Integer result = MainBizTypeUtils.getAgentTypeByMainBizType(null);
        Assertions.assertNull(result);
        result = MainBizTypeUtils.getAgentTypeByMainBizType(4);
        Assertions.assertTrue(result == 0);
    }

    @Test
    public void testAccountTypeEnum() {
        AccountTypeEnum.CREDIT_ACCOUNT.getKey();
        AccountTypeEnum.CREDIT_ACCOUNT.getValue();
        AccountTypeEnum.OVERDUE_ACCOUNT.getKey();
        AccountTypeEnum.OVERDUE_ACCOUNT.getValue();
        Assertions.assertTrue(true);
    }
}
