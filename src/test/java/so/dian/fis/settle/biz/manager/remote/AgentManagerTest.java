package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.fis.settle.remote.AgentClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.doReturn;

/**
 * AgentManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class AgentManagerTest {

    @InjectMocks
    private AgentManager agentManager = new AgentManager();
    @Mock
    private AgentClient agentClient;

    @Test
    public void testFindById() {
        AgentDTO agentDTO = agentManager.findById(null);
        Assertions.assertNull(agentDTO);

        Long agentId = 100L;
        doReturn(null).when(agentClient).getAgentById(agentId);
        agentDTO = agentManager.findById(agentId);
        Assertions.assertNull(agentDTO);

        doReturn(BizResult.error("100", "world")).when(agentClient).getAgentById(agentId);
        agentDTO = agentManager.findById(agentId);
        Assertions.assertNull(agentDTO);

        doReturn(BizResult.create(null)).when(agentClient).getAgentById(agentId);
        agentDTO = agentManager.findById(agentId);
        Assertions.assertNull(agentDTO);

        AgentDTO agentDTOMock = new AgentDTO();
        doReturn(BizResult.create(agentDTOMock)).when(agentClient).getAgentById(agentId);
        agentDTO = agentManager.findById(agentId);
        Assertions.assertNotNull(agentDTO);
    }

    @Test
    public void testGetNameById() {
        String name = agentManager.getNameById(null);
        Assertions.assertNull(name);

        Long agentId = 100L;
        doReturn(null).when(agentClient).getAgentById(agentId);
        name = agentManager.getNameById(agentId);
        Assertions.assertNull(name);

        doReturn(BizResult.error("100", "world")).when(agentClient).getAgentById(agentId);
        name = agentManager.getNameById(agentId);
        Assertions.assertNull(name);

        AgentDTO agentDTOMock = new AgentDTO();
        agentDTOMock.setAgentName("hello");
        doReturn(BizResult.create(agentDTOMock)).when(agentClient).getAgentById(agentId);
        name = agentManager.getNameById(agentId);
        Assertions.assertNotNull(name);
    }

    @Test
    public void testGetIdsByName() {
        List<Long> result = agentManager.getIdsByName("", MainBizTypeEnum.AGENT.getCode());
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        String name = "hello";
        doReturn(null).when(agentClient).getIdsByName(name, null);
        result = agentManager.getIdsByName(name, null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.error("hello", "world")).when(agentClient).getIdsByName(name, null);
        result = agentManager.getIdsByName(name, null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(Lists.newArrayList())).when(agentClient).getIdsByName(name, null);
        result = agentManager.getIdsByName(name, null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(Lists.newArrayList(10L, 20L))).when(agentClient)
                .getIdsByName(name, null);
        result = agentManager.getIdsByName(name, null);
        Assertions.assertTrue(result.size() == 2);

        Integer mainBizType = MainBizTypeEnum.AGENT.getCode();
        doReturn(null).when(agentClient).getIdsByName(name, 0);
        result = agentManager.getIdsByName(name, mainBizType);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.error("hello", "world")).when(agentClient).getIdsByName(name, 0);
        result = agentManager.getIdsByName(name, mainBizType);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(Lists.newArrayList())).when(agentClient).getIdsByName(name, 0);
        result = agentManager.getIdsByName(name, mainBizType);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(Lists.newArrayList(10L, 20L))).when(agentClient).getIdsByName(name, 0);
        result = agentManager.getIdsByName(name, null);
        Assertions.assertTrue(result.size() == 2);

        result = agentManager.getIdsByName(name, 10);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetIdNameMap() {
        Map<Long, String> result = agentManager.getIdNameMap(null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        result = agentManager.getIdNameMap(Lists.newArrayList());
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        List<Long> agentIds = Lists.newArrayList(10L, 20L);
        doReturn(null).when(agentClient).getNameByIds(agentIds);
        result = agentManager.getIdNameMap(agentIds);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.error("hello", "world")).when(agentClient).getNameByIds(agentIds);
        result = agentManager.getIdNameMap(agentIds);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(
                BizResult.create(Lists.newArrayList(buildAgentDTO(10L, "hello"), buildAgentDTO(20L, " world"))))
                .when(agentClient).getNameByIds(agentIds);
        result = agentManager.getIdNameMap(agentIds);
        Assertions.assertTrue(result.size() == 2);

    }

    private AgentDTO buildAgentDTO(Long id, String name) {
        AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(id);
        agentDTO.setAgentName(name);
        return agentDTO;
    }

}
