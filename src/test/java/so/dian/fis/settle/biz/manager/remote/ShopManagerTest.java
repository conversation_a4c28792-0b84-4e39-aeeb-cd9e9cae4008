package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.entity.dto.ShopInfoDTO;
import so.dian.fis.settle.remote.ShopClient;
import so.dian.himalaya.common.entity.BizResult;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.doReturn;

/**
 * ShopManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class ShopManagerTest {

    @InjectMocks
    private ShopManager shopManager = new ShopManager();
    @Mock
    private ShopClient shopClient;

    @Test
    public void testListByIdList() {
        List<ShopInfoDTO> result = shopManager.listByIdList(null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        result = shopManager.listByIdList(Lists.newArrayList());
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        List<Long> shopIds = Lists.newArrayList(10L, 20L);
        doReturn(null).when(shopClient).queryShopByShopIds(shopIds);
        result = shopManager.listByIdList(shopIds);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.error("F100", "hello")).when(shopClient).queryShopByShopIds(shopIds);
        result = shopManager.listByIdList(shopIds);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(null)).when(shopClient).queryShopByShopIds(shopIds);
        result = shopManager.listByIdList(shopIds);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(Lists.newArrayList(new ShopInfoDTO()))).when(shopClient)
                .queryShopByShopIds(shopIds);
        result = shopManager.listByIdList(shopIds);
        Assertions.assertTrue(result.size() == 1);
    }

    @Test
    public void testGetShopIdMapByIdList() {
        List<Long> shopIds = Lists.newArrayList(10L, 20L);
        doReturn(BizResult.create(Lists.newArrayList(build(10L, "金碧辉煌洗浴中心"), build(20L, "金碧辉煌桑拿中心"))))
                .when(shopClient).queryShopByShopIds(shopIds);
        Map<Long, ShopInfoDTO> result = shopManager.getShopIdMapByIdList(shopIds);
        Assertions.assertTrue(result.size() == 2);
    }

    @Test
    public void testGetShopIdNameMapByIdList() {
        List<Long> shopIds = Lists.newArrayList(10L, 20L);
        doReturn(BizResult.create(Lists.newArrayList(build(10L, "金碧辉煌洗浴中心"), build(20L, "金碧辉煌桑拿中心"))))
                .when(shopClient).queryShopByShopIds(shopIds);
        Map<Long, String> result = shopManager.getShopIdNameMapByIdList(shopIds);
        Assertions.assertTrue(result.size() == 2);
    }

    private ShopInfoDTO build(Long shopId, String shopName) {
        ShopInfoDTO shopInfoDTO = new ShopInfoDTO();
        shopInfoDTO.setShopId(shopId);
        shopInfoDTO.setShopName(shopName);
        return shopInfoDTO;
    }
}
