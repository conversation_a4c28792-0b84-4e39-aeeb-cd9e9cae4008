//package so.dian.fis.settle.biz.handler;
//
//import com.google.common.collect.ImmutableBiMap;
//import com.google.common.collect.Lists;
//import org.apache.commons.collections4.CollectionUtils;
//
//
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import static org.mockito.Mockito.doReturn;
//import org.powermock.modules.junit4.PowerMockRunner;
//import so.dian.fis.settle.biz.manager.remote.AgentManager;
//import so.dian.fis.settle.biz.manager.remote.CustomerManager;
//import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
//import so.dian.fis.settle.controller.account.request.PageAccountReq;
//import so.dian.fis.settle.controller.account.response.AccountListRsp;
//import so.dian.lvy.pojo.dto.QueryAccountListDTO;
//import so.dian.lvy.pojo.enums.MainBizTypeEnum;
//import so.dian.lvy.pojo.enums.SettleSubjectTypeEnum;
//import so.dian.lvy.pojo.query.AccountListQuery;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * AccountHandlerTest
// *
// * <AUTHOR>
// */
//@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
//public class AccountHandlerTest {
//
//    @InjectMocks
//    private AccountHandler accountHandler = new AccountHandler();
//    @Mock
//    private AgentManager agentManager;
//    @Mock
//    private CustomerManager customerManager;
//
//    @Test
//    public void testConstructor() {
//        new AccountHandler();
//    }
//
//    @Test
//    public void testBuildListRspFromLvyAccountListWithMerchant() {
//        Integer mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
//        List<AccountListRsp> result = accountHandler.buildListRspFromLvyAccountList(mainBizType, null);
//        Assertions.assertTrue(CollectionUtils.isEmpty(result));
//
//        List<QueryAccountListDTO> accountListDTOList = Lists
//                .newArrayList(buildQueryAccountListDTO(10L, 10L, 10L, 10L, 10L, mainBizType));
//        doReturn(null).when(customerManager).getIdNameMap(Mockito.anyList());
//        result = accountHandler.buildListRspFromLvyAccountList(mainBizType, accountListDTOList);
//        Assertions.assertTrue(result.size() == 1);
//
//        accountListDTOList = Lists
//                .newArrayList(buildQueryAccountListDTO(10L, 10L, 10L, 10L, 10L, mainBizType));
//        doReturn(ImmutableBiMap.of(10L, "天上人间")).when(customerManager).getIdNameMap(Mockito.anyList());
//        result = accountHandler.buildListRspFromLvyAccountList(mainBizType, accountListDTOList);
//        Assertions.assertTrue(result.size() == 1);
//    }
//
//    @Test
//    public void testBuildListRspFromLvyAccountListWithAgent() {
//        Integer mainBizType = MainBizTypeEnum.AGENT.getCode();
//        List<QueryAccountListDTO> accountListDTOList = Lists
//                .newArrayList(buildQueryAccountListDTO(10L, 10L, 10L, 10L, 10L, mainBizType));
//        doReturn(ImmutableBiMap.of(10L, "天上人间")).when(customerManager).getIdNameMap(Mockito.anyList());
//        List<AccountListRsp> result = accountHandler.buildListRspFromLvyAccountList(mainBizType, accountListDTOList);
//        Assertions.assertTrue(result.size() == 1);
//    }
//
//    @Test
//    public void testBuildLvyAccountListQuery() {
//        PageAccountReq pageAccountReq = null;
//        CurrentUserReq userReq = new CurrentUserReq();
//        userReq.setSettleSubjectType(SettleSubjectTypeEnum.小电.getCode());
//        userReq.setSettleSubjectId(1L);
//        userReq.setUserId(2821L);
//        userReq.setUserName("奕超");
//        AccountListQuery result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNull(result);
//
//        userReq.setSettleSubjectType(SettleSubjectTypeEnum.代理商.getCode());
//        userReq.setSettleSubjectId(10086L);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNull(result);
//
//        pageAccountReq = new PageAccountReq();
//        pageAccountReq.setMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
//        pageAccountReq.setCreateTimeStart(new Date());
//        pageAccountReq.setCreateTimeEnd(new Date());
//        String mainBizName = "人间天堂";
//        pageAccountReq.setMainBizName(mainBizName);
//        doReturn(Lists.newArrayList()).when(customerManager).getMerchantIdsByMerchantName(mainBizName);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNull(result);
//
//        doReturn(Lists.newArrayList(10L, 20L)).when(customerManager).getMerchantIdsByMerchantName(mainBizName);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq.setMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
//        pageAccountReq.setMainBizId(100L);
//        pageAccountReq.setMainBizName(mainBizName);
//        doReturn(Lists.newArrayList(10L, 20L)).when(customerManager).getMerchantIdsByMerchantName(mainBizName);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNull(result);
//
//        pageAccountReq.setMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
//        pageAccountReq.setMainBizId(10L);
//        pageAccountReq.setMainBizName(mainBizName);
//        doReturn(Lists.newArrayList(10L, 20L)).when(customerManager).getMerchantIdsByMerchantName(mainBizName);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq.setMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
//        pageAccountReq.setMainBizId(10L);
//        pageAccountReq.setMainBizName(null);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq = new PageAccountReq();
//        pageAccountReq.setMainBizName(mainBizName);
//        Integer mainBizType = MainBizTypeEnum.AGENT.getCode();
//        pageAccountReq.setMainBizType(mainBizType);
//        doReturn(Lists.newArrayList()).when(agentManager).getIdsByName(mainBizName,mainBizType);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNull(result);
//
//        doReturn(Lists.newArrayList(10L, 20L)).when(agentManager).getIdsByName(mainBizName, mainBizType);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq.setMainBizType(mainBizType);
//        pageAccountReq.setMainBizId(100L);
//        pageAccountReq.setMainBizName(mainBizName);
//        doReturn(Lists.newArrayList(10L, 20L)).when(agentManager).getIdsByName(mainBizName, mainBizType);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNull(result);
//
//        pageAccountReq.setMainBizType(mainBizType);
//        pageAccountReq.setMainBizId(10L);
//        pageAccountReq.setMainBizName(mainBizName);
//        doReturn(Lists.newArrayList(10L, 20L)).when(agentManager).getIdsByName(mainBizName, mainBizType);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq.setMainBizType(mainBizType);
//        pageAccountReq.setMainBizId(10L);
//        pageAccountReq.setMainBizName(null);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq.setMainBizType(MainBizTypeEnum.IOT_MERCHANT.getCode());
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//        pageAccountReq.setMainBizType(10086);
//        result = accountHandler.buildLvyAccountListQuery(pageAccountReq, userReq);
//        Assertions.assertNotNull(result);
//
//    }
//
//    private QueryAccountListDTO buildQueryAccountListDTO(Long accountId, Long accountAmount, Long availableAmount,
//            Long freezeAmount, Long mainBizId, Integer mainBizType) {
//        QueryAccountListDTO result = new QueryAccountListDTO();
//        result.setAccountId(accountId);
//        result.setAccountAmount(accountAmount);
//        result.setAvailableAmount(availableAmount);
//        result.setFreezeAmount(freezeAmount);
//        result.setMainBizId(mainBizId);
//        result.setMainBizType(mainBizType);
//        result.setFreeze(0);
//        result.setCreateTime(new Date());
//        return result;
//    }
//
//}
