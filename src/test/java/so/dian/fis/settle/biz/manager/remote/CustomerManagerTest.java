package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;
import so.dian.customer.dto.MerchantDTO;
import so.dian.fis.settle.remote.CustomerClient;
import so.dian.himalaya.common.entity.BizResult;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.mockito.Mockito.doReturn;

/**
 * CustomerManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class CustomerManagerTest {

    @InjectMocks
    private CustomerManager customerManager = new CustomerManager();
    @Mock
    private CustomerClient customerClient;

    @Test
    public void testGetMerchantIdsByMerchantName() {
        List<Long> result = customerManager.getMerchantIdsByMerchantName(null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        result = customerManager.getMerchantIdsByMerchantName(" ");
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(null).when(customerClient).listMerchantByCondition(Mockito.any());
        result = customerManager.getMerchantIdsByMerchantName("world");
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.error("hello", "world")).when(customerClient)
                .listMerchantByCondition(Mockito.any());
        result = customerManager.getMerchantIdsByMerchantName("world");
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.create(null)).when(customerClient).listMerchantByCondition(Mockito.any());
        result = customerManager.getMerchantIdsByMerchantName("world");
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(
                BizResult.create(Lists
                        .newArrayList(buildMerchantDTO(10L, "甫哥洗脚店", 10L), buildMerchantDTO(20L, "甫哥男模店", 10L))))
                .when(customerClient).listMerchantByCondition(Mockito.any());
        result = customerManager.getMerchantIdsByMerchantName("world");
        Assertions.assertTrue(result.size() == 2);
    }

    @Test
    public void testGetIdNameMap() {
        Map<Long, String> result = customerManager.getIdNameMap(null);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        result = customerManager.getIdNameMap(Lists.newArrayList());
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        List<Long> ids = Lists.newArrayList(10L, 11L);
        doReturn(null).when(customerClient).listMerchantByMerchantIds(ids);
        result = customerManager.getIdNameMap(ids);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(BizResult.error("F123", "world")).when(customerClient).listMerchantByMerchantIds(ids);
        result = customerManager.getIdNameMap(ids);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));

        doReturn(
                BizResult.create(Lists
                        .newArrayList(buildMerchantDTO(10L, "甫哥洗脚店", 10L), buildMerchantDTO(11L, "甫哥男模店", 20L))))
                .when(customerClient).listMerchantByMerchantIds(Mockito.any());
        result = customerManager.getIdNameMap(ids);
        Assertions.assertTrue(result.size() == 2);
        Assertions.assertTrue(Objects.equals("甫哥男模店", result.get(20L)));

    }

    @Test
    public void testGetNameById() {
        String result = customerManager.getNameById(null);
        Assertions.assertNull(result);

        Long id = 10L;
        doReturn(null).when(customerClient).listMerchantByMerchantIds(Mockito.anyList());
        result = customerManager.getNameById(id);
        Assertions.assertNull(result);

        doReturn(BizResult.error("hello", "world")).when(customerClient)
                .listMerchantByMerchantIds(Mockito.anyList());
        result = customerManager.getNameById(id);
        Assertions.assertNull(result);

        doReturn(BizResult.create(Lists.newArrayList())).when(customerClient)
                .listMerchantByMerchantIds(Mockito.anyList());
        result = customerManager.getNameById(id);
        Assertions.assertNull(result);

        doReturn(BizResult.create(Lists.newArrayList(buildMerchantDTO(10L, "world", 10L))))
                .when(customerClient)
                .listMerchantByMerchantIds(Mockito.anyList());
        result = customerManager.getNameById(id);
        Assertions.assertNotNull(result);
    }

    private MerchantDTO buildMerchantDTO(Long id, String name, Long referId) {
        MerchantDTO merchantDTO = new MerchantDTO();
        merchantDTO.setId(id);
        merchantDTO.setName(name);
        merchantDTO.setReferId(referId);
        return merchantDTO;
    }

}
