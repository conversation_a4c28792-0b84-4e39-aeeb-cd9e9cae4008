package so.dian.fis.settle.biz.service;

import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.Test;
import so.dian.fis.settle.common.pojo.validator.BillValidator;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;

public class ConstantServiceTest {

    private ConstantService constantServiceUnderTest;

    @Before("")
    public void setUp() {
        constantServiceUnderTest = new ConstantService();
    }

    @Test
    public void testGetObjectType_XIAODIAN() {
        // Setup
//        final CurrentUserReq userReq = new CurrentUserReq("currentRole", 0L, "userName", "userMail",
//                SettleSubjectTypeEnum.XIAODIAN.getCode(), 0L);
//        final List<NameValueDTO> expectedResult = Arrays.asList(new NameValueDTO("name", "value"));
//        final List<NameValueDTO> result = constantServiceUnderTest.getObjectType(userReq);
    }

    @Test
    public void testGetObjectType() {
        // Setup
//        final CurrentUserReq userReq = new CurrentUserReq("currentRole", 0L, "userName", "userMail", SettleSubjectTypeEnum.OPERATION_SERVICE.getCode(), 0L);
//        final List<NameValueDTO> expectedResult = Arrays.asList(new NameValueDTO("name", "value"));
//        final List<NameValueDTO> result = constantServiceUnderTest.getObjectType(userReq);
    }

    @Test
    public void testGetBalanceBizType() {
        constantServiceUnderTest.getBalanceBizType();
    }

    @Test
    public void testAccountGetObjectType() {
//        final CurrentUserReq userReq = new CurrentUserReq("currentRole", 0L, "userName", "userMail", SettleSubjectTypeEnum.OPERATION_SERVICE.getCode(), 0L);
//        final List<NameValueDTO> result = constantServiceUnderTest.getAccountObjectType(userReq);
    }
    @Test
    public void testGetBillValidatorType() {
        BillValidator.isValidManualBillBizType(AccountBillBizTypeEnum.设备退货退款结算.getCode());
        BillValidator.isValidManualBillBizType(AccountBillBizTypeEnum.新签进场费代付.getCode());
        BillValidator.isValidManualBillBizType(AccountBillBizTypeEnum.不规范建店结算.getCode());
        BillValidator.isValidManualBillBizType(AccountBillBizTypeEnum.客服端退款订单结算.getCode());
        BillValidator.isValidManualBillBizType(AccountBillBizTypeEnum.合作伙伴收入_应分未分.getCode());
        BillValidator.isValidManualBillBizType(AccountBillBizTypeEnum.廉政数据.getCode());

    }
}
