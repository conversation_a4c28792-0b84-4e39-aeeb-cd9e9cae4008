//package so.dian.fis.settle.biz.service;
//
//import com.google.common.collect.Lists;
//
//
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import static org.mockito.Mockito.doReturn;
//import org.powermock.modules.junit4.PowerMockRunner;
//import so.dian.commons.eden.entity.PageResult;
//import so.dian.fis.settle.biz.CurrentUserBuilder;
//import so.dian.fis.settle.biz.handler.AccountHandler;
//import so.dian.fis.settle.biz.manager.remote.LvyManager;
//import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
//import so.dian.fis.settle.controller.account.request.PageAccountReq;
//import so.dian.fis.settle.controller.account.response.AccountListRsp;
//import so.dian.himalaya.common.entity.BizResult;
//import so.dian.himalaya.common.entity.PageData;
//import so.dian.himalaya.common.exception.BizException;
//import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
//import so.dian.lvy.pojo.dto.AccountStatisticsDTO;
//import so.dian.lvy.pojo.dto.QueryAccountListDTO;
//import so.dian.lvy.pojo.enums.MainBizTypeEnum;
//import so.dian.lvy.pojo.query.AccountListQuery;
//
//import java.util.List;
//
///**
// * AccountServiceTest
// *
// * <AUTHOR>
// */
//@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
//public class AccountServiceTest {
//
//    @InjectMocks
//    private AccountService accountService = new AccountService();
//    @Mock
//    private AccountHandler accountHandler;
//    @Mock
//    private LvyManager lvyManager;
//
//    @Test
//    public void testPageAccount() {
//        PageAccountReq req = new PageAccountReq();
//        Long userId = 2821L;
//        String userName = "奕超";
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
//        doReturn(null).when(accountHandler).buildLvyAccountListQuery(req, user);
//        BizResult<PageData<AccountListRsp>> result = accountService.pageAccount(req, user);
//        Assertions.assertTrue(result.getData().getList().size() == 0);
//
//        Integer mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
//        req.setMainBizType(mainBizType);
//        AccountListQuery query = new AccountListQuery();
//        doReturn(query).when(accountHandler).buildLvyAccountListQuery(req, user);
//        List<QueryAccountListDTO> accountListDTOList = Lists.newArrayList(new QueryAccountListDTO());
//        doReturn(PageResult.create(accountListDTOList)).when(lvyManager).pageAccount(query);
//        doReturn(Lists.newArrayList(new AccountListRsp())).when(accountHandler).buildListRspFromLvyAccountList(mainBizType, accountListDTOList);
//        result = accountService.pageAccount(req, user);
//        Assertions.assertTrue(result.getData().getList().size() == 1);
//    }
//
//    @Test
//    public void testQueryAccountAmount() {
//        PageAccountReq req = new PageAccountReq();
//        Long userId = 2821L;
//        String userName = "奕超";
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
//        doReturn(null).when(accountHandler).buildLvyAccountListQuery(req, user);
//        BizResult<AccountStatisticsDTO> result = accountService.queryAccountAmount(req, user);
//        Assertions.assertNull(result.getData());
//
//        AccountListQuery query = new AccountListQuery();
//        doReturn(query).when(accountHandler).buildLvyAccountListQuery(req, user);
//        AccountStatisticsDTO statisticsDTO = new AccountStatisticsDTO();
//        doReturn(statisticsDTO).when(lvyManager).queryAccountAmount(query);
//        result = accountService.queryAccountAmount(req, user);
//        Assertions.assertNotNull(result.getData());
//    }
//
//    @Test
//    public void testFreezeWithAccountIsNull() {
//        Long userId = 2821L;
//        String userName = "奕超";
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
//        accountService.freeze(null, user);
//    }
//
//    @Test
//    public void testFreeze() {
//        Long userId = 2821L;
//        String userName = "奕超";
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
//        Long accountId = 100L;
//        doReturn(true).when(lvyManager).freeze(accountId, userId, userName);
//        BizResult<Boolean> result = accountService.freeze(accountId, user);
//        Assertions.assertTrue(result.getData());
//
//    }
//
//    @Test
//    public void testUnFreezeWithAccountIsNull() {
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
//        accountService.unFreeze(null, user);
//    }
//
//    @Test
//    public void testUnFreeze() {
//        Long userId = 2821L;
//        String userName = "奕超";
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
//        Long accountId = 100L;
//        doReturn(true).when(lvyManager).unFreeze(accountId, userId, userName);
//        BizResult<Boolean> result = accountService.unFreeze(accountId, user);
//        Assertions.assertTrue(result.getData());
//    }
//
//}
