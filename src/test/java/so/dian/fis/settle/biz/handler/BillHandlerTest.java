package so.dian.fis.settle.biz.handler;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.commons.eden.entity.PageResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.entity.dto.ShopInfoDTO;
import so.dian.fis.settle.biz.manager.MainBizInfoManager;
import so.dian.fis.settle.biz.manager.remote.ShopManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.AddBillPcReq;
import so.dian.fis.settle.controller.account.request.ApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.BatchApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.ConfirmBillPcReq;
import so.dian.fis.settle.controller.account.request.DiscardBillPcReq;
import so.dian.fis.settle.controller.account.request.EditBillPcReq;
import so.dian.fis.settle.controller.account.request.PageBillItemReq;
import so.dian.fis.settle.controller.account.request.PcBillListReq;
import so.dian.fis.settle.controller.account.response.BillDetailPcRsp;
import so.dian.fis.settle.controller.account.response.BillInfoRsp;
import so.dian.fis.settle.controller.account.response.BillItemNoListRsp;
import so.dian.fis.settle.controller.account.response.BillItemPcRsp;
import so.dian.fis.settle.controller.account.response.BillItemShopRsp;
import so.dian.fis.settle.controller.account.response.PcBillListRsp;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.exception.BizException;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.dto.AccountBillItemDTO;
import so.dian.lvy.pojo.dto.AccountBillItemSourceDTO;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.dto.BillDetailDTO;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;
import so.dian.lvy.pojo.param.BillBatchOperationParam;
import so.dian.lvy.pojo.param.BillManualParam;
import so.dian.lvy.pojo.param.BillOperationParam;
import so.dian.lvy.pojo.query.BillItemListQuery;
import so.dian.lvy.pojo.query.BillListQuery;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.doReturn;

/**
 * BillHandlerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class BillHandlerTest {

    @InjectMocks
    private BillHandler billHandler = new BillHandler();
    @Mock
    private MainBizInfoManager mainBizInfoManager;
    @Mock
    private ShopManager shopManager;

    @Test
    public void testBuildBillListQueryWithMainBizTypeNull() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        PcBillListReq req = new PcBillListReq();
        billHandler.buildBillListQuery(user, req);
    }

    @Test
    public void testBuildBillListQuery() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        PcBillListReq req = new PcBillListReq();
        req.setBillingMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
        BillListQuery result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNotNull(result);

        user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        req = new PcBillListReq();
        req.setBillingMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
        result = billHandler.buildBillListQuery(user, req);
        req.setSettleSubjectId(1L);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.getSettleSubjectId().equals(19L));

        user = build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        req = new PcBillListReq();
        req.setBillingMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
        result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNotNull(result);

        Integer mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        req.setBillingMainBizType(mainBizType);
        String mainBizName = "天上人间";
        req.setBillingMainBizName(mainBizName);
        doReturn(Lists.newArrayList()).when(mainBizInfoManager)
                .listMainBizIdByTypeAndName(mainBizType, mainBizName);
        result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNull(result);

        mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        req.setBillingMainBizType(mainBizType);
        mainBizName = "天上人间";
        req.setBillingMainBizName(mainBizName);
        doReturn(Lists.newArrayList(10L, 20L)).when(mainBizInfoManager)
                .listMainBizIdByTypeAndName(mainBizType, mainBizName);
        result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNotNull(result);

        req.setBillingMainBizIds(Lists.newArrayList(20L, 50L));
        mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        req.setBillingMainBizType(mainBizType);
        mainBizName = "天上人间";
        req.setBillingMainBizName(mainBizName);
        doReturn(Lists.newArrayList(10L, 20L)).when(mainBizInfoManager)
                .listMainBizIdByTypeAndName(mainBizType, mainBizName);
        result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.getBillingMainBizIds().size() == 1);

        req.setBillingMainBizIds(Lists.newArrayList(30L, 50L));
        mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        req.setBillingMainBizType(mainBizType);
        mainBizName = "天上人间";
        req.setBillingMainBizName(mainBizName);
        doReturn(Lists.newArrayList(10L, 20L)).when(mainBizInfoManager)
                .listMainBizIdByTypeAndName(mainBizType, mainBizName);
        result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNull(result);

        req.setBillingMainBizIds(Lists.newArrayList(20L, 50L));
        mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        req.setBillingMainBizType(mainBizType);
        mainBizName = "天上人间";
        req.setBillingMainBizName(null);
        doReturn(Lists.newArrayList(10L, 20L)).when(mainBizInfoManager)
                .listMainBizIdByTypeAndName(mainBizType, mainBizName);
        result = billHandler.buildBillListQuery(user, req);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.getBillingMainBizIds().size() == 2);

    }

    @Test
    public void testBuildPcBillListRspList() {
        List<PcBillListRsp> result = billHandler.buildPcBillListRspList(Lists.newArrayList());
        Assertions.assertTrue(CollUtil.isEmpty(result));

        List<BillDTO> billDTOList = Lists.newArrayList(buildBillDTO(10L, 100L, MainBizTypeEnum.NORMAL_MERCHANT),
                buildBillDTO(20L, 101L, MainBizTypeEnum.NORMAL_MERCHANT));
        doReturn(Maps.newHashMap()).when(mainBizInfoManager)
                .getIdNameMap(Mockito.anyInt(), Mockito.anyList());
        result = billHandler.buildPcBillListRspList(billDTOList);
        Assertions.assertTrue(result.size() == 2);
        for (PcBillListRsp pcBillListRsp : result) {
            Assertions.assertNull(pcBillListRsp.getBillingMainBizName());
        }

        billDTOList = Lists.newArrayList(buildBillDTO(10L, 100L, MainBizTypeEnum.NORMAL_MERCHANT),
                buildBillDTO(20L, 101L, MainBizTypeEnum.NORMAL_MERCHANT));
        doReturn(ImmutableBiMap
                .of(billDTOList.get(0).getBillingMainBizId(), "hello", billDTOList.get(1).getBillingMainBizId(),
                        "world")).when(mainBizInfoManager)
                .getIdNameMap(Mockito.anyInt(), Mockito.anyList());
        result = billHandler.buildPcBillListRspList(billDTOList);
        Assertions.assertTrue(result.size() == 2);
        for (PcBillListRsp pcBillListRsp : result) {
            if (pcBillListRsp.getBillingMainBizId().equals(100L)) {
                Assertions.assertTrue(pcBillListRsp.getBillingMainBizName().equals("hello"));
            }
            if (pcBillListRsp.getBillingMainBizId().equals(101L)) {
                Assertions.assertTrue(pcBillListRsp.getBillingMainBizName().equals("world"));
            }
        }
    }

    @Test
    public void testBuildAddBillParamWithXDSettleSubjectNotSame() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.XIAODIAN, 19L, 2821L, "奕超");
        AddBillPcReq addBillPcReq = new AddBillPcReq();
        addBillPcReq.setSettleSubjectType(SettleSubjectTypeEnum.OPERATION_SERVICE.getCode());
        addBillPcReq.setSettleSubjectId(10L);
        billHandler.buildAddBillParam(user, addBillPcReq);
    }

    @Test
    public void testBuildAddBillParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        AddBillPcReq addBillPcReq = new AddBillPcReq();
        addBillPcReq.setSettleSubjectType(SettleSubjectTypeEnum.OPERATION_SERVICE.getCode());
        addBillPcReq.setSettleSubjectId(10L);
        BillManualParam result = billHandler.buildAddBillParam(user, addBillPcReq);
        Assertions.assertTrue(result.getBillParam().getSettleSubjectType().equals(SettleSubjectTypeEnum.AGENT.getCode()));
        Assertions.assertTrue(result.getBillParam().getSettleSubjectId().equals(19L));

        user = build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        addBillPcReq = new AddBillPcReq();
        addBillPcReq.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
        addBillPcReq.setSettleSubjectId(3L);
        result = billHandler.buildAddBillParam(user, addBillPcReq);
        Assertions.assertTrue(
                result.getBillParam().getSettleSubjectType().equals(SettleSubjectTypeEnum.XIAODIAN.getCode()));
        Assertions.assertTrue(result.getBillParam().getSettleSubjectId().equals(3L));

        user = build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        addBillPcReq = new AddBillPcReq();
        addBillPcReq.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
        addBillPcReq.setSettleSubjectId(3L);
        addBillPcReq.setBillingMainBizType(MainBizTypeEnum.IOT_MERCHANT.getCode());
        result = billHandler.buildAddBillParam(user, addBillPcReq);
        Assertions.assertTrue(
                result.getBillParam().getSettleSubjectType().equals(SettleSubjectTypeEnum.XIAODIAN.getCode()));
        Assertions.assertTrue(result.getBillParam().getSettleSubjectId().equals(0L));
    }

    @Test
    public void testBuildEditBillParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        EditBillPcReq editBillPcReq = new EditBillPcReq();
        editBillPcReq.setSettleSubjectType(SettleSubjectTypeEnum.OPERATION_SERVICE.getCode());
        editBillPcReq.setSettleSubjectId(10L);
        BillManualParam result = billHandler.buildEditBillParam(user, editBillPcReq);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildBillInfoRsp() {
        BillInfoRsp result = billHandler.buildBillInfoRsp(null, null);
        Assertions.assertNull(result);

        BillDTO billDTO = new BillDTO();
        Integer mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        Long mainBizId = 100L;
        billDTO.setBillingMainBizType(mainBizType);
        billDTO.setBillingMainBizId(mainBizId);
        doReturn("hello world").when(mainBizInfoManager).getNameByMainBiz(mainBizType, mainBizId);
        result = billHandler.buildBillInfoRsp(billDTO, null);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.getBillingMainBizName().equals("hello world"));
    }

    @Test
    public void testBuildDiscardBillParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        DiscardBillPcReq req = new DiscardBillPcReq();
        BillOperationParam result = billHandler.buildDiscardBillParam(user, req);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildBillBatchApprovalParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        BatchApprovalBillPcReq req = new BatchApprovalBillPcReq();
        BillBatchOperationParam result = billHandler.buildBillBatchApprovalParam(user, req);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildApprovalBillParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        ApprovalBillPcReq req = new ApprovalBillPcReq();
        BillOperationParam result = billHandler.buildApprovalBillParam(user, req);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildConfirmBillParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        ConfirmBillPcReq confirmBillPcReq = new ConfirmBillPcReq();
        BillOperationParam result = billHandler.buildConfirmBillParam(user, confirmBillPcReq);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildSettleBillAgainParam() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.AGENT, 19L, 2821L, "奕超");
        String billNo = "12345435";
        BillOperationParam result = billHandler.buildSettleBillAgainParam(user, billNo);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildBillDetail() {
        BillDetailPcRsp result = billHandler.buildBillDetail(null);
        Assertions.assertNull(result);

        BillDetailDTO billDetailDTO = new BillDetailDTO();
        Integer mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
        Long mainBizId = 100L;
        billDetailDTO.setBillingMainBizType(mainBizType);
        billDetailDTO.setBillingMainBizId(mainBizId);
        doReturn("hello").when(mainBizInfoManager).getNameByMainBiz(mainBizType, mainBizId);
        result = billHandler.buildBillDetail(billDetailDTO);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.getBillingMainBizName().equals("hello"));
    }

    @Test
    public void testBuildBillItemPcRsp() {
        List<BillItemPcRsp> result = billHandler.buildBillItemPcRsp(Lists.newArrayList());
        Assertions.assertTrue(CollUtil.isEmpty(result));

        AccountBillItemDTO billItemDTO = new AccountBillItemDTO();
        Long shopId = 10L;
        billItemDTO.setSourceBizId(shopId);
        billItemDTO.setBillingMainBizType(MainBizTypeEnum.IOT_MERCHANT.getCode());
        List<AccountBillItemDTO> billItemDTOListForMock = Lists.newArrayList();
        billItemDTOListForMock.add(billItemDTO);
        result = billHandler.buildBillItemPcRsp(billItemDTOListForMock);
        Assertions.assertTrue(CollUtil.isNotEmpty(result));

        billItemDTO.setBillingMainBizType(MainBizTypeEnum.NORMAL_MERCHANT.getCode());
        doReturn(ImmutableBiMap.of(10L, "world")).when(shopManager)
                .getShopIdNameMapByIdList(Mockito.anyList());
        result = billHandler.buildBillItemPcRsp(billItemDTOListForMock);
        Assertions.assertTrue(result.size() == 1);
        Assertions.assertTrue(result.get(0).getSourceBizName().equals("world"));
    }

    @Test
    public void buildBillItemPcRsp() {
        AccountBillItemDTO billItemDTO = new AccountBillItemDTO();
        String shopName = "hello world";
        BillItemPcRsp result = billHandler.buildBillItemPcRsp(billItemDTO, shopName);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildBillItemQuery() {
        CurrentUserReq user = build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        PageBillItemReq req = new PageBillItemReq();
        BillItemListQuery result = billHandler.buildBillItemQuery(user, req);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildBillItemNoListRsp() {
        PageResult<String> billItemNoPageResult = PageResult.create(Lists.newArrayList("hello", "world"));
        BillItemNoListRsp result = billHandler.buildBillItemNoListRsp(billItemNoPageResult);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBuildBillItemNoListRspWithNull() {
        billHandler.buildBillItemNoListRsp(null);
    }

    @Test
    public void testBuildBillItemNoListRspWithFail() {
        billHandler.buildBillItemNoListRsp(PageResult.error(ErrorCodeEnum.PARAMS_ERROR));
    }

    @Test
    public void testBuildBillItemShopPage() {
        PageData<BillItemShopRsp> result = billHandler.buildBillItemShopPage(Lists.newArrayList());
        Assertions.assertTrue(result.getTotalCount().equals(0L));

        List<AccountBillItemSourceDTO> billItemSourceDTOList = Lists.newArrayList();
        AccountBillItemSourceDTO sourceDTO = new AccountBillItemSourceDTO();
        Long shopId = 10L;
        sourceDTO.setSourceBizId(shopId);
        billItemSourceDTOList.add(sourceDTO);
        ShopInfoDTO shopInfoDTO = new ShopInfoDTO();
        shopInfoDTO.setShopName("hello world");
        Map<Long, ShopInfoDTO> shopIdInfoMap = Maps.newHashMap();
        shopIdInfoMap.put(shopId, shopInfoDTO);
        doReturn(shopIdInfoMap).when(shopManager).getShopIdMapByIdList(Mockito.anyList());
        result = billHandler.buildBillItemShopPage(billItemSourceDTOList);
        Assertions.assertTrue(result.getList().size() == 1);
        Assertions.assertTrue(result.getList().get(0).getShopName().equals("hello world"));
    }

    private BillDTO buildBillDTO(Long id, Long mainBizId, MainBizTypeEnum mainBizTypeEnum) {
        BillDTO billDTO = new BillDTO();
        billDTO.setId(id);
        billDTO.setBillingMainBizId(mainBizId);
        billDTO.setBillingMainBizType(mainBizTypeEnum.getCode());
        billDTO.setBillingMainBizTypeName(mainBizTypeEnum.getDesc());
        return billDTO;
    }

    private CurrentUserReq build(SettleSubjectTypeEnum subjectTypeEnum, Long settleSubjectId, Long userId,
            String userName) {
        CurrentUserReq result = new CurrentUserReq();
        result.setSettleSubjectType(subjectTypeEnum.getCode());
        result.setSettleSubjectId(settleSubjectId);
        result.setUserId(userId);
        result.setUserName(userName);
        return result;
    }

}
