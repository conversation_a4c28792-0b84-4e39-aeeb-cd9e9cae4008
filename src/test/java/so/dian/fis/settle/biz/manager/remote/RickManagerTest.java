package so.dian.fis.settle.biz.manager.remote;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.fis.settle.remote.rickMerchant.RickMerchantClient;

/**
 * HrManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class RickManagerTest {

    @InjectMocks
    private RickMerchantManager rickMerchantManager = new RickMerchantManager();
    @Mock
    private RickMerchantClient rickMerchantClient;

    @Test
    public void testGetEmployeeById() {

    }

}
