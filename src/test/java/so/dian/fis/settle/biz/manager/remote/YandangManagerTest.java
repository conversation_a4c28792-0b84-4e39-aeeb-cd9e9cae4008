package so.dian.fis.settle.biz.manager.remote;



import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.fis.settle.remote.YandangClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

import static org.mockito.Mockito.doReturn;

/**
 * YandangManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class YandangManagerTest {
    @InjectMocks
    private YandangManager yandangManager = new YandangManager();
    @Mock
    private YandangClient yandangClient;

    @Test
    public void testGetPayBillInfo() {
        PayBillInfoRsp result = yandangManager.getPayBillInfo(null);
        Assertions.assertNull(result);

        result = yandangManager.getPayBillInfo(" ");
        Assertions.assertNull(result);

        String billNo = "3492043";
        doReturn(BizResult.error("F1000", "hello")).when(yandangClient).getPayBillInfo(billNo);
        result = yandangManager.getPayBillInfo(billNo);
        Assertions.assertNull(result);

        doReturn(BizResult.create(null)).when(yandangClient).getPayBillInfo(billNo);
        result = yandangManager.getPayBillInfo(billNo);
        Assertions.assertNull(result);

        doReturn(BizResult.create(new PayBillInfoRsp())).when(yandangClient).getPayBillInfo(billNo);
        result = yandangManager.getPayBillInfo(billNo);
        Assertions.assertNotNull(result);
    }

}
