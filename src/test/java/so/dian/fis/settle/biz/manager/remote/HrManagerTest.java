package so.dian.fis.settle.biz.manager.remote;



import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.fis.settle.remote.HrClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;

import static org.mockito.Mockito.doReturn;

/**
 * HrManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class HrManagerTest {

    @InjectMocks
    private HrManager hrManager = new HrManager();
    @Mock
    private HrClient hrClient;

    @Test
    public void testGetEmployeeById() {
        AgentEmployeeDTO agentEmployeeDTO = hrManager.getEmployeeById(null);
        Assertions.assertNull(agentEmployeeDTO);

        Long employeeId = 1024L;
        doReturn(null).when(hrClient).getById(employeeId);
        agentEmployeeDTO = hrManager.getEmployeeById(employeeId);
        Assertions.assertNull(agentEmployeeDTO);

        doReturn(BizResult.error("F100", "error")).when(hrClient).getById(employeeId);
        agentEmployeeDTO = hrManager.getEmployeeById(employeeId);
        Assertions.assertNull(agentEmployeeDTO);

        doReturn(BizResult.create(null)).when(hrClient).getById(employeeId);
        agentEmployeeDTO = hrManager.getEmployeeById(employeeId);
        Assertions.assertNull(agentEmployeeDTO);

        doReturn(BizResult.create(new AgentEmployeeDTO())).when(hrClient).getById(employeeId);
        agentEmployeeDTO = hrManager.getEmployeeById(employeeId);
        Assertions.assertNotNull(agentEmployeeDTO);
    }

}
