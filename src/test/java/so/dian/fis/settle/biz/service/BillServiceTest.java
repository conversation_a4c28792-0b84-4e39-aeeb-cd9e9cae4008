package so.dian.fis.settle.biz.service;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.CurrentUserBuilder;
import so.dian.fis.settle.biz.handler.BillHandler;
import so.dian.fis.settle.biz.manager.DingTalkManager;
import so.dian.fis.settle.biz.manager.LeoManager;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.biz.manager.remote.YandangManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.common.constant.BillConstants;
import so.dian.fis.settle.controller.account.request.AddBillPcReq;
import so.dian.fis.settle.controller.account.request.AddBillReq;
import so.dian.fis.settle.controller.account.request.ApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.BatchAddManualBillReq;
import so.dian.fis.settle.controller.account.request.BatchApprovalBillPcReq;
import so.dian.fis.settle.controller.account.request.ConfirmBillPcReq;
import so.dian.fis.settle.controller.account.request.DiscardBillPcReq;
import so.dian.fis.settle.controller.account.request.EditBillPcReq;
import so.dian.fis.settle.controller.account.request.LvyBillShopPcReq;
import so.dian.fis.settle.controller.account.request.PageBillItemReq;
import so.dian.fis.settle.controller.account.request.PcBillListReq;
import so.dian.fis.settle.controller.account.request.SettleBillAgainPcReq;
import so.dian.fis.settle.controller.account.response.BillDetailPcRsp;
import so.dian.fis.settle.controller.account.response.BillInfoRsp;
import so.dian.fis.settle.controller.account.response.BillItemNoListRsp;
import so.dian.fis.settle.controller.account.response.BillItemPcRsp;
import so.dian.fis.settle.controller.account.response.BillItemShopRsp;
import so.dian.fis.settle.controller.account.response.PcBillListRsp;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.exception.BizException;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.dto.AccountBillItemDTO;
import so.dian.lvy.pojo.dto.AccountBillItemSourceDTO;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.dto.BillDetailDTO;
import so.dian.lvy.pojo.dto.PayApplyDTO;
import so.dian.lvy.pojo.enums.AccountBillBizTypeEnum;
import so.dian.lvy.pojo.param.AddBillParam;
import so.dian.lvy.pojo.param.BillBatchOperationParam;
import so.dian.lvy.pojo.param.BillManualParam;
import so.dian.lvy.pojo.param.BillOperationParam;
import so.dian.lvy.pojo.query.BillItemListQuery;
import so.dian.lvy.pojo.query.BillListQuery;
import so.dian.yandang.client.pojo.enums.BizTypeEnum;
import so.dian.yandang.client.pojo.enums.PayBillStatusEnum;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

/**
 * BillServiceTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class BillServiceTest {

    @InjectMocks
    private BillService billService;
    @Mock
    private DingTalkManager dingTalkManager;
    @Mock
    private LvyManager lvyManager;
    @Mock
    private YandangManager yandangManager;
    @Mock
    private LeoManager leoManager;
    @Mock
    private BillHandler billHandler;
    @Mock
    private BillWhiteListService billWhiteListService;

    @Test
    public void testListBillPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        PcBillListReq listReq = new PcBillListReq();
        doReturn(null).when(billHandler).buildBillListQuery(user, listReq);
        BizResult<List<PcBillListRsp>> result = billService.listBillPc(user, listReq);
        Assertions.assertTrue(result.getData().size() == 0);

        BillListQuery query = new BillListQuery();
        doReturn(query).when(billHandler).buildBillListQuery(user, listReq);
        List<BillDTO> queryBillList = Lists.newArrayList(new BillDTO());
        doReturn(queryBillList).when(lvyManager).pageBillList(query);
        doReturn(Lists.newArrayList(new PcBillListRsp())).when(billHandler)
                .buildPcBillListRspList(queryBillList);
        result = billService.listBillPc(user, listReq);
        Assertions.assertTrue(result.getData().size() == 1);
    }

    @Test
    public void testStatisticsForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        PcBillListReq listReq = new PcBillListReq();
        doReturn(null).when(billHandler).buildBillListQuery(user, listReq);
        BizResult<Long> result = billService.statisticsForPc(user, listReq);
        Assertions.assertTrue(result.getData().equals(0L));

        BillListQuery query = new BillListQuery();
        doReturn(query).when(billHandler).buildBillListQuery(user, listReq);
        doReturn(10L).when(lvyManager).getBillStatistics(query);
        result = billService.statisticsForPc(user, listReq);
        Assertions.assertTrue(result.getData().equals(10L));
    }

    @Test
    public void testAddBillForPcWithInvalidBizType() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        AddBillPcReq addBillPcReq = new AddBillPcReq();
        addBillPcReq.setBizType(AccountBillBizTypeEnum.充电宝转移.getCode());
        billService.addBillForPc(user, addBillPcReq);
    }

    @Test
    public void testAddBillForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        AddBillPcReq addBillPcReq = new AddBillPcReq();
        addBillPcReq.setBizType(AccountBillBizTypeEnum.订单分成.getCode());
        BillManualParam paramForMock = new BillManualParam();
        doReturn(paramForMock).when(billHandler).buildAddBillParam(user, addBillPcReq);
        doReturn("hello world").when(lvyManager).addManualBillReturnStr(paramForMock);
        BizResult<String> result = billService.addBillForPc(user, addBillPcReq);
        Assertions.assertTrue(result.getData().equals("hello world"));
    }

    @Test
    public void testEditBillForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        EditBillPcReq editBillPcReq = new EditBillPcReq();
        String billNo = "**********";
        editBillPcReq.setBillNo(billNo);
        BillManualParam billManualParamForMock = new BillManualParam();
        doReturn(billManualParamForMock).when(billHandler).buildEditBillParam(user, editBillPcReq);
        doReturn(billNo).when(lvyManager).editBill(billNo, billManualParamForMock);
        BizResult<String> result = billService.editBillForPc(user, editBillPcReq);
        Assertions.assertTrue(result.getData().equals(billNo));
    }

    @Test
    public void testDiscardBillForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        DiscardBillPcReq discardBillPcReq = new DiscardBillPcReq();
        BillOperationParam paramForMock = new BillOperationParam();
        doReturn(paramForMock).when(billHandler).buildDiscardBillParam(user, discardBillPcReq);
        doReturn("hello").when(lvyManager).discardBill(paramForMock);
        BizResult<String> result = billService.discardBillForPc(user, discardBillPcReq);
        Assertions.assertTrue(result.getData().equals("hello"));
    }

    @Test
    public void testBatchApprovalBillForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        BatchApprovalBillPcReq approvalBillPcReq = new BatchApprovalBillPcReq();
        BillBatchOperationParam paramForMock = new BillBatchOperationParam();
        doReturn(paramForMock).when(billHandler).buildBillBatchApprovalParam(user, approvalBillPcReq);
        doReturn(true).when(lvyManager).batchApprovalBill(paramForMock);
        BizResult<Boolean> result = billService.batchApprovalBillForPc(user, approvalBillPcReq);
        Assertions.assertTrue(result.getData());
    }

    @Test
    public void testApprovalBillForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        ApprovalBillPcReq approvalBillPcReq = new ApprovalBillPcReq();
        BillOperationParam paramForMock = new BillOperationParam();
        doReturn(paramForMock).when(billHandler).buildApprovalBillParam(user, approvalBillPcReq);
        doReturn("hello").when(lvyManager).approvalBill(paramForMock);
        BizResult<String> result = billService.approvalBillForPc(user, approvalBillPcReq);
        Assertions.assertTrue(result.getData().equals("hello"));
    }

    @Test
    public void testConfirmBillForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        ConfirmBillPcReq confirmBillPcReq = new ConfirmBillPcReq();
        BillOperationParam paramForMock = new BillOperationParam();
        doReturn(paramForMock).when(billHandler).buildConfirmBillParam(user, confirmBillPcReq);
        doReturn("hello").when(lvyManager).confirmBill(paramForMock);
        BizResult<String> result = billService.confirmBillForPc(user, confirmBillPcReq);
        Assertions.assertTrue(result.getData().equals("hello"));
    }

    @Test
    public void testSettleBillAgainForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        SettleBillAgainPcReq settleBillAgainPcReq = new SettleBillAgainPcReq();
        String billNo = "hello";
        settleBillAgainPcReq.setBillNo(billNo);
        BillOperationParam paramForMock = new BillOperationParam();
        doReturn(paramForMock).when(billHandler).buildSettleBillAgainParam(user, billNo);
        doReturn("hello").when(lvyManager).settleBillAgain(paramForMock);
        BizResult<String> result = billService.settleBillAgainForPc(user, settleBillAgainPcReq);
        Assertions.assertTrue(result.getData().equals(billNo));
    }

    @Test
    public void testGetBillInfoWithBillNoNull() {
        Long userId = 2821L;
        String userName = "奕超";
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
        billService.getBillInfo(user, null);
    }

    @Test
    public void testGetBillInfo() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        String billNo = "hello";
        BillDTO billDTO = new BillDTO();
        doReturn(billDTO).when(lvyManager).getBillInfo(billNo, settleSubjectType, settleSubjectId);
        BillInfoRsp billInfoRsp = new BillInfoRsp();
        doReturn(billInfoRsp).when(billHandler).buildBillInfoRsp(billDTO, null);
        BizResult<BillInfoRsp> result = billService.getBillInfo(user, billNo);
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void testGetBillDetailForPcWithNotExisted() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        String billNo = "hello";
        doReturn(null).when(lvyManager).getBillDetail(billNo, settleSubjectType, settleSubjectId);
        billService.getBillDetailForPc(user, billNo);
    }

    @Test
    public void testGetBillDetailForPc() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        String billNo = "hello";
        BillDetailDTO billDetailDTO = new BillDetailDTO();
        BillDetailPcRsp billDetailPcRsp = new BillDetailPcRsp();
        doReturn(billDetailDTO).when(lvyManager).getBillDetail(billNo, settleSubjectType, settleSubjectId);
        doReturn(billDetailPcRsp).when(billHandler).buildBillDetail(billDetailDTO);
        BizResult<BillDetailPcRsp> result = billService.getBillDetailForPc(user, billNo);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testPageBillItem() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        PageBillItemReq pageBillItemReq = new PageBillItemReq();
        BillItemListQuery query = new BillItemListQuery();
        doReturn(query).when(billHandler).buildBillItemQuery(user, pageBillItemReq);
        PageResult<AccountBillItemDTO> billItemDTOPageResult = PageResult.create(Lists.newArrayList(new AccountBillItemDTO()));
        doReturn(billItemDTOPageResult).when(lvyManager).pageBillItem(query);
        List<BillItemPcRsp> billItemPcRspList = Lists.newArrayList(new BillItemPcRsp());
        doReturn(billItemPcRspList).when(billHandler).buildBillItemPcRsp(billItemDTOPageResult.getData());
        BizResult<PageData<BillItemPcRsp>> result = billService.pageBillItem(user, pageBillItemReq);
        Assertions.assertTrue(result.getData().getList().size() == 1);
    }

    @Test
    public void testStatisticsBillItems() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        PageBillItemReq pageBillItemReq = new PageBillItemReq();
        BillItemListQuery query = new BillItemListQuery();
        doReturn(query).when(billHandler).buildBillItemQuery(user, pageBillItemReq);
        doReturn(10L).when(lvyManager).statisticsBillItems(query);
        BizResult<Long> result = billService.statisticsBillItems(user, pageBillItemReq);
        Assertions.assertTrue(result.getData().equals(10L));
    }

    @Test
    public void testGetBillItemNos() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        PageBillItemReq pageBillItemReq = new PageBillItemReq();
        BillItemListQuery query = new BillItemListQuery();
        doReturn(query).when(billHandler).buildBillItemQuery(user, pageBillItemReq);
        PageResult<String> billItemNoPageResult = PageResult.create(Lists.newArrayList("hello", "world"));
        doReturn(billItemNoPageResult).when(lvyManager).getBillItemNos(query);
        BillItemNoListRsp resultForMock = new BillItemNoListRsp();
        resultForMock.setList(billItemNoPageResult.getData());
        doReturn(resultForMock).when(billHandler).buildBillItemNoListRsp(billItemNoPageResult);
        BizResult<BillItemNoListRsp> result = billService.getBillItemNos(user, pageBillItemReq);
        Assertions.assertTrue(result.getData().getList().size() == 2);
    }

    @Test
    public void testGetBillShops() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        String billNo = "hello world";
        List<Long> shopIdList = Lists.newArrayList(10L,20L);
        LvyBillShopPcReq lvyBillShopPcReq = new LvyBillShopPcReq();
        lvyBillShopPcReq.setBillNo(billNo);
        lvyBillShopPcReq.setShopIds(shopIdList);
        List<AccountBillItemSourceDTO> billItemSourceDTOList = Lists.newArrayList(new AccountBillItemSourceDTO());
        doReturn(billItemSourceDTOList).when(lvyManager).getBillShops(billNo, shopIdList);
        PageData<BillItemShopRsp> resultForMock = PageData.create(Lists.newArrayList(new BillItemShopRsp()));
        doReturn(resultForMock).when(billHandler).buildBillItemShopPage(billItemSourceDTOList);
        BizResult<PageData<BillItemShopRsp>> result = billService.getBillShops(user, lvyBillShopPcReq);
        Assertions.assertTrue(result.getData().getList().size() == 1);
    }

    @Test
    public void testBatchAddManualBillWithNoPermission() {
        Long userId = 2821L;
        String userName = "奕超";
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        BatchAddManualBillReq batchAddManualBillReq = new BatchAddManualBillReq();
        BizResult<Integer> result = billService.batchAddManualBill(user, batchAddManualBillReq);
    }

    @Test
    public void testBatchAddManualBill() {
        Long userId = 2821L;
        String userName = BillConstants.UPLOAD_MANUAL_BILL_NICK_NAME.get(0);
        Integer settleSubjectType = SettleSubjectTypeEnum.XIAODIAN.getCode();
        Long settleSubjectId = 1L;
        CurrentUserReq user = CurrentUserBuilder.build(settleSubjectType, settleSubjectId, userId, userName);
        BatchAddManualBillReq batchAddManualBillReq = new BatchAddManualBillReq();
        AddBillReq addBillReq = new AddBillReq();
        List<AddBillReq> addBillReqList = Lists.newArrayList(addBillReq);
        batchAddManualBillReq.setAddBillReqList(addBillReqList);
        doReturn(true).when(lvyManager).addManualBill(any(BillManualParam.class));
        doReturn(true).when(billWhiteListService).checkHasPermission(any());
        BizResult<Integer> result = billService.batchAddManualBill(user, batchAddManualBillReq);
        // Assertions.assertTrue(result.getData().getSuccessList().size() == 1);
        // Assertions.assertTrue(result.getData().getFailList().size() == 0);


        doReturn(false).when(lvyManager).addManualBill(any(BillManualParam.class));
        result = billService.batchAddManualBill(user, batchAddManualBillReq);
        // Assertions.assertTrue(result.getData().getSuccessList().size() == 0);
        // Assertions.assertTrue(result.getData().getFailList().size() == 1);
    }

    @Test
    public void testAddBillWithPayBillNull() {
        String billNo = "hello";
        doReturn(null).when(yandangManager).getPayBillInfo(billNo);
        Mockito.verify(lvyManager, Mockito.times(0)).getPayApplyByApplyNo(Mockito.anyString());
    }

    @Test
    public void testAddBill() {
        String billNo = "hello";
        PayBillInfoRsp payBillInfoRsp = new PayBillInfoRsp();
        payBillInfoRsp.setBizType(BizTypeEnum.MERCHANT_WITHDRAW.getCode());
        doReturn(payBillInfoRsp).when(yandangManager).getPayBillInfo(billNo);
        billService.addBill(billNo);
        Mockito.verify(lvyManager, Mockito.times(0)).getPayApplyByApplyNo(Mockito.anyString());

        payBillInfoRsp.setBizType(BizTypeEnum.ADVANCE_PAYMENT.getCode());
        payBillInfoRsp.setTradeState(PayBillStatusEnum.WAIT_PAY.getCode());
        billService.addBill(billNo);
        Mockito.verify(lvyManager, Mockito.times(0)).getPayApplyByApplyNo(Mockito.anyString());

        payBillInfoRsp.setBizType(BizTypeEnum.ADVANCE_PAYMENT.getCode());
        payBillInfoRsp.setTradeState(PayBillStatusEnum.PAY_SUCCESS.getCode());
        String outBizNo = "world";
        payBillInfoRsp.setOutBizNo(outBizNo);
        doReturn(null).when(lvyManager).getPayApplyByApplyNo(outBizNo);
        billService.addBill(billNo);
        Mockito.verify(leoManager, Mockito.times(0)).getpayPlanBeforeTaxAmount(Mockito.anyString());

        payBillInfoRsp.setBizType(BizTypeEnum.ADVANCE_PAYMENT.getCode());
        payBillInfoRsp.setTradeState(PayBillStatusEnum.PAY_SUCCESS.getCode());
        outBizNo = "world";
        payBillInfoRsp.setOutBizNo(outBizNo);
        PayApplyDTO payApplyDTO = new PayApplyDTO();
        doReturn(payApplyDTO).when(lvyManager).getPayApplyByApplyNo(outBizNo);
        doReturn(1000L).when(leoManager).getpayPlanBeforeTaxAmount(outBizNo);
        doReturn("james").when(lvyManager).addBill(any(AddBillParam.class));
        billService.addBill(billNo);
        Mockito.verify(lvyManager, Mockito.times(1)).addBill(any());
    }

}
