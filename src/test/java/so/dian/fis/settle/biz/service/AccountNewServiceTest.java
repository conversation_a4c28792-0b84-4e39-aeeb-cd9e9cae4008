package so.dian.fis.settle.biz.service;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.fis.settle.biz.handler.AccountHandler;
import so.dian.fis.settle.biz.manager.remote.RickMerchantManager;

/**
 * AccountServiceTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class AccountNewServiceTest {

    @InjectMocks
    private AccountNewService accountNewService = new AccountNewService();
    @Mock
    private AccountHandler accountHandler;
    @Mock
    private RickMerchantManager rickMerchantManager;

//    @Test
//    public void testPageAccount() {
//        PageAccountNewReq req = new PageAccountNewReq();
//        req.setPageNo(1);
//        req.setPageSize(10);
//        req.setMainBizType(4);
//        req.setAccountType(30);
//        Long userId = 16102L;
//        String userName = "兴霸";
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, userId, userName);
//        doReturn(null).when(accountHandler).buildRickMerchantAccountListQuery(req, user);
//        BizResult<PageData<AccountListNewRsp>> result = accountNewService.pageAccount(req, user);
//        Assertions.assertTrue(result.getData().getList().size() == 0);
//
//        Integer mainBizType = MainBizTypeEnum.NORMAL_MERCHANT.getCode();
//        req.setMainBizType(mainBizType);
//        PageQueryAccountMerchantReq query = new PageQueryAccountMerchantReq();
//        query.setPageNo(1);
//        query.setPageSize(10);
//        query.setMerchantType(4);
//        query.setAccountType(30);
//        doReturn(query).when(accountHandler).buildRickMerchantAccountListQuery(req, user);
//        List<MerchantAccountResp> accountListDTOList = Lists.newArrayList(new MerchantAccountResp());
//        doReturn(PageResult.create(accountListDTOList)).when(rickMerchantManager).pageQueryAccount(query);
//        doReturn(Lists.newArrayList(new AccountListNewRsp())).when(accountHandler).buildListRspFromRickAccountList(mainBizType, accountListDTOList);
//        result = accountNewService.pageAccount(req, user);
//        Assertions.assertTrue(result.getData().getList().size() == 1);
//    }

}
