package so.dian.fis.settle.biz.service;

import com.google.common.collect.Lists;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.commons.eden.entity.PageResult;
import so.dian.fis.settle.biz.CurrentUserBuilder;
import so.dian.fis.settle.biz.manager.remote.LvyManager;
import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceReq;
import so.dian.fis.settle.controller.account.response.AccountBalancePcRsp;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
import so.dian.lvy.pojo.dto.AccountBalanceDTO;
import so.dian.lvy.pojo.dto.AccountDTO;

import static org.mockito.Mockito.doReturn;

/**
 * AccountBalanceServiceTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class AccountBalanceServiceTest {
    @InjectMocks
    private AccountBalanceService accountBalanceService = new AccountBalanceService();
    @Mock
    private LvyManager lvyManager;

    @InjectMocks
    private AccountBalanceNewService accountBalanceNewService = new AccountBalanceNewService();

    @Test
    public void testPageAccountBalanceReq() {
        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, 2821L, "奕超");
        Long accountId = 100L;
        PageAccountBalanceReq req = new PageAccountBalanceReq();
        req.setAccountId(accountId);
        doReturn(null).when(lvyManager).getAccountById(accountId);
        BizResult<PageData<AccountBalancePcRsp>> result = accountBalanceService.pageAccountBalanceReq(user, req);
        Assertions.assertTrue(result.getData().getList().size() == 0);

        req.setAccountId(accountId);
        AccountDTO accountDTO = new AccountDTO();
        accountDTO.setId(accountId);
        accountDTO.setSettleSubjectType(SettleSubjectTypeEnum.OPERATION_SERVICE.getCode());
        accountDTO.setSettleSubjectId(100L);
        doReturn(accountDTO).when(lvyManager).getAccountById(accountId);
        result = accountBalanceService.pageAccountBalanceReq(user, req);
        Assertions.assertTrue(result.getData().getList().size() == 0);

        req.setAccountId(accountId);
        accountDTO = new AccountDTO();
        accountDTO.setId(accountId);
        accountDTO.setSettleSubjectType(SettleSubjectTypeEnum.XIAODIAN.getCode());
        accountDTO.setSettleSubjectId(1L);
        doReturn(accountDTO).when(lvyManager).getAccountById(accountId);
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setId(10L);
        doReturn(PageResult.create(Lists.newArrayList(accountBalanceDTO), 1, 1, 10)).when(lvyManager).pageBalanceList(req);
        result = accountBalanceService.pageAccountBalanceReq(user, req);
        Assertions.assertTrue(result.getData().getList().size() == 1);
    }
//
//    @Test
//    public void testAccountBalanceType(){
//        final CurrentUserReq userReq = new CurrentUserReq("currentRole", 0L, "userName", "userMail", SettleSubjectTypeEnum.OPERATION_SERVICE.getCode(), 0L);
//        accountBalanceService.accountBalanceType(userReq);
//    }

}
