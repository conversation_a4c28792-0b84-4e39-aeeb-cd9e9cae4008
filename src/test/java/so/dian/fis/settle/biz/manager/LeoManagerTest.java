package so.dian.fis.settle.biz.manager;

import com.alibaba.fastjson.JSON;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.common.logger.Log;
import so.dian.fis.settle.remote.LeoRemoteService;
import so.dian.himalaya.common.entity.BizResult;

import java.util.Objects;

import static org.mockito.Mockito.doReturn;

/**
 * LeoManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class LeoManagerTest {

    @InjectMocks
    private LeoManager leoManager = new LeoManager();
    @Mock
    private LeoRemoteService leoRemoteService;

    @Test
    public void testGetpayPlanBeforeTaxAmount() {
        Log.info(Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        String applyNo = "12345";
        BizResult<Long> returnResult = BizResult.error("hello", "world");
        doReturn(JSON.toJSONString(returnResult)).when(leoRemoteService).getpayPlanBeforeTaxAmount(applyNo);
        Long result = leoManager.getpayPlanBeforeTaxAmount(applyNo);
        Assertions.assertNull(result);
        doReturn(null).when(leoRemoteService).getpayPlanBeforeTaxAmount(applyNo);
        result = leoManager.getpayPlanBeforeTaxAmount(applyNo);
        Assertions.assertNull(result);
        doReturn("{\"data\":100}").when(leoRemoteService).getpayPlanBeforeTaxAmount(applyNo);
        result = leoManager.getpayPlanBeforeTaxAmount(applyNo);
        Assertions.assertNull(result);
        returnResult = BizResult.create(100L);
        doReturn(JSON.toJSONString(returnResult)).when(leoRemoteService).getpayPlanBeforeTaxAmount(applyNo);
        result = leoManager.getpayPlanBeforeTaxAmount(applyNo);
        Assertions.assertTrue(Objects.equals(result, 100L));

    }

}
