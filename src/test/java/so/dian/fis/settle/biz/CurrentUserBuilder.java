package so.dian.fis.settle.biz;

import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;

/**
 * CurrentUserBuilder
 *
 * <AUTHOR>
 */
public class CurrentUserBuilder {

    public static CurrentUserReq build(SettleSubjectTypeEnum subjectTypeEnum, Long settleSubjectId, Long userId,
            String userName) {
        CurrentUserReq result = new CurrentUserReq();
        result.setSettleSubjectType(subjectTypeEnum.getCode());
        result.setSettleSubjectId(settleSubjectId);
        result.setUserId(userId);
        result.setUserName(userName);
        return result;
    }

    public static CurrentUserReq build(Integer subjectType, Long settleSubjectId, Long userId,
            String userName) {
        CurrentUserReq result = new CurrentUserReq();
        result.setSettleSubjectType(subjectType);
        result.setSettleSubjectId(settleSubjectId);
        result.setUserId(userId);
        result.setUserName(userName);
        return result;
    }
}
