//package so.dian.fis.settle.biz.service;
//
//
//
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.powermock.modules.junit4.PowerMockRunner;
//import so.dian.fis.settle.biz.CurrentUserBuilder;
//import so.dian.fis.settle.biz.handler.AccountHandler;
//import so.dian.fis.settle.common.aop.aspect.CurrentUserReq;
//import so.dian.fis.settle.common.constant.CommonConstants;
//import so.dian.fis.settle.controller.account.request.PageAccountNewReq;
//import so.dian.fis.settle.controller.account.request.PageAccountReq;
//import so.dian.fis.settle.controller.account.response.AccountListNewRsp;
//import so.dian.fis.settle.controller.account.response.NameValueRsp;
//import so.dian.himalaya.common.entity.BizResult;
//import so.dian.himalaya.common.entity.PageData;
//import so.dian.kunlun.caifa.enums.SettleSubjectTypeEnum;
//import so.dian.lvy.pojo.query.AccountListQuery;
//
//import java.util.List;
//
///**
// * @author: miaoshuai
// * @create: 2022/10/18 15:31
// * @description:
// */
//@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
//public class SelectionOptionServiceTest {
//
//    @InjectMocks
//    private SelectionOptionService optionService = new SelectionOptionService();
//    @InjectMocks
//    private AccountNewService accountNewService = new AccountNewService();
//    @InjectMocks
//    private AccountHandler accountHandler = new AccountHandler();
//
//    @Test
//    public void testBelongCompanys() {
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, 33287L, "苗帅");
//        List<NameValueRsp<String, String>> nameValueRsps = optionService.belongCompanys(user);
//        Assertions.assertTrue(nameValueRsps.size() != 0);
//    }
//
//    @Test
//    public void testBelongCompanysJv() {
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.JV_CORP, 1L, 16694L, "测试组合资老板");
//        List<NameValueRsp<String, String>> nameValueRsps = optionService.belongCompanys(user);
//        Assertions.assertTrue(nameValueRsps.size() != 0);
//    }
//
//    @Test
//    public void testBuildLvyAccountListQuery() {
//        PageAccountReq pageAccountReq = new PageAccountReq();
//        pageAccountReq.setFreeze(-1);
//        pageAccountReq.setMainBizId(1L);
//        pageAccountReq.setPageNo(1);
//        pageAccountReq.setPageSize(10);
//        pageAccountReq.setSettleSubjectId(4000L);
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, 33287L, "苗帅");
//        AccountListQuery accountListQuery = accountHandler.buildLvyAccountListQuery(pageAccountReq, user);
//        Assertions.assertTrue(CommonConstants.AGENT_SETTLE_TYPE_MAPPING.containsKey(accountListQuery.getSettleSubjectType()));
//    }
//
//    @Test
//    public void testPageAccount() {
//        PageAccountNewReq newReq = new PageAccountNewReq();
//        newReq.setPageNo(1);
//        newReq.setPageSize(20);
//        newReq.setAccountType(30);
//        newReq.setSettleSubjectId(0L);
//        CurrentUserReq user = CurrentUserBuilder.build(SettleSubjectTypeEnum.XIAODIAN, 1L, 33287L, "苗帅");
//        BizResult<PageData<AccountListNewRsp>> pageDataBizResult = accountNewService.pageAccount(newReq, user);
//        Assertions.assertTrue(pageDataBizResult.getData().getList().size() != 0);
//    }
//}
