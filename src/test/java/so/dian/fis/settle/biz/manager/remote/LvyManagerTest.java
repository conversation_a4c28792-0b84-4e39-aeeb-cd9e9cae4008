package so.dian.fis.settle.biz.manager.remote;

import com.google.common.collect.Lists;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.commons.eden.entity.PageResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.fis.settle.controller.account.request.PageAccountBalanceReq;
import so.dian.fis.settle.remote.LvyClient;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.exception.BizException;
import so.dian.lvy.pojo.dto.AccountBalanceDTO;
import so.dian.lvy.pojo.dto.AccountBillItemDTO;
import so.dian.lvy.pojo.dto.AccountBillItemSourceDTO;
import so.dian.lvy.pojo.dto.AccountDTO;
import so.dian.lvy.pojo.dto.AccountStatisticsDTO;
import so.dian.lvy.pojo.dto.BillDTO;
import so.dian.lvy.pojo.dto.BillDetailDTO;
import so.dian.lvy.pojo.dto.PayApplyDTO;
import so.dian.lvy.pojo.dto.QueryAccountListDTO;
import so.dian.lvy.pojo.enums.BalanceBizDocTypeEnum;
import so.dian.lvy.pojo.param.AddBillParam;
import so.dian.lvy.pojo.param.BillBatchOperationParam;
import so.dian.lvy.pojo.param.BillManualParam;
import so.dian.lvy.pojo.param.BillOperationParam;
import so.dian.lvy.pojo.param.BillParam;
import so.dian.lvy.pojo.query.AccountBalanceNewQuery;
import so.dian.lvy.pojo.query.AccountListQuery;
import so.dian.lvy.pojo.query.BillItemListQuery;
import so.dian.lvy.pojo.query.BillListQuery;
import so.dian.lvy.pojo.query.PayApplyDetailQuery;

import java.util.List;

import static org.mockito.Mockito.doReturn;

/**
 * LvyManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
public class LvyManagerTest {

    @InjectMocks
    private LvyManager lvyManager = new LvyManager();
    @Mock
    private LvyClient lvyClient;

    @Test
    public void testPageAccount() {
        doReturn(PageResult.create(Lists.newArrayList())).when(lvyClient)
                .pageAccount(Mockito.any(AccountListQuery.class));
        PageResult<QueryAccountListDTO> result = lvyManager.pageAccount(new AccountListQuery());
        Assertions.assertNotNull(result);
    }

    @Test
    public void testGetAccountById() {
        AccountDTO result = lvyManager.getAccountById(null);
        Assertions.assertNull(result);

        Long accountId = 10086L;
        doReturn(null).when(lvyClient).getAccountById(accountId);
        result = lvyManager.getAccountById(accountId);
        Assertions.assertNull(result);

        accountId = 10086L;
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).getAccountById(accountId);
        result = lvyManager.getAccountById(accountId);
        Assertions.assertNull(result);

        accountId = 10086L;
        doReturn(BizResult.create(new AccountDTO())).when(lvyClient).getAccountById(accountId);
        result = lvyManager.getAccountById(accountId);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testQueryAccountAmount() {
        doReturn(null).when(lvyClient).statisticsAccount(Mockito.any(AccountListQuery.class));
        AccountStatisticsDTO result = lvyManager.queryAccountAmount(new AccountListQuery());
        Assertions.assertNull(result);

        doReturn(BizResult.error("F1000", "hello")).when(lvyClient)
                .statisticsAccount(Mockito.any(AccountListQuery.class));
        result = lvyManager.queryAccountAmount(new AccountListQuery());
        Assertions.assertNull(result);

        doReturn(BizResult.create(null)).when(lvyClient)
                .statisticsAccount(Mockito.any(AccountListQuery.class));
        result = lvyManager.queryAccountAmount(new AccountListQuery());
        Assertions.assertNull(result);

        doReturn(BizResult.create(new AccountStatisticsDTO())).when(lvyClient)
                .statisticsAccount(Mockito.any(AccountListQuery.class));
        result = lvyManager.queryAccountAmount(new AccountListQuery());
        Assertions.assertNotNull(result);
    }

    //----------account freeze & unfreeze-----
    @Test
    public void testFreezeWithNull() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(null).when(lvyClient).freeze(accountId, userId, nickName);
        lvyManager.freeze(accountId, userId, nickName);
    }

    @Test
    public void testFreezeWithFail() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).freeze(accountId, userId, nickName);
        lvyManager.freeze(accountId, userId, nickName);
    }

    @Test
    public void testFreezeWithDataFalse() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(BizResult.create(false)).when(lvyClient).freeze(accountId, userId, nickName);
        lvyManager.freeze(accountId, userId, nickName);
    }

    @Test
    public void testFreezeWithDataTrue() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(BizResult.create(true)).when(lvyClient).freeze(accountId, userId, nickName);
        Boolean result = lvyManager.freeze(accountId, userId, nickName);
        Assertions.assertTrue(result);
    }

    @Test
    public void testUnfreezeWithNull() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(null).when(lvyClient).unFreeze(accountId, userId, nickName);
        lvyManager.unFreeze(accountId, userId, nickName);
    }

    @Test
    public void testUnfreezeWithFail() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).unFreeze(accountId, userId, nickName);
        lvyManager.unFreeze(accountId, userId, nickName);
    }

    @Test
    public void testUnfreezeWithDataFalse() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(BizResult.create(false)).when(lvyClient).unFreeze(accountId, userId, nickName);
        lvyManager.unFreeze(accountId, userId, nickName);
    }

    @Test
    public void testUnfreezeWithDataTrue() {
        Long accountId = 10086L;
        Long userId = 2821L;
        String nickName = "奕超";
        doReturn(BizResult.create(true)).when(lvyClient).unFreeze(accountId, userId, nickName);
        Boolean result = lvyManager.unFreeze(accountId, userId, nickName);
        Assertions.assertTrue(result);
    }

    //----------bill--------
    @Test
    public void testAddBill() {
        AddBillParam param = new AddBillParam();
        param.setBillParam(new BillParam());
        doReturn(null).when(lvyClient).addBill(param);
        String result = lvyManager.addBill(param);
        Assertions.assertNull(result);

        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).addBill(param);
        result = lvyManager.addBill(param);
        Assertions.assertNull(result);

        doReturn(BizResult.create("***************")).when(lvyClient).addBill(param);
        result = lvyManager.addBill(param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testAddManualBill() {
        BillManualParam param = new BillManualParam();
        param.setBillParam(new BillParam());
        doReturn(null).when(lvyClient).addBillByManual(param);
        boolean result = lvyManager.addManualBill(param);
        Assertions.assertFalse(result);

        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).addBillByManual(param);
        result = lvyManager.addManualBill(param);
        Assertions.assertFalse(result);

        doReturn(BizResult.create(true)).when(lvyClient).addBillByManual(param);
        result = lvyManager.addManualBill(param);
        Assertions.assertTrue(result);
    }

    @Test
    public void testAddManualBillReturnStrWithFalse() {
        BillManualParam param = new BillManualParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).addBillByManual(param);
        lvyManager.addManualBillReturnStr(param);
    }

    @Test
    public void testAddManualBillReturnStr() {
        BillManualParam param = new BillManualParam();
        doReturn(BizResult.create("F1000hello")).when(lvyClient).addBillByManual(param);
        String result = lvyManager.addManualBillReturnStr(param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testEditBillWithFail() {
        String billNo = "12321313";
        BillManualParam param = new BillManualParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).editBill(billNo, param);
        lvyManager.editBill(billNo, param);
    }

    @Test
    public void testEditBill() {
        String billNo = "12321313";
        BillManualParam param = new BillManualParam();
        doReturn(BizResult.create("F1000hello")).when(lvyClient).editBill(billNo, param);
        String result = lvyManager.editBill(billNo, param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testDiscardBillWithFail() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).discardBill(param);
        lvyManager.discardBill(param);
    }

    @Test
    public void testDiscardBill() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.create("F1000hello")).when(lvyClient).discardBill(param);
        String result = lvyManager.discardBill(param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testBatchApprovalBillWithFail() {
        BillBatchOperationParam param = new BillBatchOperationParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).approveBillByBatch(param);
        lvyManager.batchApprovalBill(param);
    }

    @Test
    public void testBatchApprovalBill() {
        BillBatchOperationParam param = new BillBatchOperationParam();
        doReturn(BizResult.create(true)).when(lvyClient).approveBillByBatch(param);
        boolean result = lvyManager.batchApprovalBill(param);
        Assertions.assertTrue(result);
    }

    @Test
    public void testApprovalBillWithFail() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).approveBill(param);
        lvyManager.approvalBill(param);
    }

    @Test
    public void testApprovalBill() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.create("F1000hello")).when(lvyClient).approveBill(param);
        String result = lvyManager.approvalBill(param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testConfirmBillWithFail() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).confirmationBill(param);
        lvyManager.confirmBill(param);
    }

    @Test
    public void testConfirmBill() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.create("F1000hello")).when(lvyClient).confirmationBill(param);
        String result = lvyManager.confirmBill(param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testSettleBillAgainWithFail() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).settlementBillAgain(param);
        lvyManager.settleBillAgain(param);
    }

    @Test
    public void testSettleBillAgain() {
        BillOperationParam param = new BillOperationParam();
        doReturn(BizResult.create("F1000hello")).when(lvyClient).settlementBillAgain(param);
        String result = lvyManager.settleBillAgain(param);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testPageBillListWithNull() {
        BillListQuery query = new BillListQuery();
        doReturn(null).when(lvyClient).listBillByPage(query);
        lvyManager.pageBillList(query);
    }

    @Test
    public void testPageBillListWithFail() {
        BillListQuery query = new BillListQuery();
        doReturn(PageResult.error(ErrorCodeEnum.FALLBACK)).when(lvyClient).listBillByPage(query);
        lvyManager.pageBillList(query);
    }

    @Test
    public void testPageBillList() {
        BillListQuery query = new BillListQuery();
        doReturn(PageResult.create(Lists.newArrayList(new BillDTO()))).when(lvyClient)
                .listBillByPage(query);
        List<BillDTO> result = lvyManager.pageBillList(query);
        Assertions.assertTrue(result.size() == 1);
    }

    @Test
    public void testGetBillStatisticsWithNull() {
        BillListQuery query = new BillListQuery();
        doReturn(null).when(lvyClient).getBillStatistics(query);
        lvyManager.getBillStatistics(query);
    }

    @Test
    public void testGetBillStatisticsWithFail() {
        BillListQuery query = new BillListQuery();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).getBillStatistics(query);
        lvyManager.getBillStatistics(query);
    }

    @Test
    public void testGetBillStatisticsWithDataNull() {
        BillListQuery query = new BillListQuery();
        doReturn(BizResult.create(null)).when(lvyClient).getBillStatistics(query);
        Long result = lvyManager.getBillStatistics(query);
        Assertions.assertTrue(result.equals(0L));
    }

    @Test
    public void testGetBillStatistics() {
        BillListQuery query = new BillListQuery();
        doReturn(BizResult.create(100L)).when(lvyClient).getBillStatistics(query);
        Long result = lvyManager.getBillStatistics(query);
        Assertions.assertTrue(result.equals(100L));
    }

    @Test
    public void testGetBillInfoWithNull() {
        String billNo = "231231";
        Integer settleSubjectType = 1;
        Long settleSubjectId = 1L;
        doReturn(null).when(lvyClient).getBillInfo(billNo, settleSubjectType, settleSubjectId);
        lvyManager.getBillInfo(billNo, settleSubjectType, settleSubjectId);
    }

    @Test
    public void testGetBillInfoWithFail() {
        String billNo = "231231";
        Integer settleSubjectType = 1;
        Long settleSubjectId = 1L;
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient)
                .getBillInfo(billNo, settleSubjectType, settleSubjectId);
        lvyManager.getBillInfo(billNo, settleSubjectType, settleSubjectId);
    }

    @Test
    public void testGetBillInfo() {
        String billNo = "231231";
        Integer settleSubjectType = 1;
        Long settleSubjectId = 1L;
        doReturn(BizResult.create(new BillDTO())).when(lvyClient)
                .getBillInfo(billNo, settleSubjectType, settleSubjectId);
        BillDTO billDTO = lvyManager.getBillInfo(billNo, settleSubjectType, settleSubjectId);
        Assertions.assertNotNull(billDTO);
    }

    @Test
    public void testGetBillDetailWithNull() {
        String billNo = "231231";
        Integer settleSubjectType = 1;
        Long settleSubjectId = 1L;
        doReturn(null).when(lvyClient).getBillDetail(billNo, settleSubjectType, settleSubjectId);
        lvyManager.getBillDetail(billNo, settleSubjectType, settleSubjectId);
    }

    @Test
    public void testGetBillDetailWithFail() {
        String billNo = "231231";
        Integer settleSubjectType = 1;
        Long settleSubjectId = 1L;
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient)
                .getBillDetail(billNo, settleSubjectType, settleSubjectId);
        lvyManager.getBillDetail(billNo, settleSubjectType, settleSubjectId);
    }

    @Test
    public void testGetBillDetail() {
        String billNo = "231231";
        Integer settleSubjectType = 1;
        Long settleSubjectId = 1L;
        doReturn(BizResult.create(new BillDetailDTO())).when(lvyClient)
                .getBillDetail(billNo, settleSubjectType, settleSubjectId);
        BillDetailDTO billDTO = lvyManager.getBillDetail(billNo, settleSubjectType, settleSubjectId);
        Assertions.assertNotNull(billDTO);
    }

    @Test
    public void testPageBillItem() {
        BillItemListQuery query = new BillItemListQuery();
        doReturn(PageResult.create(Lists.newArrayList())).when(lvyClient).getBillItems(query);
        PageResult<AccountBillItemDTO> result = lvyManager.pageBillItem(query);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testStatisticsBillItemsWithNull() {
        BillItemListQuery query = new BillItemListQuery();
        doReturn(null).when(lvyClient).getBillItemStatistics(query);
        lvyManager.statisticsBillItems(query);
    }

    @Test
    public void testStatisticsBillItemsWithFail() {
        BillItemListQuery query = new BillItemListQuery();
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).getBillItemStatistics(query);
        lvyManager.statisticsBillItems(query);
    }

    @Test
    public void testStatisticsBillItems() {
        BillItemListQuery query = new BillItemListQuery();
        doReturn(BizResult.create(10000L)).when(lvyClient).getBillItemStatistics(query);
        Long result = lvyManager.statisticsBillItems(query);
        Assertions.assertTrue(result.equals(10000L));
    }

    @Test
    public void testGetBillItemNos() {
        BillItemListQuery query = new BillItemListQuery();
        doReturn(PageResult.create(Lists.newArrayList("hello", "world"))).when(lvyClient)
                .getBillItemNos(query);
        PageResult<String> result = lvyManager.getBillItemNos(query);
        Assertions.assertTrue(result.getData().size() == 2);
    }

    @Test
    public void testGetBillShopsWithNull() {
        String billNo = "123";
        List<Long> shopIdList = Lists.newArrayList(10L, 20L);
        doReturn(null).when(lvyClient).getBillShops(billNo, shopIdList);
        lvyManager.getBillShops(billNo, shopIdList);
    }

    @Test
    public void testGetBillShopsWithFail() {
        String billNo = "123";
        List<Long> shopIdList = Lists.newArrayList(10L, 20L);
        doReturn(BizResult.error("F1000", "hello")).when(lvyClient).getBillShops(billNo, shopIdList);
        lvyManager.getBillShops(billNo, shopIdList);
    }

    @Test
    public void testGetBillShops() {
        String billNo = "123";
        List<Long> shopIdList = Lists.newArrayList(10L, 20L);
        doReturn(BizResult.create(Lists.newArrayList(new AccountBillItemSourceDTO()))).when(lvyClient)
                .getBillShops(billNo, shopIdList);
        List<AccountBillItemSourceDTO> result = lvyManager.getBillShops(billNo, shopIdList);
        Assertions.assertTrue(result.size() == 1);
    }

    //------payApply--------
    @Test
    public void testGetPayApplyByApplyNo() {
        PayApplyDTO result = lvyManager.getPayApplyByApplyNo(null);
        Assertions.assertNull(result);

        result = lvyManager.getPayApplyByApplyNo("");
        Assertions.assertNull(result);

        doReturn(null).when(lvyClient).getPayApplyByApplyNo(Mockito.any(PayApplyDetailQuery.class));
        result = lvyManager.getPayApplyByApplyNo("hello");
        Assertions.assertNull(result);

        doReturn(BizResult.error("F1000", "hello")).when(lvyClient)
                .getPayApplyByApplyNo(Mockito.any(PayApplyDetailQuery.class));
        result = lvyManager.getPayApplyByApplyNo("hello");
        Assertions.assertNull(result);

        doReturn(BizResult.create(new PayApplyDTO())).when(lvyClient)
                .getPayApplyByApplyNo(Mockito.any(PayApplyDetailQuery.class));
        result = lvyManager.getPayApplyByApplyNo("hello");
        Assertions.assertNotNull(result);
    }

    //---------balance--------
    @Test
    public void testPageBalanceList() {
        PageAccountBalanceReq req = new PageAccountBalanceReq();
        req.setAccountId(100L);
        req.setBalanceBizDocType(BalanceBizDocTypeEnum.ORDER_PROFIT_BALANCE.getCode());
        req.setPageNo(1);
        req.setPageSize(10);
        doReturn(PageResult.create(Lists.newArrayList())).when(lvyClient)
                .listBillingDetails(Mockito.any(AccountBalanceNewQuery.class));
        PageResult<AccountBalanceDTO> result = lvyManager.pageBalanceList(req);
        Assertions.assertNotNull(result);
    }

}
