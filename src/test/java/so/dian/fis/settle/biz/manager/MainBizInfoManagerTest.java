package so.dian.fis.settle.biz.manager;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.MapUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import so.dian.fis.settle.biz.manager.remote.AgentManager;
import so.dian.fis.settle.biz.manager.remote.CustomerManager;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.doReturn;

/**
 * MainBizInfoManagerTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class) // 使用 Mockito 扩展来初始化测试环境
//@PrepareForTest({MainBizInfoManager.class})
public class MainBizInfoManagerTest {

    @InjectMocks
    private MainBizInfoManager mainBizInfoManager = new MainBizInfoManager();
    @Mock
    private CustomerManager customerManager;
    @Mock
    private AgentManager agentManager;

    @Test
    public void testListMainBizIdByTypeAndName() {
        List<Long> result = mainBizInfoManager.listMainBizIdByTypeAndName(null, "hello");
        Assertions.assertTrue(CollUtil.isEmpty(result));
        result = mainBizInfoManager.listMainBizIdByTypeAndName(null, null);
        Assertions.assertTrue(CollUtil.isEmpty(result));
        result = mainBizInfoManager.listMainBizIdByTypeAndName(MainBizTypeEnum.NORMAL_MERCHANT.getCode(), null);
        Assertions.assertTrue(CollUtil.isEmpty(result));
        String mainBizName = "hello";
        doReturn(Lists.newArrayList(1L, 2L)).when(customerManager)
                .getMerchantIdsByMerchantName(mainBizName);
        result = mainBizInfoManager.listMainBizIdByTypeAndName(MainBizTypeEnum.NORMAL_MERCHANT.getCode(), mainBizName);
        Assertions.assertTrue(result.size() == 2);
        doReturn(Lists.newArrayList(1L, 2L)).when(agentManager)
                .getIdsByName(mainBizName, MainBizTypeEnum.AGENT.getCode());
        result = mainBizInfoManager.listMainBizIdByTypeAndName(MainBizTypeEnum.AGENT.getCode(), mainBizName);
        Assertions.assertTrue(result.size() == 2);
        doReturn(null).when(agentManager).getIdsByName(mainBizName, MainBizTypeEnum.AGENT.getCode());
        result = mainBizInfoManager.listMainBizIdByTypeAndName(MainBizTypeEnum.AGENT.getCode(), mainBizName);
        Assertions.assertTrue(result.size() == 0);
    }

    @Test
    public void testGetIdNameMap() {
        Map<Long, String> result = mainBizInfoManager.getIdNameMap(null, null);
        Assertions.assertTrue(MapUtils.isEmpty(result));
        result = mainBizInfoManager.getIdNameMap(MainBizTypeEnum.AGENT.getCode(), null);
        Assertions.assertTrue(MapUtils.isEmpty(result));
        result = mainBizInfoManager.getIdNameMap(null, Lists.newArrayList(1L,2L));
        Assertions.assertTrue(MapUtils.isEmpty(result));
        result = mainBizInfoManager.getIdNameMap(MainBizTypeEnum.IOT_MERCHANT.getCode(), Lists.newArrayList(10L,20L));
        Assertions.assertTrue(MapUtils.isEmpty(result));
        List<Long> mainBizIdList = Lists.newArrayList(10L, 20L);
        doReturn(ImmutableMap.of(10L,"hello",20L,"world")).when(customerManager).getIdNameMap(mainBizIdList);
        result = mainBizInfoManager.getIdNameMap(MainBizTypeEnum.NORMAL_MERCHANT.getCode(), mainBizIdList);
        Assertions.assertTrue(result.size() == 2);
        doReturn(ImmutableMap.of(10L,"hello",20L,"world")).when(agentManager).getIdNameMap(mainBizIdList);
        result = mainBizInfoManager.getIdNameMap(MainBizTypeEnum.AGENT.getCode(), mainBizIdList);
        Assertions.assertTrue(result.size() == 2);
    }

    @Test
    public void testGetNameByMainBiz() {
        String result = mainBizInfoManager.getNameByMainBiz(null, null);
        Assertions.assertTrue(result == null);
        result = mainBizInfoManager.getNameByMainBiz(MainBizTypeEnum.NORMAL_MERCHANT.getCode(), null);
        Assertions.assertTrue(result == null);
        result = mainBizInfoManager.getNameByMainBiz(null, 10L);
        Assertions.assertTrue(result == null);
        Long mainBizId = 100L;
        doReturn("hello").when(customerManager).getNameById(mainBizId);
        result = mainBizInfoManager.getNameByMainBiz(MainBizTypeEnum.NORMAL_MERCHANT.getCode(), mainBizId);
        Assertions.assertTrue(result.equals("hello"));
        doReturn("world").when(agentManager).getNameById(mainBizId);
        result = mainBizInfoManager.getNameByMainBiz(MainBizTypeEnum.AGENT.getCode(), mainBizId);
        Assertions.assertTrue(result.equals("world"));
        result = mainBizInfoManager.getNameByMainBiz(MainBizTypeEnum.IOT_MERCHANT.getCode(), mainBizId);
        Assertions.assertTrue(result == null);
    }

}
