<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>so.dian.fis</groupId>
  <artifactId>settle-client</artifactId>
  <version>1.0.3-RELEASE</version>
  <packaging>jar</packaging>

  <name>${project.artifactId}</name>
  <description>settle-sdk</description>

  <dependencies>
    <!--
    只引用必要的依赖。禁止引用其他client包，以防循环依赖。
    常量包固定版本（1.0.0-SNAPSHOT）
    其他依赖加上“<scope>provided</scope>”，由引用方自行指定版本，防止依赖覆盖
     -->
    <!-- 公用枚举常量包 -->
    <dependency>
      <groupId>so.dian.kunlun</groupId>
      <artifactId>kunlun</artifactId>
      <version>2022.01.07-RELEASE</version>
    </dependency>
    <!-- 工具包 -->
    <dependency>
      <groupId>so.dian.himalaya</groupId>
      <artifactId>himalaya</artifactId>
      <version>1.1.3-RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
      <version>4.3.25.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.5.20</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- 要将源码上传到maven仓库 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <attach>true</attach>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <distributionManagement>
    <snapshotRepository>
      <id>snapshots</id>
      <name>User Porject Snapshot</name>
      <url>http://maven.dian.so/nexus/content/repositories/snapshots/</url>
      <uniqueVersion>true</uniqueVersion>
    </snapshotRepository>
    <repository>
      <id>releases</id>
      <name>User Porject Release</name>
      <url>http://maven.dian.so/nexus/content/repositories/releases/</url>
    </repository>
  </distributionManagement>
</project>
