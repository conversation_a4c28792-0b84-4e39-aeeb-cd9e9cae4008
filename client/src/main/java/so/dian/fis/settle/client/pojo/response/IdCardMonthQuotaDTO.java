package so.dian.fis.settle.client.pojo.response;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IdCardStaticsAmountDTO
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class IdCardMonthQuotaDTO implements Serializable {

    private Long monthQuota;

    private String idCard;

    public static IdCardMonthQuotaDTO zero(String idCard) {
        return of(0L, idCard);
    }


}
