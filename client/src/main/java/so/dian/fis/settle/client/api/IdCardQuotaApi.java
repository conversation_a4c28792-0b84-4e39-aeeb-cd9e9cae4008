package so.dian.fis.settle.client.api;

import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.fis.settle.client.pojo.request.IdCardMonthQuotaQueryDTO;
import so.dian.fis.settle.client.pojo.response.IdCardMonthQuotaDTO;
import so.dian.himalaya.common.entity.BizResult;

/**
 * IdCardQuotaApi
 *
 * <AUTHOR>
 * @desc
 * @date 2022/11/29 16:21
 */
public interface IdCardQuotaApi {


    @PostMapping(value = "/idCard/month/quota")
    BizResult<IdCardMonthQuotaDTO> monthQuota(@Valid @RequestBody IdCardMonthQuotaQueryDTO query);

}
