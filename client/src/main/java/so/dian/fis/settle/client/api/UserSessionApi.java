package so.dian.fis.settle.client.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.fis.settle.client.pojo.response.UserSessionDTO;
import so.dian.himalaya.common.entity.BizResult;

/**
 * UserSessionApi
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/20 15:29
 */
public interface UserSessionApi {

    @GetMapping("/user/session/get")
    BizResult<UserSessionDTO> get(@RequestParam("userId") Long userId);

}
