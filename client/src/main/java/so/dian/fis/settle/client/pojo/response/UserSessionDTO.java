package so.dian.fis.settle.client.pojo.response;

import java.io.Serializable;
import lombok.Data;

/**
 * UserSessionDTO
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/20 15:29
 */
@Data
public class UserSessionDTO implements Serializable {

    private Long userId;

    private String userRole;

    private String email;

    private String nickName;

    private Long settleSubjectId;

    private Integer settleSubjectType;

    private Integer agentSubType;

}
