@Library('jenkins-pipeline-library@java') _
pipeline {
    agent {
        kubernetes {
            yamlFile 'kubernetesPod.yaml'
        }
    }
    options { timeout(time: 30, unit: 'MINUTES') }
    environment {
        REGISTRY = credentials('d4dec53c-d6e1-460c-a629-4151ad2cd8f0')
    }
    stages {
        stage('check env') {
            steps {
                checkEnv()
            }
        }
        stage('build image') {
            when {
                expression { return MASTER_BRANCH == 'false' }
            }
            steps {
                container('java') {
                    sh "mvn package jib:build -U -DskipTests -Djib.to.auth.username=${REGISTRY_USR} -Djib.to.auth.password=${REGISTRY_PSW} -Djib.to.image=${imageAddr} -Dmaven.repo.local=/.m2/${APP_NAME}"
                }
            }
        }
        stage('upload image') {
            steps {
                uploadImage()
            }
        }
        stage('parallel') {
            parallel {
                stage('deploy') {
                    when {
                        expression { return MASTER_BRANCH == 'false' }
                    }
                    // 部署
                    steps {
                        deploy()
                    }
                }
                stage('snyk scan') {
                    // 安全扫描
                    steps {
                        snykScan()
                    }
                }
                stage('snapshot') {
                    // 检查工程是否使用了快照版本的jar
                    steps {
                        snapshot()
                    }
                }
                stage('unit and sonar') {
                    stages {
                        stage('unit') {
                            // 执行单元测试，不忽略失败的单测，必须保障通过单测
                            steps {
                                unit()
                            }
                        }
                        stage('sonar') {
                            // 执行sonar静态代码扫描
                            steps {
                                sonar()
                            }
                        }
                    }
                }
            }
        }
    }
}
