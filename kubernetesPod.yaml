apiVersion: "v1"
kind: "Pod"
metadata:
  annotations:
    cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
  labels:
    jenkins: "slave"
spec:
  containers:
  - args:
    - "while true; do sleep infinity || exit 0; done"
    command:
    - "/bin/sh"
    - "-c"
    image: "quay.xiaodiankeji.net/openjdk/openjdk-21-runtime-skywalking@sha256:ccb808d7b43ff2f411e329ef9d746d20378886968e86e7035ebc1661f283c67c"
    name: "java"
    volumeMounts:
    - mountPath: "/app/.m2/settings.xml"
      name: "volume-0"
      readOnly: false
      subPath: "settings.xml"
    - mountPath: "/.m2"
      name: "volume-1"
      readOnly: false
    workingDir: "/app"
    env:
      - name: "HOME"
        value: "/app"
  - name: "jnlp"
    workingDir: "/app"
    image: "registry.redhat.io/openshift4/ose-jenkins-agent-base:v4.10"
  nodeSelector:
    beta.kubernetes.io/os: "linux"
    node-role.dian.so/worker: ""
  restartPolicy: "Never"
  securityContext:
    runAsGroup: 1001
    runAsUser: 1001
    fsGroup: 1001
  serviceAccountName: "jenkins"
  terminationGracePeriodSeconds: 60
  volumes:
  - name: "volume-0"
    configMap:
      name: "maven-settings"
      optional: false
  - name: "volume-1"
    persistentVolumeClaim:
      claimName: "jenkins-home"