## 标准工程模板

####项目结构
```
.
├── README.md
├── client    提供给他人使用的SDK
│   ├── pom.xml
│   └── src
│       └── main
│           └── java
│               └── so
│                   └── dian
│                       └── demo
│                           └── client
│                               ├── api
│                               │   └── StaffApi.java
│                               └── pojo
│                                   ├── enums
│                                   │   └── DemoEnum.java
│                                   ├── request
│                                   │   ├── StaffAddReq.java
│                                   │   └── StaffEditReq.java
│                                   └── response
│                                       └── StaffRsp.java
├── pom.xml
└── src
    ├── main
    │   ├── java
    │   │   └── so
    │   │       └── dian
    │   │           └── demo
    │   │               ├── DemoApplication.java
    │   │               ├── biz
    │   │               │   ├── handler   manager和service层的中间层，业务复杂是使用。简单业务可以不要
    │   │               │   │   └── README.md
    │   │               │   ├── manager   数据层
    │   │               │   │   └── StaffManager.java
    │   │               │   └── service   业务层
    │   │               │       └── StaffService.java
    │   │               ├── common    项目公共包
    │   │               │   ├── constant  常量包
    │   │               │   │   └── DemoConstants.java
    │   │               │   ├── enums   枚举包
    │   │               │   │   ├── CacheEnum.java
    │   │               │   │   ├── LockEnum.java
    │   │               │   │   └── error 自定义异常
    │   │               │   │       └── BizErrorCodeEnum.java
    │   │               │   ├── exception 自定义异常
    │   │               │   │   └── README.md
    │   │               │   ├── pojo    实体类
    │   │               │   │   ├── bo
    │   │               │   │   │   └── StaffBO.java
    │   │               │   │   ├── converter
    │   │               │   │   │   └── StaffConverter.java
    │   │               │   │   ├── domain
    │   │               │   │   │   └── StaffDO.java
    │   │               │   │   ├── properties
    │   │               │   │   │   └── DemoProperties.java
    │   │               │   │   └── validator
    │   │               │   │       └── StaffValidator.java
    │   │               │   └── util  项目工具类
    │   │               │       ├── README.md
    │   │               │       ├── YmlUtils.java
    │   │               │       └── biz
    │   │               │           └── README.md
    │   │               ├── config  配置包
    │   │               │   ├── DataSourceConfiguration.java
    │   │               │   ├── FeignConfiguration.java
    │   │               │   ├── RibbonConfiguration.java
    │   │               │   └── ons
    │   │               │       ├── OnsConsumerConfiguration.java
    │   │               │       ├── OnsProducerConfiguration.java
    │   │               │       └── OnsPropereties.java
    │   │               ├── controller  控制器
    │   │               │   ├── StaffController.java
    │   │               │   └── TestController.java
    │   │               ├── job 定时任务
    │   │               │   └── README.md
    │   │               ├── mapper  MyBatis
    │   │               │   └── StaffMapper.java
    │   │               ├── mq  消息
    │   │               │   ├── consumer
    │   │               │   │   └── DemoListener.java
    │   │               │   └── producer
    │   │               │       └── README.md
    │   │               └── remote 远程接口（第三方）
    │   │                   └── PolarisClient.java
    │   └── resources
    │       ├── application-local.yml
    │       ├── application.properties
    │       ├── application.yml
    │       ├── bootstrap.yml
    │       ├── data.sql
    │       ├── logback-spring.xml
    │       ├── mybatis
    │       │   ├── mapper
    │       │   │   └── StaffMapper.xml
    │       │   └── mybatis-config.xml
    │       └── schema.sql
    └── test
        └── java
            └── so
                └── dian
                    └── demo
                        └── DemoApplicationTests.java
```