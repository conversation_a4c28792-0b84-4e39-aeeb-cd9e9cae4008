<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.4</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <groupId>so.dian.fis</groupId>
  <artifactId>fis-settle-platform</artifactId>
  <version>0.0.1-RELEASE</version>
  <packaging>jar</packaging>

  <name>${project.artifactId}</name>
  <description>结算聚合服务</description>


  <properties>
    <java.version>21</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <jacoco-maven.version>0.8.12</jacoco-maven.version>

    <fastjson.version>1.2.83</fastjson.version>
    <lombok.version>1.18.30</lombok.version>
    <dian.logger.version>2.0.5</dian.logger.version>
    <spring-cloud.version>2023.0.1</spring-cloud.version>
    <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
    <nacos-client.version>1.4.7</nacos-client.version>

    <opentelemetry-instrumentation.version>2.6.0</opentelemetry-instrumentation.version>
    <opentelemetry-logback-mdc.version>2.6.0-alpha</opentelemetry-logback-mdc.version>
    <datasource-micrometer.version>1.0.3</datasource-micrometer.version>

    <!-- sonar unit test -->
    <sonar.inclusions>
      **/biz/service/**,
      **/biz/manager/**,
      **/biz/manager/remote/**,
      **/biz/handler/**,
      **/common/util/biz/**
    </sonar.inclusions>
    <sonar.exclusions>
      **/biz/manager/DingTalkManager*,
      **/biz/service/CurrentUserService*
    </sonar.exclusions>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-instrumentation-bom</artifactId>
        <version>${opentelemetry-instrumentation.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!--web-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <!-- opentelemetry -->
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-core-micrometer</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-micrometer</artifactId>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-otlp</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-tracing-bridge-otel</artifactId>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-exporter-otlp</artifactId>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry.instrumentation</groupId>
      <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
      <version>${opentelemetry-logback-mdc.version}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.ttddyy.observation</groupId>
      <artifactId>datasource-micrometer-spring-boot</artifactId>
      <version>${datasource-micrometer.version}</version>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>

      <!-- nacos -->
      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
          <version>${fastjson.version}</version>
      </dependency>
      <dependency>
          <groupId>com.alibaba.cloud</groupId>
          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
          <version>${spring-cloud-alibaba.version}</version>
          <exclusions>
              <exclusion>
                  <groupId>com.alibaba.nacos</groupId>
                  <artifactId>nacos-client</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <groupId>com.alibaba.nacos</groupId>
          <artifactId>nacos-client</artifactId>
          <version>${nacos-client.version}</version>
          <exclusions>
              <exclusion>
                  <groupId>commons-io</groupId>
                  <artifactId>commons-io</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>3.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>6.1.5.Final</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>3.38.1</version>
    </dependency>

    <dependency>
      <groupId>so.dian.common</groupId>
      <artifactId>dian-logger</artifactId>
      <version>${dian.logger.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>sunreaver-api</artifactId>
      <version>1.2.4-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-openfeign-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-aop</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-web</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- client-->
    <dependency>
      <groupId>so.dian.kunlun</groupId>
      <artifactId>kunlun</artifactId>
      <version>2022.01.07-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian.himalaya</groupId>
      <artifactId>himalaya-spring-boot-starter</artifactId>
      <version>1.1.5-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>xxl-job-core</artifactId>
          <groupId>so.dian.xxl.job</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>contract-explain-client</artifactId>
      <version>1.2.8-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian.yandang</groupId>
      <artifactId>yandang-client</artifactId>
      <version>1.6.5-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian.fis</groupId>
      <artifactId>settle-client</artifactId>
      <version>1.0.3-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>lvy-client</artifactId>
      <version>1.1.4-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>pap-client</artifactId>
      <version>1.2.8-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>wp-client</artifactId>
      <version>1.2.4-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>spring-cloud-starter-feign</artifactId>
          <groupId>org.springframework.cloud</groupId>
        </exclusion>
        <exclusion>
          <artifactId>feign-core</artifactId>
          <groupId>io.github.openfeign</groupId>
        </exclusion>
        <exclusion>
          <artifactId>feign-hystrix</artifactId>
          <groupId>io.github.openfeign</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.wutai</groupId>
      <artifactId>wutai-client</artifactId>
      <version>1.1.4-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian.hr</groupId>
      <artifactId>hr-service-api</artifactId>
      <version>1.2.3-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>so.dian.agent</groupId>
      <artifactId>agent-service-api</artifactId>
      <version>2.7.2-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>customer-api</artifactId>
      <version>1.2.5-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.mdm</groupId>
      <artifactId>mdm-client</artifactId>
      <version>1.0.2-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>so.dian</groupId>
          <artifactId>kunlun</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>shop-client</artifactId>
      <version>1.8.8</version>
      <exclusions>
        <exclusion>
          <groupId>so.dian</groupId>
          <artifactId>commons-eden</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.shop</groupId>
      <artifactId>shopcenter-api</artifactId>
      <version>3.3.1-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>dove-client</artifactId>
      <version>0.0.12.RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>oss-springboot15x-starter</artifactId>
      <version>2021.10.21</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>rick-merchant-client</artifactId>
      <version>1.2.6</version>
    </dependency>
    <dependency>
      <groupId>so.dian.hera</groupId>
      <artifactId>hera-client</artifactId>
      <version>2.9.34-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>zuul-client</artifactId>
      <version>1.0.1-RELEASE</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${jacoco-maven.version}</version>
        <configuration>
          <!--结果数据存放,配合execution:jacoco-site-->
          <destFile>target/coverage-reports/jacoco-unit.exec</destFile>
          <dataFile>target/coverage-reports/jacoco-unit.exec</dataFile>
          <!-- includes里指定需要扫描的包/类；exclude里指定不需要扫描的包/类-->
          <includes>
            <include>**/biz/service/**</include>
            <include>**/biz/manager/**</include>
            <include>**/biz/manager/remote/**</include>
            <include>**/biz/handler/**</include>
            <include>**/common/util/biz/**</include>
          </includes>
          <excludes>
            <exclude>**/biz/manager/DingTalkManager*</exclude>
            <exclude>**/biz/service/CurrentUserService*</exclude>
          </excludes>
          <!-- rules里面指定覆盖规则 -->
          <rules>
            <rule implementation="org.jacoco.maven.RuleConfiguration">
              <element>BUNDLE</element>
              <limits>　　
                <!-- 指定方法覆盖到50% -->
                <limit implementation="org.jacoco.report.check.Limit">
                  <counter>METHOD</counter>
                  <value>COVEREDRATIO</value>
                  <minimum>0</minimum>
                </limit>
                <!-- 指定分支覆盖到50% -->
                <limit implementation="org.jacoco.report.check.Limit">
                  <counter>BRANCH</counter>
                  <value>COVEREDRATIO</value>
                  <minimum>0</minimum>
                </limit>
                <!-- 指定类覆盖到100%，遗失类最多个数 -->
                <limit implementation="org.jacoco.report.check.Limit">
                  <counter>CLASS</counter>
                  <value>MISSEDCOUNT</value>
                  <maximum>50</maximum>
                </limit>
              </limits>
            </rule>
          </rules>
        </configuration>
        <!-- 配置插件目标 -->
        <executions>
          <execution>
            <id>prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.google.cloud.tools</groupId>
        <artifactId>jib-maven-plugin</artifactId>
        <version>3.4.1</version>
        <configuration>
          <containerizingMode>packaged</containerizingMode>
          <container>
            <entrypoint>/opt/jboss/container/java/run/run-java.sh</entrypoint>
          </container>
          <extraDirectories>
            <paths>
              <path>
                <from>target/</from>
                <includes>${project.build.finalName}.jar</includes>
                <into>/deployments</into>
              </path>
            </paths>
          </extraDirectories>
          <pluginExtensions>
            <pluginExtension>
              <implementation>
                com.google.cloud.tools.jib.maven.extension.layerfilter.JibLayerFilterExtension</implementation>
              <configuration
                implementation="com.google.cloud.tools.jib.maven.extension.layerfilter.Configuration">
                <filters>
                  <filter>
                    <!-- exclude all jib layers, which is basically anything in /app -->
                    <glob>/app/**</glob>
                  </filter>
                  <filter>
                    <!-- this is our fat jar, this should be kept by adding it into its own layer -->
                    <glob>/deployments/${project.build.finalName}.jar</glob>
                    <toLayer>jib-custom-fatJar</toLayer>
                  </filter>
                </filters>
              </configuration>
            </pluginExtension>
          </pluginExtensions>
          <from>
            <image>
              quay.xiaodiankeji.net/openjdk/openjdk-21-runtime-skywalking@sha256:ccb808d7b43ff2f411e329ef9d746d20378886968e86e7035ebc1661f283c67c
            </image>
            <platforms>
              <platform>
                <architecture>arm64</architecture>
                <os>linux</os>
              </platform>
              <platform>
                <architecture>amd64</architecture>
                <os>linux</os>
              </platform>
            </platforms>
          </from>
          <to>
            <image>quay.xiaodiankeji.net/dian-dev/${project.build.finalName}</image>
            <auth>
              <username>${env.REGISTRY_USR}</username>
              <password>${env.REGISTRY_PSW}</password>
            </auth>
          </to>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.cloud.tools</groupId>
            <artifactId>jib-layer-filter-extension-maven</artifactId>
            <version>0.3.0</version>
          </dependency>
        </dependencies>
    </plugin>
    <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <version>2.43.0</version>
        <configuration>
          <ratchetFrom>origin/main</ratchetFrom>
          <formats>
            <format>
              <includes>
                <include>*.md</include>
                <include>.gitignore</include>
              </includes>
              <trimTrailingWhitespace></trimTrailingWhitespace>
              <endWithNewline></endWithNewline>
              <indent>
                <tabs>true</tabs>
                <spacesPerTab>4</spacesPerTab>
              </indent>
            </format>
          </formats>
          <java>
            <includes>
              <include>src/main/java/**/*.java</include>
              <include>src/test/java/**/*.java</include>
            </includes>
            <googleJavaFormat>
              <style>GOOGLE</style>
              <reflowLongStrings>true</reflowLongStrings>
            </googleJavaFormat>
            <importOrder>
              <wildcardsLast>false</wildcardsLast>
              <order>java|javax|jakarta,org.springframework,org,com,so.dian,,\#so.dian,\#</order>
              <semanticSort>false</semanticSort>
            </importOrder>
            <removeUnusedImports></removeUnusedImports>
            <formatAnnotations></formatAnnotations>
          </java>
          <pom>
            <includes>
              <include>pom.xml</include>
            </includes>
            <sortPom></sortPom>
          </pom>
        </configuration>
    </plugin>
    <plugin>
      <groupId>org.apache.maven.plugins</groupId>
      <artifactId>maven-pmd-plugin</artifactId>
      <version>3.21.2</version>
    </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.5.0</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.21.2</version>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <!-- don't run aggregate in child modules -->
            <reports>
              <report>aggregate-pmd</report>
              <report>aggregate-cpd</report>
            </reports>
            <inherited>false</inherited>
          </reportSet>
          <reportSet>
            <id>default</id>
            <reports>
              <report>pmd</report>
              <report>cpd</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>
</project>
